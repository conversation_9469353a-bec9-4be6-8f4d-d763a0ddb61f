{"version": 3, "sources": ["../../.pnpm/@vue-flow+node-resizer@1.4._bd182e41db68a69f48d13820a0fd3d90/node_modules/@vue-flow/node-resizer/dist/vue-flow-node-resizer.mjs"], "sourcesContent": ["import { defineComponent, ref, toRef, watchEffect, openBlock, createElementBlock, normalizeClass, normalizeStyle, renderSlot, inject, computed, watch, Fragment, renderList, createVNode, unref, createCommentVNode } from \"vue\";\nimport { useVueFlow, useGetPointerPosition, clamp, NodeIdInjection } from \"@vue-flow/core\";\nconst style = \"\";\nvar xhtml = \"http://www.w3.org/1999/xhtml\";\nconst namespaces = {\n  svg: \"http://www.w3.org/2000/svg\",\n  xhtml,\n  xlink: \"http://www.w3.org/1999/xlink\",\n  xml: \"http://www.w3.org/XML/1998/namespace\",\n  xmlns: \"http://www.w3.org/2000/xmlns/\"\n};\nfunction namespace(name) {\n  var prefix = name += \"\", i = prefix.indexOf(\":\");\n  if (i >= 0 && (prefix = name.slice(0, i)) !== \"xmlns\")\n    name = name.slice(i + 1);\n  return namespaces.hasOwnProperty(prefix) ? { space: namespaces[prefix], local: name } : name;\n}\nfunction creatorInherit(name) {\n  return function() {\n    var document2 = this.ownerDocument, uri = this.namespaceURI;\n    return uri === xhtml && document2.documentElement.namespaceURI === xhtml ? document2.createElement(name) : document2.createElementNS(uri, name);\n  };\n}\nfunction creatorFixed(fullname) {\n  return function() {\n    return this.ownerDocument.createElementNS(fullname.space, fullname.local);\n  };\n}\nfunction creator(name) {\n  var fullname = namespace(name);\n  return (fullname.local ? creatorFixed : creatorInherit)(fullname);\n}\nfunction none() {\n}\nfunction selector(selector2) {\n  return selector2 == null ? none : function() {\n    return this.querySelector(selector2);\n  };\n}\nfunction selection_select(select2) {\n  if (typeof select2 !== \"function\")\n    select2 = selector(select2);\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = new Array(n), node, subnode, i = 0; i < n; ++i) {\n      if ((node = group[i]) && (subnode = select2.call(node, node.__data__, i, group))) {\n        if (\"__data__\" in node)\n          subnode.__data__ = node.__data__;\n        subgroup[i] = subnode;\n      }\n    }\n  }\n  return new Selection(subgroups, this._parents);\n}\nfunction array(x) {\n  return x == null ? [] : Array.isArray(x) ? x : Array.from(x);\n}\nfunction empty() {\n  return [];\n}\nfunction selectorAll(selector2) {\n  return selector2 == null ? empty : function() {\n    return this.querySelectorAll(selector2);\n  };\n}\nfunction arrayAll(select2) {\n  return function() {\n    return array(select2.apply(this, arguments));\n  };\n}\nfunction selection_selectAll(select2) {\n  if (typeof select2 === \"function\")\n    select2 = arrayAll(select2);\n  else\n    select2 = selectorAll(select2);\n  for (var groups = this._groups, m = groups.length, subgroups = [], parents = [], j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        subgroups.push(select2.call(node, node.__data__, i, group));\n        parents.push(node);\n      }\n    }\n  }\n  return new Selection(subgroups, parents);\n}\nfunction matcher(selector2) {\n  return function() {\n    return this.matches(selector2);\n  };\n}\nfunction childMatcher(selector2) {\n  return function(node) {\n    return node.matches(selector2);\n  };\n}\nvar find = Array.prototype.find;\nfunction childFind(match) {\n  return function() {\n    return find.call(this.children, match);\n  };\n}\nfunction childFirst() {\n  return this.firstElementChild;\n}\nfunction selection_selectChild(match) {\n  return this.select(match == null ? childFirst : childFind(typeof match === \"function\" ? match : childMatcher(match)));\n}\nvar filter = Array.prototype.filter;\nfunction children() {\n  return Array.from(this.children);\n}\nfunction childrenFilter(match) {\n  return function() {\n    return filter.call(this.children, match);\n  };\n}\nfunction selection_selectChildren(match) {\n  return this.selectAll(match == null ? children : childrenFilter(typeof match === \"function\" ? match : childMatcher(match)));\n}\nfunction selection_filter(match) {\n  if (typeof match !== \"function\")\n    match = matcher(match);\n  for (var groups = this._groups, m = groups.length, subgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, subgroup = subgroups[j] = [], node, i = 0; i < n; ++i) {\n      if ((node = group[i]) && match.call(node, node.__data__, i, group)) {\n        subgroup.push(node);\n      }\n    }\n  }\n  return new Selection(subgroups, this._parents);\n}\nfunction sparse(update) {\n  return new Array(update.length);\n}\nfunction selection_enter() {\n  return new Selection(this._enter || this._groups.map(sparse), this._parents);\n}\nfunction EnterNode(parent, datum2) {\n  this.ownerDocument = parent.ownerDocument;\n  this.namespaceURI = parent.namespaceURI;\n  this._next = null;\n  this._parent = parent;\n  this.__data__ = datum2;\n}\nEnterNode.prototype = {\n  constructor: EnterNode,\n  appendChild: function(child) {\n    return this._parent.insertBefore(child, this._next);\n  },\n  insertBefore: function(child, next) {\n    return this._parent.insertBefore(child, next);\n  },\n  querySelector: function(selector2) {\n    return this._parent.querySelector(selector2);\n  },\n  querySelectorAll: function(selector2) {\n    return this._parent.querySelectorAll(selector2);\n  }\n};\nfunction constant$1(x) {\n  return function() {\n    return x;\n  };\n}\nfunction bindIndex(parent, group, enter, update, exit, data) {\n  var i = 0, node, groupLength = group.length, dataLength = data.length;\n  for (; i < dataLength; ++i) {\n    if (node = group[i]) {\n      node.__data__ = data[i];\n      update[i] = node;\n    } else {\n      enter[i] = new EnterNode(parent, data[i]);\n    }\n  }\n  for (; i < groupLength; ++i) {\n    if (node = group[i]) {\n      exit[i] = node;\n    }\n  }\n}\nfunction bindKey(parent, group, enter, update, exit, data, key) {\n  var i, node, nodeByKeyValue = /* @__PURE__ */ new Map(), groupLength = group.length, dataLength = data.length, keyValues = new Array(groupLength), keyValue;\n  for (i = 0; i < groupLength; ++i) {\n    if (node = group[i]) {\n      keyValues[i] = keyValue = key.call(node, node.__data__, i, group) + \"\";\n      if (nodeByKeyValue.has(keyValue)) {\n        exit[i] = node;\n      } else {\n        nodeByKeyValue.set(keyValue, node);\n      }\n    }\n  }\n  for (i = 0; i < dataLength; ++i) {\n    keyValue = key.call(parent, data[i], i, data) + \"\";\n    if (node = nodeByKeyValue.get(keyValue)) {\n      update[i] = node;\n      node.__data__ = data[i];\n      nodeByKeyValue.delete(keyValue);\n    } else {\n      enter[i] = new EnterNode(parent, data[i]);\n    }\n  }\n  for (i = 0; i < groupLength; ++i) {\n    if ((node = group[i]) && nodeByKeyValue.get(keyValues[i]) === node) {\n      exit[i] = node;\n    }\n  }\n}\nfunction datum(node) {\n  return node.__data__;\n}\nfunction selection_data(value, key) {\n  if (!arguments.length)\n    return Array.from(this, datum);\n  var bind = key ? bindKey : bindIndex, parents = this._parents, groups = this._groups;\n  if (typeof value !== \"function\")\n    value = constant$1(value);\n  for (var m = groups.length, update = new Array(m), enter = new Array(m), exit = new Array(m), j = 0; j < m; ++j) {\n    var parent = parents[j], group = groups[j], groupLength = group.length, data = arraylike(value.call(parent, parent && parent.__data__, j, parents)), dataLength = data.length, enterGroup = enter[j] = new Array(dataLength), updateGroup = update[j] = new Array(dataLength), exitGroup = exit[j] = new Array(groupLength);\n    bind(parent, group, enterGroup, updateGroup, exitGroup, data, key);\n    for (var i0 = 0, i1 = 0, previous, next; i0 < dataLength; ++i0) {\n      if (previous = enterGroup[i0]) {\n        if (i0 >= i1)\n          i1 = i0 + 1;\n        while (!(next = updateGroup[i1]) && ++i1 < dataLength)\n          ;\n        previous._next = next || null;\n      }\n    }\n  }\n  update = new Selection(update, parents);\n  update._enter = enter;\n  update._exit = exit;\n  return update;\n}\nfunction arraylike(data) {\n  return typeof data === \"object\" && \"length\" in data ? data : Array.from(data);\n}\nfunction selection_exit() {\n  return new Selection(this._exit || this._groups.map(sparse), this._parents);\n}\nfunction selection_join(onenter, onupdate, onexit) {\n  var enter = this.enter(), update = this, exit = this.exit();\n  if (typeof onenter === \"function\") {\n    enter = onenter(enter);\n    if (enter)\n      enter = enter.selection();\n  } else {\n    enter = enter.append(onenter + \"\");\n  }\n  if (onupdate != null) {\n    update = onupdate(update);\n    if (update)\n      update = update.selection();\n  }\n  if (onexit == null)\n    exit.remove();\n  else\n    onexit(exit);\n  return enter && update ? enter.merge(update).order() : update;\n}\nfunction selection_merge(context) {\n  var selection = context.selection ? context.selection() : context;\n  for (var groups0 = this._groups, groups1 = selection._groups, m0 = groups0.length, m1 = groups1.length, m = Math.min(m0, m1), merges = new Array(m0), j = 0; j < m; ++j) {\n    for (var group0 = groups0[j], group1 = groups1[j], n = group0.length, merge = merges[j] = new Array(n), node, i = 0; i < n; ++i) {\n      if (node = group0[i] || group1[i]) {\n        merge[i] = node;\n      }\n    }\n  }\n  for (; j < m0; ++j) {\n    merges[j] = groups0[j];\n  }\n  return new Selection(merges, this._parents);\n}\nfunction selection_order() {\n  for (var groups = this._groups, j = -1, m = groups.length; ++j < m; ) {\n    for (var group = groups[j], i = group.length - 1, next = group[i], node; --i >= 0; ) {\n      if (node = group[i]) {\n        if (next && node.compareDocumentPosition(next) ^ 4)\n          next.parentNode.insertBefore(node, next);\n        next = node;\n      }\n    }\n  }\n  return this;\n}\nfunction selection_sort(compare) {\n  if (!compare)\n    compare = ascending;\n  function compareNode(a, b) {\n    return a && b ? compare(a.__data__, b.__data__) : !a - !b;\n  }\n  for (var groups = this._groups, m = groups.length, sortgroups = new Array(m), j = 0; j < m; ++j) {\n    for (var group = groups[j], n = group.length, sortgroup = sortgroups[j] = new Array(n), node, i = 0; i < n; ++i) {\n      if (node = group[i]) {\n        sortgroup[i] = node;\n      }\n    }\n    sortgroup.sort(compareNode);\n  }\n  return new Selection(sortgroups, this._parents).order();\n}\nfunction ascending(a, b) {\n  return a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}\nfunction selection_call() {\n  var callback = arguments[0];\n  arguments[0] = this;\n  callback.apply(null, arguments);\n  return this;\n}\nfunction selection_nodes() {\n  return Array.from(this);\n}\nfunction selection_node() {\n  for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {\n    for (var group = groups[j], i = 0, n = group.length; i < n; ++i) {\n      var node = group[i];\n      if (node)\n        return node;\n    }\n  }\n  return null;\n}\nfunction selection_size() {\n  let size = 0;\n  for (const node of this)\n    ++size;\n  return size;\n}\nfunction selection_empty() {\n  return !this.node();\n}\nfunction selection_each(callback) {\n  for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {\n    for (var group = groups[j], i = 0, n = group.length, node; i < n; ++i) {\n      if (node = group[i])\n        callback.call(node, node.__data__, i, group);\n    }\n  }\n  return this;\n}\nfunction attrRemove(name) {\n  return function() {\n    this.removeAttribute(name);\n  };\n}\nfunction attrRemoveNS(fullname) {\n  return function() {\n    this.removeAttributeNS(fullname.space, fullname.local);\n  };\n}\nfunction attrConstant(name, value) {\n  return function() {\n    this.setAttribute(name, value);\n  };\n}\nfunction attrConstantNS(fullname, value) {\n  return function() {\n    this.setAttributeNS(fullname.space, fullname.local, value);\n  };\n}\nfunction attrFunction(name, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null)\n      this.removeAttribute(name);\n    else\n      this.setAttribute(name, v);\n  };\n}\nfunction attrFunctionNS(fullname, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null)\n      this.removeAttributeNS(fullname.space, fullname.local);\n    else\n      this.setAttributeNS(fullname.space, fullname.local, v);\n  };\n}\nfunction selection_attr(name, value) {\n  var fullname = namespace(name);\n  if (arguments.length < 2) {\n    var node = this.node();\n    return fullname.local ? node.getAttributeNS(fullname.space, fullname.local) : node.getAttribute(fullname);\n  }\n  return this.each((value == null ? fullname.local ? attrRemoveNS : attrRemove : typeof value === \"function\" ? fullname.local ? attrFunctionNS : attrFunction : fullname.local ? attrConstantNS : attrConstant)(fullname, value));\n}\nfunction defaultView(node) {\n  return node.ownerDocument && node.ownerDocument.defaultView || node.document && node || node.defaultView;\n}\nfunction styleRemove(name) {\n  return function() {\n    this.style.removeProperty(name);\n  };\n}\nfunction styleConstant(name, value, priority) {\n  return function() {\n    this.style.setProperty(name, value, priority);\n  };\n}\nfunction styleFunction(name, value, priority) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null)\n      this.style.removeProperty(name);\n    else\n      this.style.setProperty(name, v, priority);\n  };\n}\nfunction selection_style(name, value, priority) {\n  return arguments.length > 1 ? this.each((value == null ? styleRemove : typeof value === \"function\" ? styleFunction : styleConstant)(name, value, priority == null ? \"\" : priority)) : styleValue(this.node(), name);\n}\nfunction styleValue(node, name) {\n  return node.style.getPropertyValue(name) || defaultView(node).getComputedStyle(node, null).getPropertyValue(name);\n}\nfunction propertyRemove(name) {\n  return function() {\n    delete this[name];\n  };\n}\nfunction propertyConstant(name, value) {\n  return function() {\n    this[name] = value;\n  };\n}\nfunction propertyFunction(name, value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    if (v == null)\n      delete this[name];\n    else\n      this[name] = v;\n  };\n}\nfunction selection_property(name, value) {\n  return arguments.length > 1 ? this.each((value == null ? propertyRemove : typeof value === \"function\" ? propertyFunction : propertyConstant)(name, value)) : this.node()[name];\n}\nfunction classArray(string) {\n  return string.trim().split(/^|\\s+/);\n}\nfunction classList(node) {\n  return node.classList || new ClassList(node);\n}\nfunction ClassList(node) {\n  this._node = node;\n  this._names = classArray(node.getAttribute(\"class\") || \"\");\n}\nClassList.prototype = {\n  add: function(name) {\n    var i = this._names.indexOf(name);\n    if (i < 0) {\n      this._names.push(name);\n      this._node.setAttribute(\"class\", this._names.join(\" \"));\n    }\n  },\n  remove: function(name) {\n    var i = this._names.indexOf(name);\n    if (i >= 0) {\n      this._names.splice(i, 1);\n      this._node.setAttribute(\"class\", this._names.join(\" \"));\n    }\n  },\n  contains: function(name) {\n    return this._names.indexOf(name) >= 0;\n  }\n};\nfunction classedAdd(node, names) {\n  var list = classList(node), i = -1, n = names.length;\n  while (++i < n)\n    list.add(names[i]);\n}\nfunction classedRemove(node, names) {\n  var list = classList(node), i = -1, n = names.length;\n  while (++i < n)\n    list.remove(names[i]);\n}\nfunction classedTrue(names) {\n  return function() {\n    classedAdd(this, names);\n  };\n}\nfunction classedFalse(names) {\n  return function() {\n    classedRemove(this, names);\n  };\n}\nfunction classedFunction(names, value) {\n  return function() {\n    (value.apply(this, arguments) ? classedAdd : classedRemove)(this, names);\n  };\n}\nfunction selection_classed(name, value) {\n  var names = classArray(name + \"\");\n  if (arguments.length < 2) {\n    var list = classList(this.node()), i = -1, n = names.length;\n    while (++i < n)\n      if (!list.contains(names[i]))\n        return false;\n    return true;\n  }\n  return this.each((typeof value === \"function\" ? classedFunction : value ? classedTrue : classedFalse)(names, value));\n}\nfunction textRemove() {\n  this.textContent = \"\";\n}\nfunction textConstant(value) {\n  return function() {\n    this.textContent = value;\n  };\n}\nfunction textFunction(value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    this.textContent = v == null ? \"\" : v;\n  };\n}\nfunction selection_text(value) {\n  return arguments.length ? this.each(value == null ? textRemove : (typeof value === \"function\" ? textFunction : textConstant)(value)) : this.node().textContent;\n}\nfunction htmlRemove() {\n  this.innerHTML = \"\";\n}\nfunction htmlConstant(value) {\n  return function() {\n    this.innerHTML = value;\n  };\n}\nfunction htmlFunction(value) {\n  return function() {\n    var v = value.apply(this, arguments);\n    this.innerHTML = v == null ? \"\" : v;\n  };\n}\nfunction selection_html(value) {\n  return arguments.length ? this.each(value == null ? htmlRemove : (typeof value === \"function\" ? htmlFunction : htmlConstant)(value)) : this.node().innerHTML;\n}\nfunction raise() {\n  if (this.nextSibling)\n    this.parentNode.appendChild(this);\n}\nfunction selection_raise() {\n  return this.each(raise);\n}\nfunction lower() {\n  if (this.previousSibling)\n    this.parentNode.insertBefore(this, this.parentNode.firstChild);\n}\nfunction selection_lower() {\n  return this.each(lower);\n}\nfunction selection_append(name) {\n  var create = typeof name === \"function\" ? name : creator(name);\n  return this.select(function() {\n    return this.appendChild(create.apply(this, arguments));\n  });\n}\nfunction constantNull() {\n  return null;\n}\nfunction selection_insert(name, before) {\n  var create = typeof name === \"function\" ? name : creator(name), select2 = before == null ? constantNull : typeof before === \"function\" ? before : selector(before);\n  return this.select(function() {\n    return this.insertBefore(create.apply(this, arguments), select2.apply(this, arguments) || null);\n  });\n}\nfunction remove() {\n  var parent = this.parentNode;\n  if (parent)\n    parent.removeChild(this);\n}\nfunction selection_remove() {\n  return this.each(remove);\n}\nfunction selection_cloneShallow() {\n  var clone = this.cloneNode(false), parent = this.parentNode;\n  return parent ? parent.insertBefore(clone, this.nextSibling) : clone;\n}\nfunction selection_cloneDeep() {\n  var clone = this.cloneNode(true), parent = this.parentNode;\n  return parent ? parent.insertBefore(clone, this.nextSibling) : clone;\n}\nfunction selection_clone(deep) {\n  return this.select(deep ? selection_cloneDeep : selection_cloneShallow);\n}\nfunction selection_datum(value) {\n  return arguments.length ? this.property(\"__data__\", value) : this.node().__data__;\n}\nfunction contextListener(listener) {\n  return function(event) {\n    listener.call(this, event, this.__data__);\n  };\n}\nfunction parseTypenames$1(typenames) {\n  return typenames.trim().split(/^|\\s+/).map(function(t) {\n    var name = \"\", i = t.indexOf(\".\");\n    if (i >= 0)\n      name = t.slice(i + 1), t = t.slice(0, i);\n    return { type: t, name };\n  });\n}\nfunction onRemove(typename) {\n  return function() {\n    var on = this.__on;\n    if (!on)\n      return;\n    for (var j = 0, i = -1, m = on.length, o; j < m; ++j) {\n      if (o = on[j], (!typename.type || o.type === typename.type) && o.name === typename.name) {\n        this.removeEventListener(o.type, o.listener, o.options);\n      } else {\n        on[++i] = o;\n      }\n    }\n    if (++i)\n      on.length = i;\n    else\n      delete this.__on;\n  };\n}\nfunction onAdd(typename, value, options) {\n  return function() {\n    var on = this.__on, o, listener = contextListener(value);\n    if (on)\n      for (var j = 0, m = on.length; j < m; ++j) {\n        if ((o = on[j]).type === typename.type && o.name === typename.name) {\n          this.removeEventListener(o.type, o.listener, o.options);\n          this.addEventListener(o.type, o.listener = listener, o.options = options);\n          o.value = value;\n          return;\n        }\n      }\n    this.addEventListener(typename.type, listener, options);\n    o = { type: typename.type, name: typename.name, value, listener, options };\n    if (!on)\n      this.__on = [o];\n    else\n      on.push(o);\n  };\n}\nfunction selection_on(typename, value, options) {\n  var typenames = parseTypenames$1(typename + \"\"), i, n = typenames.length, t;\n  if (arguments.length < 2) {\n    var on = this.node().__on;\n    if (on)\n      for (var j = 0, m = on.length, o; j < m; ++j) {\n        for (i = 0, o = on[j]; i < n; ++i) {\n          if ((t = typenames[i]).type === o.type && t.name === o.name) {\n            return o.value;\n          }\n        }\n      }\n    return;\n  }\n  on = value ? onAdd : onRemove;\n  for (i = 0; i < n; ++i)\n    this.each(on(typenames[i], value, options));\n  return this;\n}\nfunction dispatchEvent(node, type, params) {\n  var window = defaultView(node), event = window.CustomEvent;\n  if (typeof event === \"function\") {\n    event = new event(type, params);\n  } else {\n    event = window.document.createEvent(\"Event\");\n    if (params)\n      event.initEvent(type, params.bubbles, params.cancelable), event.detail = params.detail;\n    else\n      event.initEvent(type, false, false);\n  }\n  node.dispatchEvent(event);\n}\nfunction dispatchConstant(type, params) {\n  return function() {\n    return dispatchEvent(this, type, params);\n  };\n}\nfunction dispatchFunction(type, params) {\n  return function() {\n    return dispatchEvent(this, type, params.apply(this, arguments));\n  };\n}\nfunction selection_dispatch(type, params) {\n  return this.each((typeof params === \"function\" ? dispatchFunction : dispatchConstant)(type, params));\n}\nfunction* selection_iterator() {\n  for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {\n    for (var group = groups[j], i = 0, n = group.length, node; i < n; ++i) {\n      if (node = group[i])\n        yield node;\n    }\n  }\n}\nvar root = [null];\nfunction Selection(groups, parents) {\n  this._groups = groups;\n  this._parents = parents;\n}\nfunction selection_selection() {\n  return this;\n}\nSelection.prototype = {\n  constructor: Selection,\n  select: selection_select,\n  selectAll: selection_selectAll,\n  selectChild: selection_selectChild,\n  selectChildren: selection_selectChildren,\n  filter: selection_filter,\n  data: selection_data,\n  enter: selection_enter,\n  exit: selection_exit,\n  join: selection_join,\n  merge: selection_merge,\n  selection: selection_selection,\n  order: selection_order,\n  sort: selection_sort,\n  call: selection_call,\n  nodes: selection_nodes,\n  node: selection_node,\n  size: selection_size,\n  empty: selection_empty,\n  each: selection_each,\n  attr: selection_attr,\n  style: selection_style,\n  property: selection_property,\n  classed: selection_classed,\n  text: selection_text,\n  html: selection_html,\n  raise: selection_raise,\n  lower: selection_lower,\n  append: selection_append,\n  insert: selection_insert,\n  remove: selection_remove,\n  clone: selection_clone,\n  datum: selection_datum,\n  on: selection_on,\n  dispatch: selection_dispatch,\n  [Symbol.iterator]: selection_iterator\n};\nfunction select(selector2) {\n  return typeof selector2 === \"string\" ? new Selection([[document.querySelector(selector2)]], [document.documentElement]) : new Selection([[selector2]], root);\n}\nfunction sourceEvent(event) {\n  let sourceEvent2;\n  while (sourceEvent2 = event.sourceEvent)\n    event = sourceEvent2;\n  return event;\n}\nfunction pointer(event, node) {\n  event = sourceEvent(event);\n  if (node === void 0)\n    node = event.currentTarget;\n  if (node) {\n    var svg = node.ownerSVGElement || node;\n    if (svg.createSVGPoint) {\n      var point = svg.createSVGPoint();\n      point.x = event.clientX, point.y = event.clientY;\n      point = point.matrixTransform(node.getScreenCTM().inverse());\n      return [point.x, point.y];\n    }\n    if (node.getBoundingClientRect) {\n      var rect = node.getBoundingClientRect();\n      return [event.clientX - rect.left - node.clientLeft, event.clientY - rect.top - node.clientTop];\n    }\n  }\n  return [event.pageX, event.pageY];\n}\nvar noop = { value: () => {\n} };\nfunction dispatch() {\n  for (var i = 0, n = arguments.length, _ = {}, t; i < n; ++i) {\n    if (!(t = arguments[i] + \"\") || t in _ || /[\\s.]/.test(t))\n      throw new Error(\"illegal type: \" + t);\n    _[t] = [];\n  }\n  return new Dispatch(_);\n}\nfunction Dispatch(_) {\n  this._ = _;\n}\nfunction parseTypenames(typenames, types) {\n  return typenames.trim().split(/^|\\s+/).map(function(t) {\n    var name = \"\", i = t.indexOf(\".\");\n    if (i >= 0)\n      name = t.slice(i + 1), t = t.slice(0, i);\n    if (t && !types.hasOwnProperty(t))\n      throw new Error(\"unknown type: \" + t);\n    return { type: t, name };\n  });\n}\nDispatch.prototype = dispatch.prototype = {\n  constructor: Dispatch,\n  on: function(typename, callback) {\n    var _ = this._, T = parseTypenames(typename + \"\", _), t, i = -1, n = T.length;\n    if (arguments.length < 2) {\n      while (++i < n)\n        if ((t = (typename = T[i]).type) && (t = get(_[t], typename.name)))\n          return t;\n      return;\n    }\n    if (callback != null && typeof callback !== \"function\")\n      throw new Error(\"invalid callback: \" + callback);\n    while (++i < n) {\n      if (t = (typename = T[i]).type)\n        _[t] = set(_[t], typename.name, callback);\n      else if (callback == null)\n        for (t in _)\n          _[t] = set(_[t], typename.name, null);\n    }\n    return this;\n  },\n  copy: function() {\n    var copy = {}, _ = this._;\n    for (var t in _)\n      copy[t] = _[t].slice();\n    return new Dispatch(copy);\n  },\n  call: function(type, that) {\n    if ((n = arguments.length - 2) > 0)\n      for (var args = new Array(n), i = 0, n, t; i < n; ++i)\n        args[i] = arguments[i + 2];\n    if (!this._.hasOwnProperty(type))\n      throw new Error(\"unknown type: \" + type);\n    for (t = this._[type], i = 0, n = t.length; i < n; ++i)\n      t[i].value.apply(that, args);\n  },\n  apply: function(type, that, args) {\n    if (!this._.hasOwnProperty(type))\n      throw new Error(\"unknown type: \" + type);\n    for (var t = this._[type], i = 0, n = t.length; i < n; ++i)\n      t[i].value.apply(that, args);\n  }\n};\nfunction get(type, name) {\n  for (var i = 0, n = type.length, c; i < n; ++i) {\n    if ((c = type[i]).name === name) {\n      return c.value;\n    }\n  }\n}\nfunction set(type, name, callback) {\n  for (var i = 0, n = type.length; i < n; ++i) {\n    if (type[i].name === name) {\n      type[i] = noop, type = type.slice(0, i).concat(type.slice(i + 1));\n      break;\n    }\n  }\n  if (callback != null)\n    type.push({ name, value: callback });\n  return type;\n}\nconst nonpassive = { passive: false };\nconst nonpassivecapture = { capture: true, passive: false };\nfunction nopropagation(event) {\n  event.stopImmediatePropagation();\n}\nfunction noevent(event) {\n  event.preventDefault();\n  event.stopImmediatePropagation();\n}\nfunction nodrag(view) {\n  var root2 = view.document.documentElement, selection = select(view).on(\"dragstart.drag\", noevent, nonpassivecapture);\n  if (\"onselectstart\" in root2) {\n    selection.on(\"selectstart.drag\", noevent, nonpassivecapture);\n  } else {\n    root2.__noselect = root2.style.MozUserSelect;\n    root2.style.MozUserSelect = \"none\";\n  }\n}\nfunction yesdrag(view, noclick) {\n  var root2 = view.document.documentElement, selection = select(view).on(\"dragstart.drag\", null);\n  if (noclick) {\n    selection.on(\"click.drag\", noevent, nonpassivecapture);\n    setTimeout(function() {\n      selection.on(\"click.drag\", null);\n    }, 0);\n  }\n  if (\"onselectstart\" in root2) {\n    selection.on(\"selectstart.drag\", null);\n  } else {\n    root2.style.MozUserSelect = root2.__noselect;\n    delete root2.__noselect;\n  }\n}\nconst constant = (x) => () => x;\nfunction DragEvent(type, {\n  sourceEvent: sourceEvent2,\n  subject,\n  target,\n  identifier,\n  active,\n  x,\n  y,\n  dx,\n  dy,\n  dispatch: dispatch2\n}) {\n  Object.defineProperties(this, {\n    type: { value: type, enumerable: true, configurable: true },\n    sourceEvent: { value: sourceEvent2, enumerable: true, configurable: true },\n    subject: { value: subject, enumerable: true, configurable: true },\n    target: { value: target, enumerable: true, configurable: true },\n    identifier: { value: identifier, enumerable: true, configurable: true },\n    active: { value: active, enumerable: true, configurable: true },\n    x: { value: x, enumerable: true, configurable: true },\n    y: { value: y, enumerable: true, configurable: true },\n    dx: { value: dx, enumerable: true, configurable: true },\n    dy: { value: dy, enumerable: true, configurable: true },\n    _: { value: dispatch2 }\n  });\n}\nDragEvent.prototype.on = function() {\n  var value = this._.on.apply(this._, arguments);\n  return value === this._ ? this : value;\n};\nfunction defaultFilter(event) {\n  return !event.ctrlKey && !event.button;\n}\nfunction defaultContainer() {\n  return this.parentNode;\n}\nfunction defaultSubject(event, d) {\n  return d == null ? { x: event.x, y: event.y } : d;\n}\nfunction defaultTouchable() {\n  return navigator.maxTouchPoints || \"ontouchstart\" in this;\n}\nfunction drag() {\n  var filter2 = defaultFilter, container = defaultContainer, subject = defaultSubject, touchable = defaultTouchable, gestures = {}, listeners = dispatch(\"start\", \"drag\", \"end\"), active = 0, mousedownx, mousedowny, mousemoving, touchending, clickDistance2 = 0;\n  function drag2(selection) {\n    selection.on(\"mousedown.drag\", mousedowned).filter(touchable).on(\"touchstart.drag\", touchstarted).on(\"touchmove.drag\", touchmoved, nonpassive).on(\"touchend.drag touchcancel.drag\", touchended).style(\"touch-action\", \"none\").style(\"-webkit-tap-highlight-color\", \"rgba(0,0,0,0)\");\n  }\n  function mousedowned(event, d) {\n    if (touchending || !filter2.call(this, event, d))\n      return;\n    var gesture = beforestart(this, container.call(this, event, d), event, d, \"mouse\");\n    if (!gesture)\n      return;\n    select(event.view).on(\"mousemove.drag\", mousemoved, nonpassivecapture).on(\"mouseup.drag\", mouseupped, nonpassivecapture);\n    nodrag(event.view);\n    nopropagation(event);\n    mousemoving = false;\n    mousedownx = event.clientX;\n    mousedowny = event.clientY;\n    gesture(\"start\", event);\n  }\n  function mousemoved(event) {\n    noevent(event);\n    if (!mousemoving) {\n      var dx = event.clientX - mousedownx, dy = event.clientY - mousedowny;\n      mousemoving = dx * dx + dy * dy > clickDistance2;\n    }\n    gestures.mouse(\"drag\", event);\n  }\n  function mouseupped(event) {\n    select(event.view).on(\"mousemove.drag mouseup.drag\", null);\n    yesdrag(event.view, mousemoving);\n    noevent(event);\n    gestures.mouse(\"end\", event);\n  }\n  function touchstarted(event, d) {\n    if (!filter2.call(this, event, d))\n      return;\n    var touches = event.changedTouches, c = container.call(this, event, d), n = touches.length, i, gesture;\n    for (i = 0; i < n; ++i) {\n      if (gesture = beforestart(this, c, event, d, touches[i].identifier, touches[i])) {\n        nopropagation(event);\n        gesture(\"start\", event, touches[i]);\n      }\n    }\n  }\n  function touchmoved(event) {\n    var touches = event.changedTouches, n = touches.length, i, gesture;\n    for (i = 0; i < n; ++i) {\n      if (gesture = gestures[touches[i].identifier]) {\n        noevent(event);\n        gesture(\"drag\", event, touches[i]);\n      }\n    }\n  }\n  function touchended(event) {\n    var touches = event.changedTouches, n = touches.length, i, gesture;\n    if (touchending)\n      clearTimeout(touchending);\n    touchending = setTimeout(function() {\n      touchending = null;\n    }, 500);\n    for (i = 0; i < n; ++i) {\n      if (gesture = gestures[touches[i].identifier]) {\n        nopropagation(event);\n        gesture(\"end\", event, touches[i]);\n      }\n    }\n  }\n  function beforestart(that, container2, event, d, identifier, touch) {\n    var dispatch2 = listeners.copy(), p = pointer(touch || event, container2), dx, dy, s;\n    if ((s = subject.call(that, new DragEvent(\"beforestart\", {\n      sourceEvent: event,\n      target: drag2,\n      identifier,\n      active,\n      x: p[0],\n      y: p[1],\n      dx: 0,\n      dy: 0,\n      dispatch: dispatch2\n    }), d)) == null)\n      return;\n    dx = s.x - p[0] || 0;\n    dy = s.y - p[1] || 0;\n    return function gesture(type, event2, touch2) {\n      var p0 = p, n;\n      switch (type) {\n        case \"start\":\n          gestures[identifier] = gesture, n = active++;\n          break;\n        case \"end\":\n          delete gestures[identifier], --active;\n        case \"drag\":\n          p = pointer(touch2 || event2, container2), n = active;\n          break;\n      }\n      dispatch2.call(\n        type,\n        that,\n        new DragEvent(type, {\n          sourceEvent: event2,\n          subject: s,\n          target: drag2,\n          identifier,\n          active: n,\n          x: p[0] + dx,\n          y: p[1] + dy,\n          dx: p[0] - p0[0],\n          dy: p[1] - p0[1],\n          dispatch: dispatch2\n        }),\n        d\n      );\n    };\n  }\n  drag2.filter = function(_) {\n    return arguments.length ? (filter2 = typeof _ === \"function\" ? _ : constant(!!_), drag2) : filter2;\n  };\n  drag2.container = function(_) {\n    return arguments.length ? (container = typeof _ === \"function\" ? _ : constant(_), drag2) : container;\n  };\n  drag2.subject = function(_) {\n    return arguments.length ? (subject = typeof _ === \"function\" ? _ : constant(_), drag2) : subject;\n  };\n  drag2.touchable = function(_) {\n    return arguments.length ? (touchable = typeof _ === \"function\" ? _ : constant(!!_), drag2) : touchable;\n  };\n  drag2.on = function() {\n    var value = listeners.on.apply(listeners, arguments);\n    return value === listeners ? drag2 : value;\n  };\n  drag2.clickDistance = function(_) {\n    return arguments.length ? (clickDistance2 = (_ = +_) * _, drag2) : Math.sqrt(clickDistance2);\n  };\n  return drag2;\n}\nvar ResizeControlVariant = /* @__PURE__ */ ((ResizeControlVariant2) => {\n  ResizeControlVariant2[\"Line\"] = \"line\";\n  ResizeControlVariant2[\"Handle\"] = \"handle\";\n  return ResizeControlVariant2;\n})(ResizeControlVariant || {});\nfunction getDirection({ width, prevWidth, height, prevHeight, invertX, invertY }) {\n  const deltaWidth = width - prevWidth;\n  const deltaHeight = height - prevHeight;\n  const direction = [deltaWidth > 0 ? 1 : deltaWidth < 0 ? -1 : 0, deltaHeight > 0 ? 1 : deltaHeight < 0 ? -1 : 0];\n  if (deltaWidth && invertX) {\n    direction[0] = direction[0] * -1;\n  }\n  if (deltaHeight && invertY) {\n    direction[1] = direction[1] * -1;\n  }\n  return direction;\n}\nconst DefaultPositions = {\n  [ResizeControlVariant.Line]: \"right\",\n  [ResizeControlVariant.Handle]: \"bottom-right\"\n};\nconst StylingProperty = {\n  [ResizeControlVariant.Line]: \"borderColor\",\n  [ResizeControlVariant.Handle]: \"backgroundColor\"\n};\nconst __default__$1 = {\n  name: \"ResizeControl\",\n  compatConfig: { MODE: 3 }\n};\nconst _sfc_main$1 = /* @__PURE__ */ defineComponent({\n  ...__default__$1,\n  props: {\n    nodeId: {},\n    color: {},\n    minWidth: { default: 10 },\n    minHeight: { default: 10 },\n    maxWidth: { default: Number.MAX_VALUE },\n    maxHeight: { default: Number.MAX_VALUE },\n    position: {},\n    variant: { default: \"handle\" },\n    shouldResize: {},\n    keepAspectRatio: { type: [Boolean, Number], default: false }\n  },\n  emits: [\"resizeStart\", \"resize\", \"resizeEnd\"],\n  setup(__props, { emit: emits }) {\n    const props = __props;\n    const initPrevValues = { width: 0, height: 0, x: 0, y: 0 };\n    const initStartValues = {\n      ...initPrevValues,\n      pointerX: 0,\n      pointerY: 0,\n      aspectRatio: 1\n    };\n    const { findNode, emits: triggerEmits } = useVueFlow();\n    const getPointerPosition = useGetPointerPosition();\n    const resizeControlRef = ref();\n    let startValues = initStartValues;\n    let prevValues = initPrevValues;\n    const controlPosition = toRef(() => props.position ?? DefaultPositions[props.variant]);\n    const positionClassNames = toRef(() => controlPosition.value.split(\"-\"));\n    const controlStyle = toRef(() => props.color ? { [StylingProperty[props.variant]]: props.color } : {});\n    watchEffect((onCleanup) => {\n      if (!resizeControlRef.value || !props.nodeId) {\n        return;\n      }\n      const selection = select(resizeControlRef.value);\n      const enableX = controlPosition.value.includes(\"right\") || controlPosition.value.includes(\"left\");\n      const enableY = controlPosition.value.includes(\"bottom\") || controlPosition.value.includes(\"top\");\n      const invertX = controlPosition.value.includes(\"left\");\n      const invertY = controlPosition.value.includes(\"top\");\n      const dragHandler = drag().on(\"start\", (event) => {\n        const node = findNode(props.nodeId);\n        const { xSnapped, ySnapped } = getPointerPosition(event);\n        prevValues = {\n          width: (node == null ? void 0 : node.dimensions.width) ?? 0,\n          height: (node == null ? void 0 : node.dimensions.height) ?? 0,\n          x: (node == null ? void 0 : node.position.x) ?? 0,\n          y: (node == null ? void 0 : node.position.y) ?? 0\n        };\n        startValues = {\n          ...prevValues,\n          pointerX: xSnapped,\n          pointerY: ySnapped,\n          aspectRatio: prevValues.width / prevValues.height\n        };\n        emits(\"resizeStart\", { event, params: prevValues });\n      }).on(\"drag\", (event) => {\n        var _a;\n        const { xSnapped, ySnapped } = getPointerPosition(event);\n        const node = findNode(props.nodeId);\n        if (node) {\n          const changes = [];\n          const {\n            pointerX: startX,\n            pointerY: startY,\n            width: startWidth,\n            height: startHeight,\n            x: startNodeX,\n            y: startNodeY,\n            aspectRatio: startAspectRatio\n          } = startValues;\n          const { x: prevX, y: prevY, width: prevWidth, height: prevHeight } = prevValues;\n          const distX = Math.floor(enableX ? xSnapped - startX : 0);\n          const distY = Math.floor(enableY ? ySnapped - startY : 0);\n          let width = clamp(startWidth + (invertX ? -distX : distX), props.minWidth, props.maxWidth);\n          let height = clamp(startHeight + (invertY ? -distY : distY), props.minHeight, props.maxHeight);\n          if (props.keepAspectRatio) {\n            const nextAspectRatio = width / height;\n            let aspectRatio = startAspectRatio;\n            if (typeof props.keepAspectRatio === \"number\" && nextAspectRatio !== props.keepAspectRatio) {\n              aspectRatio = props.keepAspectRatio;\n            }\n            const isDiagonal = enableX && enableY;\n            const isHorizontal = enableX && !enableY;\n            const isVertical = enableY && !enableX;\n            width = nextAspectRatio <= aspectRatio && isDiagonal || isVertical ? height * aspectRatio : width;\n            height = nextAspectRatio > aspectRatio && isDiagonal || isHorizontal ? width / aspectRatio : height;\n            if (width >= props.maxWidth) {\n              width = props.maxWidth;\n              height = props.maxWidth / aspectRatio;\n            } else if (width <= props.minWidth) {\n              width = props.minWidth;\n              height = props.minWidth / aspectRatio;\n            }\n            if (height >= props.maxHeight) {\n              height = props.maxHeight;\n              width = props.maxHeight * aspectRatio;\n            } else if (height <= props.minHeight) {\n              height = props.minHeight;\n              width = props.minHeight * aspectRatio;\n            }\n          }\n          const isWidthChange = width !== prevWidth;\n          const isHeightChange = height !== prevHeight;\n          if (invertX || invertY) {\n            const x = invertX ? startNodeX - (width - startWidth) : startNodeX;\n            const y = invertY ? startNodeY - (height - startHeight) : startNodeY;\n            const isXPosChange = x !== prevX && isWidthChange;\n            const isYPosChange = y !== prevY && isHeightChange;\n            if (isXPosChange || isYPosChange) {\n              const positionChange = {\n                id: node.id,\n                type: \"position\",\n                from: node.position,\n                position: {\n                  x: isXPosChange ? x : prevX,\n                  y: isYPosChange ? y : prevY\n                }\n              };\n              changes.push(positionChange);\n              prevValues.x = positionChange.position.x;\n              prevValues.y = positionChange.position.y;\n            }\n          }\n          if (props.nodeId && (isWidthChange || isHeightChange)) {\n            const dimensionChange = {\n              id: props.nodeId,\n              type: \"dimensions\",\n              updateStyle: true,\n              resizing: true,\n              dimensions: {\n                width,\n                height\n              }\n            };\n            changes.push(dimensionChange);\n            prevValues.width = width;\n            prevValues.height = height;\n          }\n          if (changes.length === 0) {\n            return;\n          }\n          const direction = getDirection({\n            width: prevValues.width,\n            prevWidth,\n            height: prevValues.height,\n            prevHeight,\n            invertX,\n            invertY\n          });\n          const nextValues = { ...prevValues, direction };\n          const callResize = (_a = props.shouldResize) == null ? void 0 : _a.call(props, event, nextValues);\n          if (callResize === false) {\n            return;\n          }\n          emits(\"resize\", { event, params: nextValues });\n          triggerEmits.nodesChange(changes);\n        }\n      }).on(\"end\", (event) => {\n        if (props.nodeId) {\n          const dimensionChange = {\n            id: props.nodeId,\n            type: \"dimensions\",\n            resizing: false\n          };\n          emits(\"resizeEnd\", { event, params: prevValues });\n          triggerEmits.nodesChange([dimensionChange]);\n        }\n      });\n      selection.call(dragHandler);\n      onCleanup(() => {\n        selection.on(\".drag\", null);\n      });\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        ref_key: \"resizeControlRef\",\n        ref: resizeControlRef,\n        class: normalizeClass([\"vue-flow__resize-control nodrag\", [...positionClassNames.value, _ctx.variant]]),\n        style: normalizeStyle(controlStyle.value)\n      }, [\n        renderSlot(_ctx.$slots, \"default\")\n      ], 6);\n    };\n  }\n});\nconst __default__ = {\n  name: \"NodeResizer\",\n  compatConfig: { MODE: 3 },\n  inheritAttrs: false\n};\nconst _sfc_main = /* @__PURE__ */ defineComponent({\n  ...__default__,\n  props: {\n    nodeId: {},\n    color: {},\n    handleClassName: {},\n    handleStyle: {},\n    lineClassName: {},\n    lineStyle: {},\n    isVisible: { type: Boolean, default: true },\n    minWidth: {},\n    minHeight: {},\n    maxWidth: {},\n    maxHeight: {},\n    shouldResize: {},\n    keepAspectRatio: { type: [Boolean, Number] }\n  },\n  emits: [\"resizeStart\", \"resize\", \"resizeEnd\"],\n  setup(__props, { emit: emits }) {\n    const props = __props;\n    const { findNode, emits: triggerEmits } = useVueFlow();\n    const handleControls = [\"top-left\", \"top-right\", \"bottom-left\", \"bottom-right\"];\n    const lineControls = [\"top\", \"right\", \"bottom\", \"left\"];\n    const contextNodeId = inject(NodeIdInjection, null);\n    const nodeId = toRef(() => typeof props.nodeId === \"string\" ? props.nodeId : contextNodeId);\n    const node = computed(() => findNode(nodeId.value));\n    watch(\n      [\n        () => props.minWidth,\n        () => props.minHeight,\n        () => props.maxWidth,\n        () => props.maxHeight,\n        () => {\n          var _a;\n          return !!((_a = node.value) == null ? void 0 : _a.dimensions.width) && !!node.value.dimensions.height;\n        }\n      ],\n      ([minWidth, minHeight, maxWidth, maxHeight, isInitialized]) => {\n        const n = node.value;\n        if (n && isInitialized) {\n          const dimensionChange = {\n            id: n.id,\n            type: \"dimensions\",\n            updateStyle: true,\n            dimensions: {\n              width: n.dimensions.width,\n              height: n.dimensions.height\n            }\n          };\n          if (minWidth && n.dimensions.width < minWidth) {\n            dimensionChange.dimensions.width = minWidth;\n          }\n          if (minHeight && n.dimensions.height < minHeight) {\n            dimensionChange.dimensions.height = minHeight;\n          }\n          if (maxWidth && n.dimensions.width > maxWidth) {\n            dimensionChange.dimensions.width = maxWidth;\n          }\n          if (maxHeight && n.dimensions.height > maxHeight) {\n            dimensionChange.dimensions.height = maxHeight;\n          }\n          if (dimensionChange.dimensions.width !== n.dimensions.width || dimensionChange.dimensions.height !== n.dimensions.height) {\n            triggerEmits.nodesChange([dimensionChange]);\n          }\n        }\n      },\n      { flush: \"post\", immediate: true }\n    );\n    return (_ctx, _cache) => {\n      return _ctx.isVisible ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [\n        (openBlock(), createElementBlock(Fragment, null, renderList(lineControls, (c) => {\n          return createVNode(_sfc_main$1, {\n            key: c,\n            class: normalizeClass(_ctx.lineClassName),\n            style: normalizeStyle(_ctx.lineStyle),\n            \"node-id\": nodeId.value,\n            position: c,\n            variant: unref(ResizeControlVariant).Line,\n            \"keep-aspect-ratio\": _ctx.keepAspectRatio,\n            color: _ctx.color,\n            \"min-width\": _ctx.minWidth,\n            \"min-height\": _ctx.minHeight,\n            \"max-width\": _ctx.maxWidth,\n            \"max-height\": _ctx.maxHeight,\n            \"should-resize\": _ctx.shouldResize,\n            onResizeStart: _cache[0] || (_cache[0] = ($event) => emits(\"resizeStart\", $event)),\n            onResize: _cache[1] || (_cache[1] = ($event) => emits(\"resize\", $event)),\n            onResizeEnd: _cache[2] || (_cache[2] = ($event) => emits(\"resizeEnd\", $event))\n          }, null, 8, [\"class\", \"style\", \"node-id\", \"position\", \"variant\", \"keep-aspect-ratio\", \"color\", \"min-width\", \"min-height\", \"max-width\", \"max-height\", \"should-resize\"]);\n        }), 64)),\n        (openBlock(), createElementBlock(Fragment, null, renderList(handleControls, (c) => {\n          return createVNode(_sfc_main$1, {\n            key: c,\n            class: normalizeClass(_ctx.handleClassName),\n            style: normalizeStyle(_ctx.handleStyle),\n            \"node-id\": nodeId.value,\n            position: c,\n            color: _ctx.color,\n            \"min-width\": _ctx.minWidth,\n            \"min-height\": _ctx.minHeight,\n            \"max-width\": _ctx.maxWidth,\n            \"max-height\": _ctx.maxHeight,\n            \"should-resize\": _ctx.shouldResize,\n            \"keep-aspect-ratio\": _ctx.keepAspectRatio,\n            onResizeStart: _cache[3] || (_cache[3] = ($event) => emits(\"resizeStart\", $event)),\n            onResize: _cache[4] || (_cache[4] = ($event) => emits(\"resize\", $event)),\n            onResizeEnd: _cache[5] || (_cache[5] = ($event) => emits(\"resizeEnd\", $event))\n          }, null, 8, [\"class\", \"style\", \"node-id\", \"position\", \"color\", \"min-width\", \"min-height\", \"max-width\", \"max-height\", \"should-resize\", \"keep-aspect-ratio\"]);\n        }), 64))\n      ], 64)) : createCommentVNode(\"\", true);\n    };\n  }\n});\nexport {\n  _sfc_main$1 as NodeResizeControl,\n  _sfc_main as NodeResizer,\n  ResizeControlVariant\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,IAAI,QAAQ;AACZ,IAAM,aAAa;AAAA,EACjB,KAAK;AAAA,EACL;AAAA,EACA,OAAO;AAAA,EACP,KAAK;AAAA,EACL,OAAO;AACT;AACA,SAAS,UAAU,MAAM;AACvB,MAAI,SAAS,QAAQ,IAAI,IAAI,OAAO,QAAQ,GAAG;AAC/C,MAAI,KAAK,MAAM,SAAS,KAAK,MAAM,GAAG,CAAC,OAAO;AAC5C,WAAO,KAAK,MAAM,IAAI,CAAC;AACzB,SAAO,WAAW,eAAe,MAAM,IAAI,EAAE,OAAO,WAAW,MAAM,GAAG,OAAO,KAAK,IAAI;AAC1F;AACA,SAAS,eAAe,MAAM;AAC5B,SAAO,WAAW;AAChB,QAAI,YAAY,KAAK,eAAe,MAAM,KAAK;AAC/C,WAAO,QAAQ,SAAS,UAAU,gBAAgB,iBAAiB,QAAQ,UAAU,cAAc,IAAI,IAAI,UAAU,gBAAgB,KAAK,IAAI;AAAA,EAChJ;AACF;AACA,SAAS,aAAa,UAAU;AAC9B,SAAO,WAAW;AAChB,WAAO,KAAK,cAAc,gBAAgB,SAAS,OAAO,SAAS,KAAK;AAAA,EAC1E;AACF;AACA,SAAS,QAAQ,MAAM;AACrB,MAAI,WAAW,UAAU,IAAI;AAC7B,UAAQ,SAAS,QAAQ,eAAe,gBAAgB,QAAQ;AAClE;AACA,SAAS,OAAO;AAChB;AACA,SAAS,SAAS,WAAW;AAC3B,SAAO,aAAa,OAAO,OAAO,WAAW;AAC3C,WAAO,KAAK,cAAc,SAAS;AAAA,EACrC;AACF;AACA,SAAS,iBAAiB,SAAS;AACjC,MAAI,OAAO,YAAY;AACrB,cAAU,SAAS,OAAO;AAC5B,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,YAAY,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC9F,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,WAAW,UAAU,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,MAAM,SAAS,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtH,WAAK,OAAO,MAAM,CAAC,OAAO,UAAU,QAAQ,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK,IAAI;AAChF,YAAI,cAAc;AAChB,kBAAQ,WAAW,KAAK;AAC1B,iBAAS,CAAC,IAAI;AAAA,MAChB;AAAA,IACF;AAAA,EACF;AACA,SAAO,IAAI,UAAU,WAAW,KAAK,QAAQ;AAC/C;AACA,SAAS,MAAM,GAAG;AAChB,SAAO,KAAK,OAAO,CAAC,IAAI,MAAM,QAAQ,CAAC,IAAI,IAAI,MAAM,KAAK,CAAC;AAC7D;AACA,SAAS,QAAQ;AACf,SAAO,CAAC;AACV;AACA,SAAS,YAAY,WAAW;AAC9B,SAAO,aAAa,OAAO,QAAQ,WAAW;AAC5C,WAAO,KAAK,iBAAiB,SAAS;AAAA,EACxC;AACF;AACA,SAAS,SAAS,SAAS;AACzB,SAAO,WAAW;AAChB,WAAO,MAAM,QAAQ,MAAM,MAAM,SAAS,CAAC;AAAA,EAC7C;AACF;AACA,SAAS,oBAAoB,SAAS;AACpC,MAAI,OAAO,YAAY;AACrB,cAAU,SAAS,OAAO;AAAA;AAE1B,cAAU,YAAY,OAAO;AAC/B,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,YAAY,CAAC,GAAG,UAAU,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAClG,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACrE,UAAI,OAAO,MAAM,CAAC,GAAG;AACnB,kBAAU,KAAK,QAAQ,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK,CAAC;AAC1D,gBAAQ,KAAK,IAAI;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AACA,SAAO,IAAI,UAAU,WAAW,OAAO;AACzC;AACA,SAAS,QAAQ,WAAW;AAC1B,SAAO,WAAW;AAChB,WAAO,KAAK,QAAQ,SAAS;AAAA,EAC/B;AACF;AACA,SAAS,aAAa,WAAW;AAC/B,SAAO,SAAS,MAAM;AACpB,WAAO,KAAK,QAAQ,SAAS;AAAA,EAC/B;AACF;AACA,IAAI,OAAO,MAAM,UAAU;AAC3B,SAAS,UAAU,OAAO;AACxB,SAAO,WAAW;AAChB,WAAO,KAAK,KAAK,KAAK,UAAU,KAAK;AAAA,EACvC;AACF;AACA,SAAS,aAAa;AACpB,SAAO,KAAK;AACd;AACA,SAAS,sBAAsB,OAAO;AACpC,SAAO,KAAK,OAAO,SAAS,OAAO,aAAa,UAAU,OAAO,UAAU,aAAa,QAAQ,aAAa,KAAK,CAAC,CAAC;AACtH;AACA,IAAI,SAAS,MAAM,UAAU;AAC7B,SAAS,WAAW;AAClB,SAAO,MAAM,KAAK,KAAK,QAAQ;AACjC;AACA,SAAS,eAAe,OAAO;AAC7B,SAAO,WAAW;AAChB,WAAO,OAAO,KAAK,KAAK,UAAU,KAAK;AAAA,EACzC;AACF;AACA,SAAS,yBAAyB,OAAO;AACvC,SAAO,KAAK,UAAU,SAAS,OAAO,WAAW,eAAe,OAAO,UAAU,aAAa,QAAQ,aAAa,KAAK,CAAC,CAAC;AAC5H;AACA,SAAS,iBAAiB,OAAO;AAC/B,MAAI,OAAO,UAAU;AACnB,YAAQ,QAAQ,KAAK;AACvB,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,YAAY,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC9F,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,WAAW,UAAU,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACnG,WAAK,OAAO,MAAM,CAAC,MAAM,MAAM,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK,GAAG;AAClE,iBAAS,KAAK,IAAI;AAAA,MACpB;AAAA,IACF;AAAA,EACF;AACA,SAAO,IAAI,UAAU,WAAW,KAAK,QAAQ;AAC/C;AACA,SAAS,OAAO,QAAQ;AACtB,SAAO,IAAI,MAAM,OAAO,MAAM;AAChC;AACA,SAAS,kBAAkB;AACzB,SAAO,IAAI,UAAU,KAAK,UAAU,KAAK,QAAQ,IAAI,MAAM,GAAG,KAAK,QAAQ;AAC7E;AACA,SAAS,UAAU,QAAQ,QAAQ;AACjC,OAAK,gBAAgB,OAAO;AAC5B,OAAK,eAAe,OAAO;AAC3B,OAAK,QAAQ;AACb,OAAK,UAAU;AACf,OAAK,WAAW;AAClB;AACA,UAAU,YAAY;AAAA,EACpB,aAAa;AAAA,EACb,aAAa,SAAS,OAAO;AAC3B,WAAO,KAAK,QAAQ,aAAa,OAAO,KAAK,KAAK;AAAA,EACpD;AAAA,EACA,cAAc,SAAS,OAAO,MAAM;AAClC,WAAO,KAAK,QAAQ,aAAa,OAAO,IAAI;AAAA,EAC9C;AAAA,EACA,eAAe,SAAS,WAAW;AACjC,WAAO,KAAK,QAAQ,cAAc,SAAS;AAAA,EAC7C;AAAA,EACA,kBAAkB,SAAS,WAAW;AACpC,WAAO,KAAK,QAAQ,iBAAiB,SAAS;AAAA,EAChD;AACF;AACA,SAAS,WAAW,GAAG;AACrB,SAAO,WAAW;AAChB,WAAO;AAAA,EACT;AACF;AACA,SAAS,UAAU,QAAQ,OAAO,OAAO,QAAQ,MAAM,MAAM;AAC3D,MAAI,IAAI,GAAG,MAAM,cAAc,MAAM,QAAQ,aAAa,KAAK;AAC/D,SAAO,IAAI,YAAY,EAAE,GAAG;AAC1B,QAAI,OAAO,MAAM,CAAC,GAAG;AACnB,WAAK,WAAW,KAAK,CAAC;AACtB,aAAO,CAAC,IAAI;AAAA,IACd,OAAO;AACL,YAAM,CAAC,IAAI,IAAI,UAAU,QAAQ,KAAK,CAAC,CAAC;AAAA,IAC1C;AAAA,EACF;AACA,SAAO,IAAI,aAAa,EAAE,GAAG;AAC3B,QAAI,OAAO,MAAM,CAAC,GAAG;AACnB,WAAK,CAAC,IAAI;AAAA,IACZ;AAAA,EACF;AACF;AACA,SAAS,QAAQ,QAAQ,OAAO,OAAO,QAAQ,MAAM,MAAM,KAAK;AAC9D,MAAI,GAAG,MAAM,iBAAiC,oBAAI,IAAI,GAAG,cAAc,MAAM,QAAQ,aAAa,KAAK,QAAQ,YAAY,IAAI,MAAM,WAAW,GAAG;AACnJ,OAAK,IAAI,GAAG,IAAI,aAAa,EAAE,GAAG;AAChC,QAAI,OAAO,MAAM,CAAC,GAAG;AACnB,gBAAU,CAAC,IAAI,WAAW,IAAI,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK,IAAI;AACpE,UAAI,eAAe,IAAI,QAAQ,GAAG;AAChC,aAAK,CAAC,IAAI;AAAA,MACZ,OAAO;AACL,uBAAe,IAAI,UAAU,IAAI;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AACA,OAAK,IAAI,GAAG,IAAI,YAAY,EAAE,GAAG;AAC/B,eAAW,IAAI,KAAK,QAAQ,KAAK,CAAC,GAAG,GAAG,IAAI,IAAI;AAChD,QAAI,OAAO,eAAe,IAAI,QAAQ,GAAG;AACvC,aAAO,CAAC,IAAI;AACZ,WAAK,WAAW,KAAK,CAAC;AACtB,qBAAe,OAAO,QAAQ;AAAA,IAChC,OAAO;AACL,YAAM,CAAC,IAAI,IAAI,UAAU,QAAQ,KAAK,CAAC,CAAC;AAAA,IAC1C;AAAA,EACF;AACA,OAAK,IAAI,GAAG,IAAI,aAAa,EAAE,GAAG;AAChC,SAAK,OAAO,MAAM,CAAC,MAAM,eAAe,IAAI,UAAU,CAAC,CAAC,MAAM,MAAM;AAClE,WAAK,CAAC,IAAI;AAAA,IACZ;AAAA,EACF;AACF;AACA,SAAS,MAAM,MAAM;AACnB,SAAO,KAAK;AACd;AACA,SAAS,eAAe,OAAO,KAAK;AAClC,MAAI,CAAC,UAAU;AACb,WAAO,MAAM,KAAK,MAAM,KAAK;AAC/B,MAAI,OAAO,MAAM,UAAU,WAAW,UAAU,KAAK,UAAU,SAAS,KAAK;AAC7E,MAAI,OAAO,UAAU;AACnB,YAAQ,WAAW,KAAK;AAC1B,WAAS,IAAI,OAAO,QAAQ,SAAS,IAAI,MAAM,CAAC,GAAG,QAAQ,IAAI,MAAM,CAAC,GAAG,OAAO,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC/G,QAAI,SAAS,QAAQ,CAAC,GAAG,QAAQ,OAAO,CAAC,GAAG,cAAc,MAAM,QAAQ,OAAO,UAAU,MAAM,KAAK,QAAQ,UAAU,OAAO,UAAU,GAAG,OAAO,CAAC,GAAG,aAAa,KAAK,QAAQ,aAAa,MAAM,CAAC,IAAI,IAAI,MAAM,UAAU,GAAG,cAAc,OAAO,CAAC,IAAI,IAAI,MAAM,UAAU,GAAG,YAAY,KAAK,CAAC,IAAI,IAAI,MAAM,WAAW;AAC1T,SAAK,QAAQ,OAAO,YAAY,aAAa,WAAW,MAAM,GAAG;AACjE,aAAS,KAAK,GAAG,KAAK,GAAG,UAAU,MAAM,KAAK,YAAY,EAAE,IAAI;AAC9D,UAAI,WAAW,WAAW,EAAE,GAAG;AAC7B,YAAI,MAAM;AACR,eAAK,KAAK;AACZ,eAAO,EAAE,OAAO,YAAY,EAAE,MAAM,EAAE,KAAK;AACzC;AACF,iBAAS,QAAQ,QAAQ;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AACA,WAAS,IAAI,UAAU,QAAQ,OAAO;AACtC,SAAO,SAAS;AAChB,SAAO,QAAQ;AACf,SAAO;AACT;AACA,SAAS,UAAU,MAAM;AACvB,SAAO,OAAO,SAAS,YAAY,YAAY,OAAO,OAAO,MAAM,KAAK,IAAI;AAC9E;AACA,SAAS,iBAAiB;AACxB,SAAO,IAAI,UAAU,KAAK,SAAS,KAAK,QAAQ,IAAI,MAAM,GAAG,KAAK,QAAQ;AAC5E;AACA,SAAS,eAAe,SAAS,UAAU,QAAQ;AACjD,MAAI,QAAQ,KAAK,MAAM,GAAG,SAAS,MAAM,OAAO,KAAK,KAAK;AAC1D,MAAI,OAAO,YAAY,YAAY;AACjC,YAAQ,QAAQ,KAAK;AACrB,QAAI;AACF,cAAQ,MAAM,UAAU;AAAA,EAC5B,OAAO;AACL,YAAQ,MAAM,OAAO,UAAU,EAAE;AAAA,EACnC;AACA,MAAI,YAAY,MAAM;AACpB,aAAS,SAAS,MAAM;AACxB,QAAI;AACF,eAAS,OAAO,UAAU;AAAA,EAC9B;AACA,MAAI,UAAU;AACZ,SAAK,OAAO;AAAA;AAEZ,WAAO,IAAI;AACb,SAAO,SAAS,SAAS,MAAM,MAAM,MAAM,EAAE,MAAM,IAAI;AACzD;AACA,SAAS,gBAAgB,SAAS;AAChC,MAAI,YAAY,QAAQ,YAAY,QAAQ,UAAU,IAAI;AAC1D,WAAS,UAAU,KAAK,SAAS,UAAU,UAAU,SAAS,KAAK,QAAQ,QAAQ,KAAK,QAAQ,QAAQ,IAAI,KAAK,IAAI,IAAI,EAAE,GAAG,SAAS,IAAI,MAAM,EAAE,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACvK,aAAS,SAAS,QAAQ,CAAC,GAAG,SAAS,QAAQ,CAAC,GAAG,IAAI,OAAO,QAAQ,QAAQ,OAAO,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC/H,UAAI,OAAO,OAAO,CAAC,KAAK,OAAO,CAAC,GAAG;AACjC,cAAM,CAAC,IAAI;AAAA,MACb;AAAA,IACF;AAAA,EACF;AACA,SAAO,IAAI,IAAI,EAAE,GAAG;AAClB,WAAO,CAAC,IAAI,QAAQ,CAAC;AAAA,EACvB;AACA,SAAO,IAAI,UAAU,QAAQ,KAAK,QAAQ;AAC5C;AACA,SAAS,kBAAkB;AACzB,WAAS,SAAS,KAAK,SAAS,IAAI,IAAI,IAAI,OAAO,QAAQ,EAAE,IAAI,KAAK;AACpE,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,SAAS,GAAG,OAAO,MAAM,CAAC,GAAG,MAAM,EAAE,KAAK,KAAK;AACnF,UAAI,OAAO,MAAM,CAAC,GAAG;AACnB,YAAI,QAAQ,KAAK,wBAAwB,IAAI,IAAI;AAC/C,eAAK,WAAW,aAAa,MAAM,IAAI;AACzC,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,eAAe,SAAS;AAC/B,MAAI,CAAC;AACH,cAAU;AACZ,WAAS,YAAY,GAAG,GAAG;AACzB,WAAO,KAAK,IAAI,QAAQ,EAAE,UAAU,EAAE,QAAQ,IAAI,CAAC,IAAI,CAAC;AAAA,EAC1D;AACA,WAAS,SAAS,KAAK,SAAS,IAAI,OAAO,QAAQ,aAAa,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC/F,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,MAAM,QAAQ,YAAY,WAAW,CAAC,IAAI,IAAI,MAAM,CAAC,GAAG,MAAM,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AAC/G,UAAI,OAAO,MAAM,CAAC,GAAG;AACnB,kBAAU,CAAC,IAAI;AAAA,MACjB;AAAA,IACF;AACA,cAAU,KAAK,WAAW;AAAA,EAC5B;AACA,SAAO,IAAI,UAAU,YAAY,KAAK,QAAQ,EAAE,MAAM;AACxD;AACA,SAAS,UAAU,GAAG,GAAG;AACvB,SAAO,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI;AAC/C;AACA,SAAS,iBAAiB;AACxB,MAAI,WAAW,UAAU,CAAC;AAC1B,YAAU,CAAC,IAAI;AACf,WAAS,MAAM,MAAM,SAAS;AAC9B,SAAO;AACT;AACA,SAAS,kBAAkB;AACzB,SAAO,MAAM,KAAK,IAAI;AACxB;AACA,SAAS,iBAAiB;AACxB,WAAS,SAAS,KAAK,SAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,EAAE,GAAG;AACpE,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC/D,UAAI,OAAO,MAAM,CAAC;AAClB,UAAI;AACF,eAAO;AAAA,IACX;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,iBAAiB;AACxB,MAAI,OAAO;AACX,aAAW,QAAQ;AACjB,MAAE;AACJ,SAAO;AACT;AACA,SAAS,kBAAkB;AACzB,SAAO,CAAC,KAAK,KAAK;AACpB;AACA,SAAS,eAAe,UAAU;AAChC,WAAS,SAAS,KAAK,SAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,EAAE,GAAG;AACpE,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,GAAG,IAAI,MAAM,QAAQ,MAAM,IAAI,GAAG,EAAE,GAAG;AACrE,UAAI,OAAO,MAAM,CAAC;AAChB,iBAAS,KAAK,MAAM,KAAK,UAAU,GAAG,KAAK;AAAA,IAC/C;AAAA,EACF;AACA,SAAO;AACT;AACA,SAAS,WAAW,MAAM;AACxB,SAAO,WAAW;AAChB,SAAK,gBAAgB,IAAI;AAAA,EAC3B;AACF;AACA,SAAS,aAAa,UAAU;AAC9B,SAAO,WAAW;AAChB,SAAK,kBAAkB,SAAS,OAAO,SAAS,KAAK;AAAA,EACvD;AACF;AACA,SAAS,aAAa,MAAM,OAAO;AACjC,SAAO,WAAW;AAChB,SAAK,aAAa,MAAM,KAAK;AAAA,EAC/B;AACF;AACA,SAAS,eAAe,UAAU,OAAO;AACvC,SAAO,WAAW;AAChB,SAAK,eAAe,SAAS,OAAO,SAAS,OAAO,KAAK;AAAA,EAC3D;AACF;AACA,SAAS,aAAa,MAAM,OAAO;AACjC,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,KAAK;AACP,WAAK,gBAAgB,IAAI;AAAA;AAEzB,WAAK,aAAa,MAAM,CAAC;AAAA,EAC7B;AACF;AACA,SAAS,eAAe,UAAU,OAAO;AACvC,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,KAAK;AACP,WAAK,kBAAkB,SAAS,OAAO,SAAS,KAAK;AAAA;AAErD,WAAK,eAAe,SAAS,OAAO,SAAS,OAAO,CAAC;AAAA,EACzD;AACF;AACA,SAAS,eAAe,MAAM,OAAO;AACnC,MAAI,WAAW,UAAU,IAAI;AAC7B,MAAI,UAAU,SAAS,GAAG;AACxB,QAAI,OAAO,KAAK,KAAK;AACrB,WAAO,SAAS,QAAQ,KAAK,eAAe,SAAS,OAAO,SAAS,KAAK,IAAI,KAAK,aAAa,QAAQ;AAAA,EAC1G;AACA,SAAO,KAAK,MAAM,SAAS,OAAO,SAAS,QAAQ,eAAe,aAAa,OAAO,UAAU,aAAa,SAAS,QAAQ,iBAAiB,eAAe,SAAS,QAAQ,iBAAiB,cAAc,UAAU,KAAK,CAAC;AAChO;AACA,SAAS,YAAY,MAAM;AACzB,SAAO,KAAK,iBAAiB,KAAK,cAAc,eAAe,KAAK,YAAY,QAAQ,KAAK;AAC/F;AACA,SAAS,YAAY,MAAM;AACzB,SAAO,WAAW;AAChB,SAAK,MAAM,eAAe,IAAI;AAAA,EAChC;AACF;AACA,SAAS,cAAc,MAAM,OAAO,UAAU;AAC5C,SAAO,WAAW;AAChB,SAAK,MAAM,YAAY,MAAM,OAAO,QAAQ;AAAA,EAC9C;AACF;AACA,SAAS,cAAc,MAAM,OAAO,UAAU;AAC5C,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,KAAK;AACP,WAAK,MAAM,eAAe,IAAI;AAAA;AAE9B,WAAK,MAAM,YAAY,MAAM,GAAG,QAAQ;AAAA,EAC5C;AACF;AACA,SAAS,gBAAgB,MAAM,OAAO,UAAU;AAC9C,SAAO,UAAU,SAAS,IAAI,KAAK,MAAM,SAAS,OAAO,cAAc,OAAO,UAAU,aAAa,gBAAgB,eAAe,MAAM,OAAO,YAAY,OAAO,KAAK,QAAQ,CAAC,IAAI,WAAW,KAAK,KAAK,GAAG,IAAI;AACpN;AACA,SAAS,WAAW,MAAM,MAAM;AAC9B,SAAO,KAAK,MAAM,iBAAiB,IAAI,KAAK,YAAY,IAAI,EAAE,iBAAiB,MAAM,IAAI,EAAE,iBAAiB,IAAI;AAClH;AACA,SAAS,eAAe,MAAM;AAC5B,SAAO,WAAW;AAChB,WAAO,KAAK,IAAI;AAAA,EAClB;AACF;AACA,SAAS,iBAAiB,MAAM,OAAO;AACrC,SAAO,WAAW;AAChB,SAAK,IAAI,IAAI;AAAA,EACf;AACF;AACA,SAAS,iBAAiB,MAAM,OAAO;AACrC,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,QAAI,KAAK;AACP,aAAO,KAAK,IAAI;AAAA;AAEhB,WAAK,IAAI,IAAI;AAAA,EACjB;AACF;AACA,SAAS,mBAAmB,MAAM,OAAO;AACvC,SAAO,UAAU,SAAS,IAAI,KAAK,MAAM,SAAS,OAAO,iBAAiB,OAAO,UAAU,aAAa,mBAAmB,kBAAkB,MAAM,KAAK,CAAC,IAAI,KAAK,KAAK,EAAE,IAAI;AAC/K;AACA,SAAS,WAAW,QAAQ;AAC1B,SAAO,OAAO,KAAK,EAAE,MAAM,OAAO;AACpC;AACA,SAAS,UAAU,MAAM;AACvB,SAAO,KAAK,aAAa,IAAI,UAAU,IAAI;AAC7C;AACA,SAAS,UAAU,MAAM;AACvB,OAAK,QAAQ;AACb,OAAK,SAAS,WAAW,KAAK,aAAa,OAAO,KAAK,EAAE;AAC3D;AACA,UAAU,YAAY;AAAA,EACpB,KAAK,SAAS,MAAM;AAClB,QAAI,IAAI,KAAK,OAAO,QAAQ,IAAI;AAChC,QAAI,IAAI,GAAG;AACT,WAAK,OAAO,KAAK,IAAI;AACrB,WAAK,MAAM,aAAa,SAAS,KAAK,OAAO,KAAK,GAAG,CAAC;AAAA,IACxD;AAAA,EACF;AAAA,EACA,QAAQ,SAAS,MAAM;AACrB,QAAI,IAAI,KAAK,OAAO,QAAQ,IAAI;AAChC,QAAI,KAAK,GAAG;AACV,WAAK,OAAO,OAAO,GAAG,CAAC;AACvB,WAAK,MAAM,aAAa,SAAS,KAAK,OAAO,KAAK,GAAG,CAAC;AAAA,IACxD;AAAA,EACF;AAAA,EACA,UAAU,SAAS,MAAM;AACvB,WAAO,KAAK,OAAO,QAAQ,IAAI,KAAK;AAAA,EACtC;AACF;AACA,SAAS,WAAW,MAAM,OAAO;AAC/B,MAAI,OAAO,UAAU,IAAI,GAAG,IAAI,IAAI,IAAI,MAAM;AAC9C,SAAO,EAAE,IAAI;AACX,SAAK,IAAI,MAAM,CAAC,CAAC;AACrB;AACA,SAAS,cAAc,MAAM,OAAO;AAClC,MAAI,OAAO,UAAU,IAAI,GAAG,IAAI,IAAI,IAAI,MAAM;AAC9C,SAAO,EAAE,IAAI;AACX,SAAK,OAAO,MAAM,CAAC,CAAC;AACxB;AACA,SAAS,YAAY,OAAO;AAC1B,SAAO,WAAW;AAChB,eAAW,MAAM,KAAK;AAAA,EACxB;AACF;AACA,SAAS,aAAa,OAAO;AAC3B,SAAO,WAAW;AAChB,kBAAc,MAAM,KAAK;AAAA,EAC3B;AACF;AACA,SAAS,gBAAgB,OAAO,OAAO;AACrC,SAAO,WAAW;AAChB,KAAC,MAAM,MAAM,MAAM,SAAS,IAAI,aAAa,eAAe,MAAM,KAAK;AAAA,EACzE;AACF;AACA,SAAS,kBAAkB,MAAM,OAAO;AACtC,MAAI,QAAQ,WAAW,OAAO,EAAE;AAChC,MAAI,UAAU,SAAS,GAAG;AACxB,QAAI,OAAO,UAAU,KAAK,KAAK,CAAC,GAAG,IAAI,IAAI,IAAI,MAAM;AACrD,WAAO,EAAE,IAAI;AACX,UAAI,CAAC,KAAK,SAAS,MAAM,CAAC,CAAC;AACzB,eAAO;AACX,WAAO;AAAA,EACT;AACA,SAAO,KAAK,MAAM,OAAO,UAAU,aAAa,kBAAkB,QAAQ,cAAc,cAAc,OAAO,KAAK,CAAC;AACrH;AACA,SAAS,aAAa;AACpB,OAAK,cAAc;AACrB;AACA,SAAS,aAAa,OAAO;AAC3B,SAAO,WAAW;AAChB,SAAK,cAAc;AAAA,EACrB;AACF;AACA,SAAS,aAAa,OAAO;AAC3B,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,SAAK,cAAc,KAAK,OAAO,KAAK;AAAA,EACtC;AACF;AACA,SAAS,eAAe,OAAO;AAC7B,SAAO,UAAU,SAAS,KAAK,KAAK,SAAS,OAAO,cAAc,OAAO,UAAU,aAAa,eAAe,cAAc,KAAK,CAAC,IAAI,KAAK,KAAK,EAAE;AACrJ;AACA,SAAS,aAAa;AACpB,OAAK,YAAY;AACnB;AACA,SAAS,aAAa,OAAO;AAC3B,SAAO,WAAW;AAChB,SAAK,YAAY;AAAA,EACnB;AACF;AACA,SAAS,aAAa,OAAO;AAC3B,SAAO,WAAW;AAChB,QAAI,IAAI,MAAM,MAAM,MAAM,SAAS;AACnC,SAAK,YAAY,KAAK,OAAO,KAAK;AAAA,EACpC;AACF;AACA,SAAS,eAAe,OAAO;AAC7B,SAAO,UAAU,SAAS,KAAK,KAAK,SAAS,OAAO,cAAc,OAAO,UAAU,aAAa,eAAe,cAAc,KAAK,CAAC,IAAI,KAAK,KAAK,EAAE;AACrJ;AACA,SAAS,QAAQ;AACf,MAAI,KAAK;AACP,SAAK,WAAW,YAAY,IAAI;AACpC;AACA,SAAS,kBAAkB;AACzB,SAAO,KAAK,KAAK,KAAK;AACxB;AACA,SAAS,QAAQ;AACf,MAAI,KAAK;AACP,SAAK,WAAW,aAAa,MAAM,KAAK,WAAW,UAAU;AACjE;AACA,SAAS,kBAAkB;AACzB,SAAO,KAAK,KAAK,KAAK;AACxB;AACA,SAAS,iBAAiB,MAAM;AAC9B,MAAI,SAAS,OAAO,SAAS,aAAa,OAAO,QAAQ,IAAI;AAC7D,SAAO,KAAK,OAAO,WAAW;AAC5B,WAAO,KAAK,YAAY,OAAO,MAAM,MAAM,SAAS,CAAC;AAAA,EACvD,CAAC;AACH;AACA,SAAS,eAAe;AACtB,SAAO;AACT;AACA,SAAS,iBAAiB,MAAM,QAAQ;AACtC,MAAI,SAAS,OAAO,SAAS,aAAa,OAAO,QAAQ,IAAI,GAAG,UAAU,UAAU,OAAO,eAAe,OAAO,WAAW,aAAa,SAAS,SAAS,MAAM;AACjK,SAAO,KAAK,OAAO,WAAW;AAC5B,WAAO,KAAK,aAAa,OAAO,MAAM,MAAM,SAAS,GAAG,QAAQ,MAAM,MAAM,SAAS,KAAK,IAAI;AAAA,EAChG,CAAC;AACH;AACA,SAAS,SAAS;AAChB,MAAI,SAAS,KAAK;AAClB,MAAI;AACF,WAAO,YAAY,IAAI;AAC3B;AACA,SAAS,mBAAmB;AAC1B,SAAO,KAAK,KAAK,MAAM;AACzB;AACA,SAAS,yBAAyB;AAChC,MAAI,QAAQ,KAAK,UAAU,KAAK,GAAG,SAAS,KAAK;AACjD,SAAO,SAAS,OAAO,aAAa,OAAO,KAAK,WAAW,IAAI;AACjE;AACA,SAAS,sBAAsB;AAC7B,MAAI,QAAQ,KAAK,UAAU,IAAI,GAAG,SAAS,KAAK;AAChD,SAAO,SAAS,OAAO,aAAa,OAAO,KAAK,WAAW,IAAI;AACjE;AACA,SAAS,gBAAgB,MAAM;AAC7B,SAAO,KAAK,OAAO,OAAO,sBAAsB,sBAAsB;AACxE;AACA,SAAS,gBAAgB,OAAO;AAC9B,SAAO,UAAU,SAAS,KAAK,SAAS,YAAY,KAAK,IAAI,KAAK,KAAK,EAAE;AAC3E;AACA,SAAS,gBAAgB,UAAU;AACjC,SAAO,SAAS,OAAO;AACrB,aAAS,KAAK,MAAM,OAAO,KAAK,QAAQ;AAAA,EAC1C;AACF;AACA,SAAS,iBAAiB,WAAW;AACnC,SAAO,UAAU,KAAK,EAAE,MAAM,OAAO,EAAE,IAAI,SAAS,GAAG;AACrD,QAAI,OAAO,IAAI,IAAI,EAAE,QAAQ,GAAG;AAChC,QAAI,KAAK;AACP,aAAO,EAAE,MAAM,IAAI,CAAC,GAAG,IAAI,EAAE,MAAM,GAAG,CAAC;AACzC,WAAO,EAAE,MAAM,GAAG,KAAK;AAAA,EACzB,CAAC;AACH;AACA,SAAS,SAAS,UAAU;AAC1B,SAAO,WAAW;AAChB,QAAI,KAAK,KAAK;AACd,QAAI,CAAC;AACH;AACF,aAAS,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,QAAQ,GAAG,IAAI,GAAG,EAAE,GAAG;AACpD,UAAI,IAAI,GAAG,CAAC,IAAI,CAAC,SAAS,QAAQ,EAAE,SAAS,SAAS,SAAS,EAAE,SAAS,SAAS,MAAM;AACvF,aAAK,oBAAoB,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO;AAAA,MACxD,OAAO;AACL,WAAG,EAAE,CAAC,IAAI;AAAA,MACZ;AAAA,IACF;AACA,QAAI,EAAE;AACJ,SAAG,SAAS;AAAA;AAEZ,aAAO,KAAK;AAAA,EAChB;AACF;AACA,SAAS,MAAM,UAAU,OAAO,SAAS;AACvC,SAAO,WAAW;AAChB,QAAI,KAAK,KAAK,MAAM,GAAG,WAAW,gBAAgB,KAAK;AACvD,QAAI;AACF,eAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,IAAI,GAAG,EAAE,GAAG;AACzC,aAAK,IAAI,GAAG,CAAC,GAAG,SAAS,SAAS,QAAQ,EAAE,SAAS,SAAS,MAAM;AAClE,eAAK,oBAAoB,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO;AACtD,eAAK,iBAAiB,EAAE,MAAM,EAAE,WAAW,UAAU,EAAE,UAAU,OAAO;AACxE,YAAE,QAAQ;AACV;AAAA,QACF;AAAA,MACF;AACF,SAAK,iBAAiB,SAAS,MAAM,UAAU,OAAO;AACtD,QAAI,EAAE,MAAM,SAAS,MAAM,MAAM,SAAS,MAAM,OAAO,UAAU,QAAQ;AACzE,QAAI,CAAC;AACH,WAAK,OAAO,CAAC,CAAC;AAAA;AAEd,SAAG,KAAK,CAAC;AAAA,EACb;AACF;AACA,SAAS,aAAa,UAAU,OAAO,SAAS;AAC9C,MAAI,YAAY,iBAAiB,WAAW,EAAE,GAAG,GAAG,IAAI,UAAU,QAAQ;AAC1E,MAAI,UAAU,SAAS,GAAG;AACxB,QAAI,KAAK,KAAK,KAAK,EAAE;AACrB,QAAI;AACF,eAAS,IAAI,GAAG,IAAI,GAAG,QAAQ,GAAG,IAAI,GAAG,EAAE,GAAG;AAC5C,aAAK,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG;AACjC,eAAK,IAAI,UAAU,CAAC,GAAG,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM;AAC3D,mBAAO,EAAE;AAAA,UACX;AAAA,QACF;AAAA,MACF;AACF;AAAA,EACF;AACA,OAAK,QAAQ,QAAQ;AACrB,OAAK,IAAI,GAAG,IAAI,GAAG,EAAE;AACnB,SAAK,KAAK,GAAG,UAAU,CAAC,GAAG,OAAO,OAAO,CAAC;AAC5C,SAAO;AACT;AACA,SAAS,cAAc,MAAM,MAAM,QAAQ;AACzC,MAAI,SAAS,YAAY,IAAI,GAAG,QAAQ,OAAO;AAC/C,MAAI,OAAO,UAAU,YAAY;AAC/B,YAAQ,IAAI,MAAM,MAAM,MAAM;AAAA,EAChC,OAAO;AACL,YAAQ,OAAO,SAAS,YAAY,OAAO;AAC3C,QAAI;AACF,YAAM,UAAU,MAAM,OAAO,SAAS,OAAO,UAAU,GAAG,MAAM,SAAS,OAAO;AAAA;AAEhF,YAAM,UAAU,MAAM,OAAO,KAAK;AAAA,EACtC;AACA,OAAK,cAAc,KAAK;AAC1B;AACA,SAAS,iBAAiB,MAAM,QAAQ;AACtC,SAAO,WAAW;AAChB,WAAO,cAAc,MAAM,MAAM,MAAM;AAAA,EACzC;AACF;AACA,SAAS,iBAAiB,MAAM,QAAQ;AACtC,SAAO,WAAW;AAChB,WAAO,cAAc,MAAM,MAAM,OAAO,MAAM,MAAM,SAAS,CAAC;AAAA,EAChE;AACF;AACA,SAAS,mBAAmB,MAAM,QAAQ;AACxC,SAAO,KAAK,MAAM,OAAO,WAAW,aAAa,mBAAmB,kBAAkB,MAAM,MAAM,CAAC;AACrG;AACA,UAAU,qBAAqB;AAC7B,WAAS,SAAS,KAAK,SAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,EAAE,GAAG;AACpE,aAAS,QAAQ,OAAO,CAAC,GAAG,IAAI,GAAG,IAAI,MAAM,QAAQ,MAAM,IAAI,GAAG,EAAE,GAAG;AACrE,UAAI,OAAO,MAAM,CAAC;AAChB,cAAM;AAAA,IACV;AAAA,EACF;AACF;AACA,IAAI,OAAO,CAAC,IAAI;AAChB,SAAS,UAAU,QAAQ,SAAS;AAClC,OAAK,UAAU;AACf,OAAK,WAAW;AAClB;AACA,SAAS,sBAAsB;AAC7B,SAAO;AACT;AACA,UAAU,YAAY;AAAA,EACpB,aAAa;AAAA,EACb,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,aAAa;AAAA,EACb,gBAAgB;AAAA,EAChB,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,WAAW;AAAA,EACX,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,UAAU;AAAA,EACV,SAAS;AAAA,EACT,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,OAAO;AAAA,EACP,IAAI;AAAA,EACJ,UAAU;AAAA,EACV,CAAC,OAAO,QAAQ,GAAG;AACrB;AACA,SAAS,OAAO,WAAW;AACzB,SAAO,OAAO,cAAc,WAAW,IAAI,UAAU,CAAC,CAAC,SAAS,cAAc,SAAS,CAAC,CAAC,GAAG,CAAC,SAAS,eAAe,CAAC,IAAI,IAAI,UAAU,CAAC,CAAC,SAAS,CAAC,GAAG,IAAI;AAC7J;AACA,SAAS,YAAY,OAAO;AAC1B,MAAI;AACJ,SAAO,eAAe,MAAM;AAC1B,YAAQ;AACV,SAAO;AACT;AACA,SAAS,QAAQ,OAAO,MAAM;AAC5B,UAAQ,YAAY,KAAK;AACzB,MAAI,SAAS;AACX,WAAO,MAAM;AACf,MAAI,MAAM;AACR,QAAI,MAAM,KAAK,mBAAmB;AAClC,QAAI,IAAI,gBAAgB;AACtB,UAAI,QAAQ,IAAI,eAAe;AAC/B,YAAM,IAAI,MAAM,SAAS,MAAM,IAAI,MAAM;AACzC,cAAQ,MAAM,gBAAgB,KAAK,aAAa,EAAE,QAAQ,CAAC;AAC3D,aAAO,CAAC,MAAM,GAAG,MAAM,CAAC;AAAA,IAC1B;AACA,QAAI,KAAK,uBAAuB;AAC9B,UAAI,OAAO,KAAK,sBAAsB;AACtC,aAAO,CAAC,MAAM,UAAU,KAAK,OAAO,KAAK,YAAY,MAAM,UAAU,KAAK,MAAM,KAAK,SAAS;AAAA,IAChG;AAAA,EACF;AACA,SAAO,CAAC,MAAM,OAAO,MAAM,KAAK;AAClC;AACA,IAAI,OAAO,EAAE,OAAO,MAAM;AAC1B,EAAE;AACF,SAAS,WAAW;AAClB,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG,EAAE,GAAG;AAC3D,QAAI,EAAE,IAAI,UAAU,CAAC,IAAI,OAAO,KAAK,KAAK,QAAQ,KAAK,CAAC;AACtD,YAAM,IAAI,MAAM,mBAAmB,CAAC;AACtC,MAAE,CAAC,IAAI,CAAC;AAAA,EACV;AACA,SAAO,IAAI,SAAS,CAAC;AACvB;AACA,SAAS,SAAS,GAAG;AACnB,OAAK,IAAI;AACX;AACA,SAAS,eAAe,WAAW,OAAO;AACxC,SAAO,UAAU,KAAK,EAAE,MAAM,OAAO,EAAE,IAAI,SAAS,GAAG;AACrD,QAAI,OAAO,IAAI,IAAI,EAAE,QAAQ,GAAG;AAChC,QAAI,KAAK;AACP,aAAO,EAAE,MAAM,IAAI,CAAC,GAAG,IAAI,EAAE,MAAM,GAAG,CAAC;AACzC,QAAI,KAAK,CAAC,MAAM,eAAe,CAAC;AAC9B,YAAM,IAAI,MAAM,mBAAmB,CAAC;AACtC,WAAO,EAAE,MAAM,GAAG,KAAK;AAAA,EACzB,CAAC;AACH;AACA,SAAS,YAAY,SAAS,YAAY;AAAA,EACxC,aAAa;AAAA,EACb,IAAI,SAAS,UAAU,UAAU;AAC/B,QAAI,IAAI,KAAK,GAAG,IAAI,eAAe,WAAW,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,IAAI,EAAE;AACvE,QAAI,UAAU,SAAS,GAAG;AACxB,aAAO,EAAE,IAAI;AACX,aAAK,KAAK,WAAW,EAAE,CAAC,GAAG,UAAU,IAAI,IAAI,EAAE,CAAC,GAAG,SAAS,IAAI;AAC9D,iBAAO;AACX;AAAA,IACF;AACA,QAAI,YAAY,QAAQ,OAAO,aAAa;AAC1C,YAAM,IAAI,MAAM,uBAAuB,QAAQ;AACjD,WAAO,EAAE,IAAI,GAAG;AACd,UAAI,KAAK,WAAW,EAAE,CAAC,GAAG;AACxB,UAAE,CAAC,IAAI,IAAI,EAAE,CAAC,GAAG,SAAS,MAAM,QAAQ;AAAA,eACjC,YAAY;AACnB,aAAK,KAAK;AACR,YAAE,CAAC,IAAI,IAAI,EAAE,CAAC,GAAG,SAAS,MAAM,IAAI;AAAA,IAC1C;AACA,WAAO;AAAA,EACT;AAAA,EACA,MAAM,WAAW;AACf,QAAI,OAAO,CAAC,GAAG,IAAI,KAAK;AACxB,aAAS,KAAK;AACZ,WAAK,CAAC,IAAI,EAAE,CAAC,EAAE,MAAM;AACvB,WAAO,IAAI,SAAS,IAAI;AAAA,EAC1B;AAAA,EACA,MAAM,SAAS,MAAM,MAAM;AACzB,SAAK,IAAI,UAAU,SAAS,KAAK;AAC/B,eAAS,OAAO,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,EAAE;AAClD,aAAK,CAAC,IAAI,UAAU,IAAI,CAAC;AAC7B,QAAI,CAAC,KAAK,EAAE,eAAe,IAAI;AAC7B,YAAM,IAAI,MAAM,mBAAmB,IAAI;AACzC,SAAK,IAAI,KAAK,EAAE,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAI,GAAG,EAAE;AACnD,QAAE,CAAC,EAAE,MAAM,MAAM,MAAM,IAAI;AAAA,EAC/B;AAAA,EACA,OAAO,SAAS,MAAM,MAAM,MAAM;AAChC,QAAI,CAAC,KAAK,EAAE,eAAe,IAAI;AAC7B,YAAM,IAAI,MAAM,mBAAmB,IAAI;AACzC,aAAS,IAAI,KAAK,EAAE,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE,QAAQ,IAAI,GAAG,EAAE;AACvD,QAAE,CAAC,EAAE,MAAM,MAAM,MAAM,IAAI;AAAA,EAC/B;AACF;AACA,SAAS,IAAI,MAAM,MAAM;AACvB,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,GAAG,IAAI,GAAG,EAAE,GAAG;AAC9C,SAAK,IAAI,KAAK,CAAC,GAAG,SAAS,MAAM;AAC/B,aAAO,EAAE;AAAA,IACX;AAAA,EACF;AACF;AACA,SAAS,IAAI,MAAM,MAAM,UAAU;AACjC,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC3C,QAAI,KAAK,CAAC,EAAE,SAAS,MAAM;AACzB,WAAK,CAAC,IAAI,MAAM,OAAO,KAAK,MAAM,GAAG,CAAC,EAAE,OAAO,KAAK,MAAM,IAAI,CAAC,CAAC;AAChE;AAAA,IACF;AAAA,EACF;AACA,MAAI,YAAY;AACd,SAAK,KAAK,EAAE,MAAM,OAAO,SAAS,CAAC;AACrC,SAAO;AACT;AACA,IAAM,aAAa,EAAE,SAAS,MAAM;AACpC,IAAM,oBAAoB,EAAE,SAAS,MAAM,SAAS,MAAM;AAC1D,SAAS,cAAc,OAAO;AAC5B,QAAM,yBAAyB;AACjC;AACA,SAAS,QAAQ,OAAO;AACtB,QAAM,eAAe;AACrB,QAAM,yBAAyB;AACjC;AACA,SAAS,OAAO,MAAM;AACpB,MAAI,QAAQ,KAAK,SAAS,iBAAiB,YAAY,OAAO,IAAI,EAAE,GAAG,kBAAkB,SAAS,iBAAiB;AACnH,MAAI,mBAAmB,OAAO;AAC5B,cAAU,GAAG,oBAAoB,SAAS,iBAAiB;AAAA,EAC7D,OAAO;AACL,UAAM,aAAa,MAAM,MAAM;AAC/B,UAAM,MAAM,gBAAgB;AAAA,EAC9B;AACF;AACA,SAAS,QAAQ,MAAM,SAAS;AAC9B,MAAI,QAAQ,KAAK,SAAS,iBAAiB,YAAY,OAAO,IAAI,EAAE,GAAG,kBAAkB,IAAI;AAC7F,MAAI,SAAS;AACX,cAAU,GAAG,cAAc,SAAS,iBAAiB;AACrD,eAAW,WAAW;AACpB,gBAAU,GAAG,cAAc,IAAI;AAAA,IACjC,GAAG,CAAC;AAAA,EACN;AACA,MAAI,mBAAmB,OAAO;AAC5B,cAAU,GAAG,oBAAoB,IAAI;AAAA,EACvC,OAAO;AACL,UAAM,MAAM,gBAAgB,MAAM;AAClC,WAAO,MAAM;AAAA,EACf;AACF;AACA,IAAM,WAAW,CAAC,MAAM,MAAM;AAC9B,SAAS,UAAU,MAAM;AAAA,EACvB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,UAAU;AACZ,GAAG;AACD,SAAO,iBAAiB,MAAM;AAAA,IAC5B,MAAM,EAAE,OAAO,MAAM,YAAY,MAAM,cAAc,KAAK;AAAA,IAC1D,aAAa,EAAE,OAAO,cAAc,YAAY,MAAM,cAAc,KAAK;AAAA,IACzE,SAAS,EAAE,OAAO,SAAS,YAAY,MAAM,cAAc,KAAK;AAAA,IAChE,QAAQ,EAAE,OAAO,QAAQ,YAAY,MAAM,cAAc,KAAK;AAAA,IAC9D,YAAY,EAAE,OAAO,YAAY,YAAY,MAAM,cAAc,KAAK;AAAA,IACtE,QAAQ,EAAE,OAAO,QAAQ,YAAY,MAAM,cAAc,KAAK;AAAA,IAC9D,GAAG,EAAE,OAAO,GAAG,YAAY,MAAM,cAAc,KAAK;AAAA,IACpD,GAAG,EAAE,OAAO,GAAG,YAAY,MAAM,cAAc,KAAK;AAAA,IACpD,IAAI,EAAE,OAAO,IAAI,YAAY,MAAM,cAAc,KAAK;AAAA,IACtD,IAAI,EAAE,OAAO,IAAI,YAAY,MAAM,cAAc,KAAK;AAAA,IACtD,GAAG,EAAE,OAAO,UAAU;AAAA,EACxB,CAAC;AACH;AACA,UAAU,UAAU,KAAK,WAAW;AAClC,MAAI,QAAQ,KAAK,EAAE,GAAG,MAAM,KAAK,GAAG,SAAS;AAC7C,SAAO,UAAU,KAAK,IAAI,OAAO;AACnC;AACA,SAAS,cAAc,OAAO;AAC5B,SAAO,CAAC,MAAM,WAAW,CAAC,MAAM;AAClC;AACA,SAAS,mBAAmB;AAC1B,SAAO,KAAK;AACd;AACA,SAAS,eAAe,OAAO,GAAG;AAChC,SAAO,KAAK,OAAO,EAAE,GAAG,MAAM,GAAG,GAAG,MAAM,EAAE,IAAI;AAClD;AACA,SAAS,mBAAmB;AAC1B,SAAO,UAAU,kBAAkB,kBAAkB;AACvD;AACA,SAAS,OAAO;AACd,MAAI,UAAU,eAAe,YAAY,kBAAkB,UAAU,gBAAgB,YAAY,kBAAkB,WAAW,CAAC,GAAG,YAAY,SAAS,SAAS,QAAQ,KAAK,GAAG,SAAS,GAAG,YAAY,YAAY,aAAa,aAAa,iBAAiB;AAC/P,WAAS,MAAM,WAAW;AACxB,cAAU,GAAG,kBAAkB,WAAW,EAAE,OAAO,SAAS,EAAE,GAAG,mBAAmB,YAAY,EAAE,GAAG,kBAAkB,YAAY,UAAU,EAAE,GAAG,kCAAkC,UAAU,EAAE,MAAM,gBAAgB,MAAM,EAAE,MAAM,+BAA+B,eAAe;AAAA,EACpR;AACA,WAAS,YAAY,OAAO,GAAG;AAC7B,QAAI,eAAe,CAAC,QAAQ,KAAK,MAAM,OAAO,CAAC;AAC7C;AACF,QAAI,UAAU,YAAY,MAAM,UAAU,KAAK,MAAM,OAAO,CAAC,GAAG,OAAO,GAAG,OAAO;AACjF,QAAI,CAAC;AACH;AACF,WAAO,MAAM,IAAI,EAAE,GAAG,kBAAkB,YAAY,iBAAiB,EAAE,GAAG,gBAAgB,YAAY,iBAAiB;AACvH,WAAO,MAAM,IAAI;AACjB,kBAAc,KAAK;AACnB,kBAAc;AACd,iBAAa,MAAM;AACnB,iBAAa,MAAM;AACnB,YAAQ,SAAS,KAAK;AAAA,EACxB;AACA,WAAS,WAAW,OAAO;AACzB,YAAQ,KAAK;AACb,QAAI,CAAC,aAAa;AAChB,UAAI,KAAK,MAAM,UAAU,YAAY,KAAK,MAAM,UAAU;AAC1D,oBAAc,KAAK,KAAK,KAAK,KAAK;AAAA,IACpC;AACA,aAAS,MAAM,QAAQ,KAAK;AAAA,EAC9B;AACA,WAAS,WAAW,OAAO;AACzB,WAAO,MAAM,IAAI,EAAE,GAAG,+BAA+B,IAAI;AACzD,YAAQ,MAAM,MAAM,WAAW;AAC/B,YAAQ,KAAK;AACb,aAAS,MAAM,OAAO,KAAK;AAAA,EAC7B;AACA,WAAS,aAAa,OAAO,GAAG;AAC9B,QAAI,CAAC,QAAQ,KAAK,MAAM,OAAO,CAAC;AAC9B;AACF,QAAI,UAAU,MAAM,gBAAgB,IAAI,UAAU,KAAK,MAAM,OAAO,CAAC,GAAG,IAAI,QAAQ,QAAQ,GAAG;AAC/F,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,UAAI,UAAU,YAAY,MAAM,GAAG,OAAO,GAAG,QAAQ,CAAC,EAAE,YAAY,QAAQ,CAAC,CAAC,GAAG;AAC/E,sBAAc,KAAK;AACnB,gBAAQ,SAAS,OAAO,QAAQ,CAAC,CAAC;AAAA,MACpC;AAAA,IACF;AAAA,EACF;AACA,WAAS,WAAW,OAAO;AACzB,QAAI,UAAU,MAAM,gBAAgB,IAAI,QAAQ,QAAQ,GAAG;AAC3D,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,UAAI,UAAU,SAAS,QAAQ,CAAC,EAAE,UAAU,GAAG;AAC7C,gBAAQ,KAAK;AACb,gBAAQ,QAAQ,OAAO,QAAQ,CAAC,CAAC;AAAA,MACnC;AAAA,IACF;AAAA,EACF;AACA,WAAS,WAAW,OAAO;AACzB,QAAI,UAAU,MAAM,gBAAgB,IAAI,QAAQ,QAAQ,GAAG;AAC3D,QAAI;AACF,mBAAa,WAAW;AAC1B,kBAAc,WAAW,WAAW;AAClC,oBAAc;AAAA,IAChB,GAAG,GAAG;AACN,SAAK,IAAI,GAAG,IAAI,GAAG,EAAE,GAAG;AACtB,UAAI,UAAU,SAAS,QAAQ,CAAC,EAAE,UAAU,GAAG;AAC7C,sBAAc,KAAK;AACnB,gBAAQ,OAAO,OAAO,QAAQ,CAAC,CAAC;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AACA,WAAS,YAAY,MAAM,YAAY,OAAO,GAAG,YAAY,OAAO;AAClE,QAAI,YAAY,UAAU,KAAK,GAAG,IAAI,QAAQ,SAAS,OAAO,UAAU,GAAG,IAAI,IAAI;AACnF,SAAK,IAAI,QAAQ,KAAK,MAAM,IAAI,UAAU,eAAe;AAAA,MACvD,aAAa;AAAA,MACb,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,MACA,GAAG,EAAE,CAAC;AAAA,MACN,GAAG,EAAE,CAAC;AAAA,MACN,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,UAAU;AAAA,IACZ,CAAC,GAAG,CAAC,MAAM;AACT;AACF,SAAK,EAAE,IAAI,EAAE,CAAC,KAAK;AACnB,SAAK,EAAE,IAAI,EAAE,CAAC,KAAK;AACnB,WAAO,SAAS,QAAQ,MAAM,QAAQ,QAAQ;AAC5C,UAAI,KAAK,GAAG;AACZ,cAAQ,MAAM;AAAA,QACZ,KAAK;AACH,mBAAS,UAAU,IAAI,SAAS,IAAI;AACpC;AAAA,QACF,KAAK;AACH,iBAAO,SAAS,UAAU,GAAG,EAAE;AAAA,QACjC,KAAK;AACH,cAAI,QAAQ,UAAU,QAAQ,UAAU,GAAG,IAAI;AAC/C;AAAA,MACJ;AACA,gBAAU;AAAA,QACR;AAAA,QACA;AAAA,QACA,IAAI,UAAU,MAAM;AAAA,UAClB,aAAa;AAAA,UACb,SAAS;AAAA,UACT,QAAQ;AAAA,UACR;AAAA,UACA,QAAQ;AAAA,UACR,GAAG,EAAE,CAAC,IAAI;AAAA,UACV,GAAG,EAAE,CAAC,IAAI;AAAA,UACV,IAAI,EAAE,CAAC,IAAI,GAAG,CAAC;AAAA,UACf,IAAI,EAAE,CAAC,IAAI,GAAG,CAAC;AAAA,UACf,UAAU;AAAA,QACZ,CAAC;AAAA,QACD;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,QAAM,SAAS,SAAS,GAAG;AACzB,WAAO,UAAU,UAAU,UAAU,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS;AAAA,EAC7F;AACA,QAAM,YAAY,SAAS,GAAG;AAC5B,WAAO,UAAU,UAAU,YAAY,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,GAAG,SAAS;AAAA,EAC7F;AACA,QAAM,UAAU,SAAS,GAAG;AAC1B,WAAO,UAAU,UAAU,UAAU,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,GAAG,SAAS;AAAA,EAC3F;AACA,QAAM,YAAY,SAAS,GAAG;AAC5B,WAAO,UAAU,UAAU,YAAY,OAAO,MAAM,aAAa,IAAI,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS;AAAA,EAC/F;AACA,QAAM,KAAK,WAAW;AACpB,QAAI,QAAQ,UAAU,GAAG,MAAM,WAAW,SAAS;AACnD,WAAO,UAAU,YAAY,QAAQ;AAAA,EACvC;AACA,QAAM,gBAAgB,SAAS,GAAG;AAChC,WAAO,UAAU,UAAU,kBAAkB,IAAI,CAAC,KAAK,GAAG,SAAS,KAAK,KAAK,cAAc;AAAA,EAC7F;AACA,SAAO;AACT;AACA,IAAI,wBAAwC,CAAC,0BAA0B;AACrE,wBAAsB,MAAM,IAAI;AAChC,wBAAsB,QAAQ,IAAI;AAClC,SAAO;AACT,GAAG,wBAAwB,CAAC,CAAC;AAC7B,SAAS,aAAa,EAAE,OAAO,WAAW,QAAQ,YAAY,SAAS,QAAQ,GAAG;AAChF,QAAM,aAAa,QAAQ;AAC3B,QAAM,cAAc,SAAS;AAC7B,QAAM,YAAY,CAAC,aAAa,IAAI,IAAI,aAAa,IAAI,KAAK,GAAG,cAAc,IAAI,IAAI,cAAc,IAAI,KAAK,CAAC;AAC/G,MAAI,cAAc,SAAS;AACzB,cAAU,CAAC,IAAI,UAAU,CAAC,IAAI;AAAA,EAChC;AACA,MAAI,eAAe,SAAS;AAC1B,cAAU,CAAC,IAAI,UAAU,CAAC,IAAI;AAAA,EAChC;AACA,SAAO;AACT;AACA,IAAM,mBAAmB;AAAA,EACvB,CAAC,qBAAqB,IAAI,GAAG;AAAA,EAC7B,CAAC,qBAAqB,MAAM,GAAG;AACjC;AACA,IAAM,kBAAkB;AAAA,EACtB,CAAC,qBAAqB,IAAI,GAAG;AAAA,EAC7B,CAAC,qBAAqB,MAAM,GAAG;AACjC;AACA,IAAM,gBAAgB;AAAA,EACpB,MAAM;AAAA,EACN,cAAc,EAAE,MAAM,EAAE;AAC1B;AACA,IAAM,cAA8B,gBAAgB;AAAA,EAClD,GAAG;AAAA,EACH,OAAO;AAAA,IACL,QAAQ,CAAC;AAAA,IACT,OAAO,CAAC;AAAA,IACR,UAAU,EAAE,SAAS,GAAG;AAAA,IACxB,WAAW,EAAE,SAAS,GAAG;AAAA,IACzB,UAAU,EAAE,SAAS,OAAO,UAAU;AAAA,IACtC,WAAW,EAAE,SAAS,OAAO,UAAU;AAAA,IACvC,UAAU,CAAC;AAAA,IACX,SAAS,EAAE,SAAS,SAAS;AAAA,IAC7B,cAAc,CAAC;AAAA,IACf,iBAAiB,EAAE,MAAM,CAAC,SAAS,MAAM,GAAG,SAAS,MAAM;AAAA,EAC7D;AAAA,EACA,OAAO,CAAC,eAAe,UAAU,WAAW;AAAA,EAC5C,MAAM,SAAS,EAAE,MAAM,MAAM,GAAG;AAC9B,UAAM,QAAQ;AACd,UAAM,iBAAiB,EAAE,OAAO,GAAG,QAAQ,GAAG,GAAG,GAAG,GAAG,EAAE;AACzD,UAAM,kBAAkB;AAAA,MACtB,GAAG;AAAA,MACH,UAAU;AAAA,MACV,UAAU;AAAA,MACV,aAAa;AAAA,IACf;AACA,UAAM,EAAE,UAAU,OAAO,aAAa,IAAI,WAAW;AACrD,UAAM,qBAAqB,sBAAsB;AACjD,UAAM,mBAAmB,IAAI;AAC7B,QAAI,cAAc;AAClB,QAAI,aAAa;AACjB,UAAM,kBAAkB,MAAM,MAAM,MAAM,YAAY,iBAAiB,MAAM,OAAO,CAAC;AACrF,UAAM,qBAAqB,MAAM,MAAM,gBAAgB,MAAM,MAAM,GAAG,CAAC;AACvE,UAAM,eAAe,MAAM,MAAM,MAAM,QAAQ,EAAE,CAAC,gBAAgB,MAAM,OAAO,CAAC,GAAG,MAAM,MAAM,IAAI,CAAC,CAAC;AACrG,gBAAY,CAAC,cAAc;AACzB,UAAI,CAAC,iBAAiB,SAAS,CAAC,MAAM,QAAQ;AAC5C;AAAA,MACF;AACA,YAAM,YAAY,OAAO,iBAAiB,KAAK;AAC/C,YAAM,UAAU,gBAAgB,MAAM,SAAS,OAAO,KAAK,gBAAgB,MAAM,SAAS,MAAM;AAChG,YAAM,UAAU,gBAAgB,MAAM,SAAS,QAAQ,KAAK,gBAAgB,MAAM,SAAS,KAAK;AAChG,YAAM,UAAU,gBAAgB,MAAM,SAAS,MAAM;AACrD,YAAM,UAAU,gBAAgB,MAAM,SAAS,KAAK;AACpD,YAAM,cAAc,KAAK,EAAE,GAAG,SAAS,CAAC,UAAU;AAChD,cAAM,OAAO,SAAS,MAAM,MAAM;AAClC,cAAM,EAAE,UAAU,SAAS,IAAI,mBAAmB,KAAK;AACvD,qBAAa;AAAA,UACX,QAAQ,QAAQ,OAAO,SAAS,KAAK,WAAW,UAAU;AAAA,UAC1D,SAAS,QAAQ,OAAO,SAAS,KAAK,WAAW,WAAW;AAAA,UAC5D,IAAI,QAAQ,OAAO,SAAS,KAAK,SAAS,MAAM;AAAA,UAChD,IAAI,QAAQ,OAAO,SAAS,KAAK,SAAS,MAAM;AAAA,QAClD;AACA,sBAAc;AAAA,UACZ,GAAG;AAAA,UACH,UAAU;AAAA,UACV,UAAU;AAAA,UACV,aAAa,WAAW,QAAQ,WAAW;AAAA,QAC7C;AACA,cAAM,eAAe,EAAE,OAAO,QAAQ,WAAW,CAAC;AAAA,MACpD,CAAC,EAAE,GAAG,QAAQ,CAAC,UAAU;AACvB,YAAI;AACJ,cAAM,EAAE,UAAU,SAAS,IAAI,mBAAmB,KAAK;AACvD,cAAM,OAAO,SAAS,MAAM,MAAM;AAClC,YAAI,MAAM;AACR,gBAAM,UAAU,CAAC;AACjB,gBAAM;AAAA,YACJ,UAAU;AAAA,YACV,UAAU;AAAA,YACV,OAAO;AAAA,YACP,QAAQ;AAAA,YACR,GAAG;AAAA,YACH,GAAG;AAAA,YACH,aAAa;AAAA,UACf,IAAI;AACJ,gBAAM,EAAE,GAAG,OAAO,GAAG,OAAO,OAAO,WAAW,QAAQ,WAAW,IAAI;AACrE,gBAAM,QAAQ,KAAK,MAAM,UAAU,WAAW,SAAS,CAAC;AACxD,gBAAM,QAAQ,KAAK,MAAM,UAAU,WAAW,SAAS,CAAC;AACxD,cAAI,QAAQ,MAAM,cAAc,UAAU,CAAC,QAAQ,QAAQ,MAAM,UAAU,MAAM,QAAQ;AACzF,cAAI,SAAS,MAAM,eAAe,UAAU,CAAC,QAAQ,QAAQ,MAAM,WAAW,MAAM,SAAS;AAC7F,cAAI,MAAM,iBAAiB;AACzB,kBAAM,kBAAkB,QAAQ;AAChC,gBAAI,cAAc;AAClB,gBAAI,OAAO,MAAM,oBAAoB,YAAY,oBAAoB,MAAM,iBAAiB;AAC1F,4BAAc,MAAM;AAAA,YACtB;AACA,kBAAM,aAAa,WAAW;AAC9B,kBAAM,eAAe,WAAW,CAAC;AACjC,kBAAM,aAAa,WAAW,CAAC;AAC/B,oBAAQ,mBAAmB,eAAe,cAAc,aAAa,SAAS,cAAc;AAC5F,qBAAS,kBAAkB,eAAe,cAAc,eAAe,QAAQ,cAAc;AAC7F,gBAAI,SAAS,MAAM,UAAU;AAC3B,sBAAQ,MAAM;AACd,uBAAS,MAAM,WAAW;AAAA,YAC5B,WAAW,SAAS,MAAM,UAAU;AAClC,sBAAQ,MAAM;AACd,uBAAS,MAAM,WAAW;AAAA,YAC5B;AACA,gBAAI,UAAU,MAAM,WAAW;AAC7B,uBAAS,MAAM;AACf,sBAAQ,MAAM,YAAY;AAAA,YAC5B,WAAW,UAAU,MAAM,WAAW;AACpC,uBAAS,MAAM;AACf,sBAAQ,MAAM,YAAY;AAAA,YAC5B;AAAA,UACF;AACA,gBAAM,gBAAgB,UAAU;AAChC,gBAAM,iBAAiB,WAAW;AAClC,cAAI,WAAW,SAAS;AACtB,kBAAM,IAAI,UAAU,cAAc,QAAQ,cAAc;AACxD,kBAAM,IAAI,UAAU,cAAc,SAAS,eAAe;AAC1D,kBAAM,eAAe,MAAM,SAAS;AACpC,kBAAM,eAAe,MAAM,SAAS;AACpC,gBAAI,gBAAgB,cAAc;AAChC,oBAAM,iBAAiB;AAAA,gBACrB,IAAI,KAAK;AAAA,gBACT,MAAM;AAAA,gBACN,MAAM,KAAK;AAAA,gBACX,UAAU;AAAA,kBACR,GAAG,eAAe,IAAI;AAAA,kBACtB,GAAG,eAAe,IAAI;AAAA,gBACxB;AAAA,cACF;AACA,sBAAQ,KAAK,cAAc;AAC3B,yBAAW,IAAI,eAAe,SAAS;AACvC,yBAAW,IAAI,eAAe,SAAS;AAAA,YACzC;AAAA,UACF;AACA,cAAI,MAAM,WAAW,iBAAiB,iBAAiB;AACrD,kBAAM,kBAAkB;AAAA,cACtB,IAAI,MAAM;AAAA,cACV,MAAM;AAAA,cACN,aAAa;AAAA,cACb,UAAU;AAAA,cACV,YAAY;AAAA,gBACV;AAAA,gBACA;AAAA,cACF;AAAA,YACF;AACA,oBAAQ,KAAK,eAAe;AAC5B,uBAAW,QAAQ;AACnB,uBAAW,SAAS;AAAA,UACtB;AACA,cAAI,QAAQ,WAAW,GAAG;AACxB;AAAA,UACF;AACA,gBAAM,YAAY,aAAa;AAAA,YAC7B,OAAO,WAAW;AAAA,YAClB;AAAA,YACA,QAAQ,WAAW;AAAA,YACnB;AAAA,YACA;AAAA,YACA;AAAA,UACF,CAAC;AACD,gBAAM,aAAa,EAAE,GAAG,YAAY,UAAU;AAC9C,gBAAM,cAAc,KAAK,MAAM,iBAAiB,OAAO,SAAS,GAAG,KAAK,OAAO,OAAO,UAAU;AAChG,cAAI,eAAe,OAAO;AACxB;AAAA,UACF;AACA,gBAAM,UAAU,EAAE,OAAO,QAAQ,WAAW,CAAC;AAC7C,uBAAa,YAAY,OAAO;AAAA,QAClC;AAAA,MACF,CAAC,EAAE,GAAG,OAAO,CAAC,UAAU;AACtB,YAAI,MAAM,QAAQ;AAChB,gBAAM,kBAAkB;AAAA,YACtB,IAAI,MAAM;AAAA,YACV,MAAM;AAAA,YACN,UAAU;AAAA,UACZ;AACA,gBAAM,aAAa,EAAE,OAAO,QAAQ,WAAW,CAAC;AAChD,uBAAa,YAAY,CAAC,eAAe,CAAC;AAAA,QAC5C;AAAA,MACF,CAAC;AACD,gBAAU,KAAK,WAAW;AAC1B,gBAAU,MAAM;AACd,kBAAU,GAAG,SAAS,IAAI;AAAA,MAC5B,CAAC;AAAA,IACH,CAAC;AACD,WAAO,CAAC,MAAM,WAAW;AACvB,aAAO,UAAU,GAAG,mBAAmB,OAAO;AAAA,QAC5C,SAAS;AAAA,QACT,KAAK;AAAA,QACL,OAAO,eAAe,CAAC,mCAAmC,CAAC,GAAG,mBAAmB,OAAO,KAAK,OAAO,CAAC,CAAC;AAAA,QACtG,OAAO,eAAe,aAAa,KAAK;AAAA,MAC1C,GAAG;AAAA,QACD,WAAW,KAAK,QAAQ,SAAS;AAAA,MACnC,GAAG,CAAC;AAAA,IACN;AAAA,EACF;AACF,CAAC;AACD,IAAM,cAAc;AAAA,EAClB,MAAM;AAAA,EACN,cAAc,EAAE,MAAM,EAAE;AAAA,EACxB,cAAc;AAChB;AACA,IAAM,YAA4B,gBAAgB;AAAA,EAChD,GAAG;AAAA,EACH,OAAO;AAAA,IACL,QAAQ,CAAC;AAAA,IACT,OAAO,CAAC;AAAA,IACR,iBAAiB,CAAC;AAAA,IAClB,aAAa,CAAC;AAAA,IACd,eAAe,CAAC;AAAA,IAChB,WAAW,CAAC;AAAA,IACZ,WAAW,EAAE,MAAM,SAAS,SAAS,KAAK;AAAA,IAC1C,UAAU,CAAC;AAAA,IACX,WAAW,CAAC;AAAA,IACZ,UAAU,CAAC;AAAA,IACX,WAAW,CAAC;AAAA,IACZ,cAAc,CAAC;AAAA,IACf,iBAAiB,EAAE,MAAM,CAAC,SAAS,MAAM,EAAE;AAAA,EAC7C;AAAA,EACA,OAAO,CAAC,eAAe,UAAU,WAAW;AAAA,EAC5C,MAAM,SAAS,EAAE,MAAM,MAAM,GAAG;AAC9B,UAAM,QAAQ;AACd,UAAM,EAAE,UAAU,OAAO,aAAa,IAAI,WAAW;AACrD,UAAM,iBAAiB,CAAC,YAAY,aAAa,eAAe,cAAc;AAC9E,UAAM,eAAe,CAAC,OAAO,SAAS,UAAU,MAAM;AACtD,UAAM,gBAAgB,OAAO,QAAiB,IAAI;AAClD,UAAM,SAAS,MAAM,MAAM,OAAO,MAAM,WAAW,WAAW,MAAM,SAAS,aAAa;AAC1F,UAAM,OAAO,SAAS,MAAM,SAAS,OAAO,KAAK,CAAC;AAClD;AAAA,MACE;AAAA,QACE,MAAM,MAAM;AAAA,QACZ,MAAM,MAAM;AAAA,QACZ,MAAM,MAAM;AAAA,QACZ,MAAM,MAAM;AAAA,QACZ,MAAM;AACJ,cAAI;AACJ,iBAAO,CAAC,GAAG,KAAK,KAAK,UAAU,OAAO,SAAS,GAAG,WAAW,UAAU,CAAC,CAAC,KAAK,MAAM,WAAW;AAAA,QACjG;AAAA,MACF;AAAA,MACA,CAAC,CAAC,UAAU,WAAW,UAAU,WAAW,aAAa,MAAM;AAC7D,cAAM,IAAI,KAAK;AACf,YAAI,KAAK,eAAe;AACtB,gBAAM,kBAAkB;AAAA,YACtB,IAAI,EAAE;AAAA,YACN,MAAM;AAAA,YACN,aAAa;AAAA,YACb,YAAY;AAAA,cACV,OAAO,EAAE,WAAW;AAAA,cACpB,QAAQ,EAAE,WAAW;AAAA,YACvB;AAAA,UACF;AACA,cAAI,YAAY,EAAE,WAAW,QAAQ,UAAU;AAC7C,4BAAgB,WAAW,QAAQ;AAAA,UACrC;AACA,cAAI,aAAa,EAAE,WAAW,SAAS,WAAW;AAChD,4BAAgB,WAAW,SAAS;AAAA,UACtC;AACA,cAAI,YAAY,EAAE,WAAW,QAAQ,UAAU;AAC7C,4BAAgB,WAAW,QAAQ;AAAA,UACrC;AACA,cAAI,aAAa,EAAE,WAAW,SAAS,WAAW;AAChD,4BAAgB,WAAW,SAAS;AAAA,UACtC;AACA,cAAI,gBAAgB,WAAW,UAAU,EAAE,WAAW,SAAS,gBAAgB,WAAW,WAAW,EAAE,WAAW,QAAQ;AACxH,yBAAa,YAAY,CAAC,eAAe,CAAC;AAAA,UAC5C;AAAA,QACF;AAAA,MACF;AAAA,MACA,EAAE,OAAO,QAAQ,WAAW,KAAK;AAAA,IACnC;AACA,WAAO,CAAC,MAAM,WAAW;AACvB,aAAO,KAAK,aAAa,UAAU,GAAG,mBAAmB,UAAU,EAAE,KAAK,EAAE,GAAG;AAAA,SAC5E,UAAU,GAAG,mBAAmB,UAAU,MAAM,WAAW,cAAc,CAAC,MAAM;AAC/E,iBAAO,YAAY,aAAa;AAAA,YAC9B,KAAK;AAAA,YACL,OAAO,eAAe,KAAK,aAAa;AAAA,YACxC,OAAO,eAAe,KAAK,SAAS;AAAA,YACpC,WAAW,OAAO;AAAA,YAClB,UAAU;AAAA,YACV,SAAS,MAAM,oBAAoB,EAAE;AAAA,YACrC,qBAAqB,KAAK;AAAA,YAC1B,OAAO,KAAK;AAAA,YACZ,aAAa,KAAK;AAAA,YAClB,cAAc,KAAK;AAAA,YACnB,aAAa,KAAK;AAAA,YAClB,cAAc,KAAK;AAAA,YACnB,iBAAiB,KAAK;AAAA,YACtB,eAAe,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,WAAW,MAAM,eAAe,MAAM;AAAA,YAChF,UAAU,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,WAAW,MAAM,UAAU,MAAM;AAAA,YACtE,aAAa,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,WAAW,MAAM,aAAa,MAAM;AAAA,UAC9E,GAAG,MAAM,GAAG,CAAC,SAAS,SAAS,WAAW,YAAY,WAAW,qBAAqB,SAAS,aAAa,cAAc,aAAa,cAAc,eAAe,CAAC;AAAA,QACvK,CAAC,GAAG,EAAE;AAAA,SACL,UAAU,GAAG,mBAAmB,UAAU,MAAM,WAAW,gBAAgB,CAAC,MAAM;AACjF,iBAAO,YAAY,aAAa;AAAA,YAC9B,KAAK;AAAA,YACL,OAAO,eAAe,KAAK,eAAe;AAAA,YAC1C,OAAO,eAAe,KAAK,WAAW;AAAA,YACtC,WAAW,OAAO;AAAA,YAClB,UAAU;AAAA,YACV,OAAO,KAAK;AAAA,YACZ,aAAa,KAAK;AAAA,YAClB,cAAc,KAAK;AAAA,YACnB,aAAa,KAAK;AAAA,YAClB,cAAc,KAAK;AAAA,YACnB,iBAAiB,KAAK;AAAA,YACtB,qBAAqB,KAAK;AAAA,YAC1B,eAAe,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,WAAW,MAAM,eAAe,MAAM;AAAA,YAChF,UAAU,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,WAAW,MAAM,UAAU,MAAM;AAAA,YACtE,aAAa,OAAO,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,WAAW,MAAM,aAAa,MAAM;AAAA,UAC9E,GAAG,MAAM,GAAG,CAAC,SAAS,SAAS,WAAW,YAAY,SAAS,aAAa,cAAc,aAAa,cAAc,iBAAiB,mBAAmB,CAAC;AAAA,QAC5J,CAAC,GAAG,EAAE;AAAA,MACR,GAAG,EAAE,KAAK,mBAAmB,IAAI,IAAI;AAAA,IACvC;AAAA,EACF;AACF,CAAC;", "names": []}