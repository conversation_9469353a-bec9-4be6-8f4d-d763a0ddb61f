<template>
  <div class="create-evaluator-wrapper">
    <div class="page-header" @click="router.go(-1)">
      <icon-left />
      <span class="page-title">返回</span>
    </div>
    <div class="create-evaluator-container">
      <div class="create-evaluator-container-form">
        <AiForm ref="formRef" v-model="form" :columns="columns">
          <template #promptLabel>
            <div class="flex justify-between w-full prompt-label">
              <span>Prompt</span>
              <span>
                <a-button class="template" :size="'mini'" type="text" style="padding: 0 4px">
                  <icon-bookmark class="mr-1" />
                  选择模版
                </a-button>
                <a-button class="clear" :size="'mini'" type="text" style="padding: 0 4px" @click="clear">
                  <icon-delete class="mr-1" />
                  清空
                </a-button>
              </span>
            </div>
          </template>
          <template #modelId>
            <a-trigger trigger="click" :unmount-on-close="false" :content-style="triggerStyles" position="bl">
              <a-button class="model-selector">{{ modelName }}</a-button>
              <template #content>
                <div class="model-selector-wrapper">
                  <div class="model-form">
                    <div class="model-form-item">
                      <div class="model-form-item-label">模型选择</div>
                      <div class="model-form-item-content">
                        <a-select
                          v-model="form.modelId"
                          placeholder="请选择模型"
                          style="width: 300px"
                          @change="handleModelChange"
                        >
                          <a-option v-for="item in modelOptions" :key="item.value">{{ item.label }}</a-option>
                        </a-select>
                      </div>
                    </div>
                    <div class="model-form-item">
                      <div class="model-form-item-label">最大回复长度</div>
                      <div class="model-form-item-content">
                        <a-slider
                          v-model="form.maxTokens"
                          :min="0"
                          :max="4096"
                          :style="{ width: '300px' }"
                          show-input
                        />
                      </div>
                    </div>
                    <div class="model-form-item">
                      <div class="model-form-item-label">生成随机性</div>
                      <div class="model-form-item-content">
                        <a-slider v-model="form.temperature" :min="0" :max="1" :style="{ width: '300px' }" show-input />
                      </div>
                    </div>
                    <div class="model-form-item">
                      <div class="model-form-item-label">Top P</div>
                      <div class="model-form-item-content">
                        <a-slider v-model="form.topP" :min="0" :max="1" :style="{ width: '300px' }" show-input />
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </a-trigger>
          </template>
          <template #prompt>
            <div class="block-item">
              <div class="block-item-header">
                <div class="flex items-center">
                  <a-button class="block-item-header-btn" style="padding: 0">
                    <icon-drag-dot-vertical class="icon-drag" :size="14" />
                  </a-button>
                  <a-button class="block-item-header-btn">
                    <span class="pr-1">System</span>
                  </a-button>
                </div>
                <div class="flex items-center">
                  <a-button class="block-item-header-btn" @click="delPromptBlockItem">
                    <icon-delete />
                  </a-button>
                </div>
              </div>
              <div class="block-item-form">
                <AiPromptEditor
                  v-model="form.prompt"
                  placeholder="请输入内容，支持按此格式书写变量：{{USER_NAME}}"
                  :enable-variable-space="true"
                  @change="promptChange"
                  @variable-update="handleVariableUpdate"
                />
              </div>
            </div>
          </template>
        </AiForm>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { type ColumnItem, AiForm } from '@/components/AiForm'
import { useResetReactive } from '@/hooks'
import { getModelTypesHttp } from '@/apis/model-mgmt'

defineOptions({ name: 'AddEvaluatorPage' })
const triggerStyles = {
  width: '550px',
  'background-color': '#ffffff',
  'box-shadow': '0 0 10px rgba(0, 0, 0, 0.1)',
  padding: '16px'
}

const router = useRouter()
const formRef = ref<InstanceType<typeof AiForm>>()
const modelOptions = ref<{ label: string; value: string }[]>([])
const modelName = ref('')
const variables = ref<string[]>([])
const showUserPrompt = ref(false)
const [form, resetForm] = useResetReactive({
  name: '',
  description: '',
  modelId: '',
  maxTokens: 2048,
  temperature: 0.7,
  topP: 0.9,
  prompt: ''
})

// 获取模型列表
const fetchModelList = async () => {
  try {
    const res = await getModelTypesHttp('llm')
    if (res.data && Array.isArray(res.data)) {
      const options: { label: string; value: string }[] = []
      res.data.forEach((group) => {
        if (group.models && Array.isArray(group.models)) {
          group.models.forEach((model) => {
            options.push({
              label: model.model,
              value: model.model
            })
          })
        }
      })
      modelOptions.value = options
      form.modelId = options[0]?.value
      modelName.value = options[0]?.label
    }
  } catch (error) {
    console.error('获取模型列表失败', error)
  }
}

onMounted(() => {
  fetchModelList()
})

const columns = reactive<ColumnItem[]>([
  {
    label: '名称',
    field: 'name',
    type: 'input',
    span: 24,
    required: true,
    props: {
      placeholder: '请输入名称',
      allowClear: true,
      maxLength: 50
    }
  },
  {
    label: '描述',
    field: 'description',
    type: 'textarea',
    span: 24,
    props: {
      placeholder: '请输入描述',
      allowClear: true
    }
  },
  {
    label: '模型选择',
    field: 'modelId',
    type: 'select',
    span: 24,
    required: true,
    props: {
      placeholder: '请选择模型',
      allowClear: true,
      options: modelOptions
    }
  },
  {
    labelSlotName: 'promptLabel',
    field: 'prompt',
    type: 'textarea',
    span: 24,
    required: true,
    props: {
      placeholder: '请输入内容，支持按此格式书写变量: {{USER_NAME}}',
      allowClear: true
    },
    formItemProps: {
      rowClass: 'prompt-label-wrapper',
      labelComponent: 'span'
    }
  }
])

const handleModelChange = (value) => {
  const ele = modelOptions.value.find((item) => item.value === value)
  modelName.value = ele?.label
}

const promptChange = (content: string) => {
  form.prompt = content
}

const handleVariableUpdate = (newVariables: string[]) => {
  variables.value = newVariables
}

const clear = () => {}
const delPromptBlockItem = () => {}
// 重置
const reset = () => {
  formRef.value?.formRef?.resetFields()
  resetForm()
}

// 提交
const onSubmit = async () => {
  try {
    const isInvalid = await formRef.value?.formRef?.validate()
    if (isInvalid) return false
    return true
  } catch (error) {
    return false
  }
}
</script>

<style scoped lang="scss">
.create-evaluator-wrapper {
  height: 100%;
  width: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  .page-header {
    width: 100%;
    display: flex;
    cursor: pointer;
    align-items: center;
    padding: 16px;
    .page-title {
      font-size: 14px;
    }
  }
  .create-evaluator-container {
    flex: 1;
    width: 100%;
    padding: 16px;
    display: flex;
    justify-content: center;
    overflow-y: auto;
    background-color: #fff;
    &-form {
      width: 700px;
    }
  }
}
.model-selector {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.model-form {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;
  &-item {
    display: flex;
    align-items: center;
    &-label {
      width: 120px;
    }
    :deep(.arco-slider) {
      display: flex;
      .arco-slider-track::before {
        height: 4px;
        border-radius: 4px;
      }
      .arco-slider-bar {
        height: 4px;
        border-radius: 4px;
      }
    }
  }
}
:deep(.block-item) {
  border: 1px solid #c9c9d6;
  border-radius: 8px;
  width: 100%;
  .block-item-header {
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 8px;
    color: var(--color-text-3);
    background-color: #fff;
    border-radius: 8px;
    .block-item-header-btn {
      height: 22px;
      border: none;
      padding: 0 8px;
      color: var(--color-text-3);
    }
  }
  .block-item-form {
    border-top: 1px solid #c9c9d6;
    padding: var(--padding);
    background-color: #fff;
    border-radius: 0 0 8px 8px;
  }
}
:deep(.prompt-label-wrapper) {
  .arco-form-item-label {
    width: 100%;
    display: flex;
    align-items: center;
  }
}
.prompt-label {
  .clear {
    color: var(--color-text-2);
  }
}
</style>
