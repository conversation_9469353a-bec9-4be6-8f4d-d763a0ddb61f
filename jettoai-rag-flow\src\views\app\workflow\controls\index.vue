<template>
  <Controls position="bottom-left">
    <!-- 撤销 -->
    <ControlButton :disabled="!canUndo" @click="undo">
      <a-tooltip :content="canUndo ? '撤销 (Ctrl+Z)' : '无可撤销操作'">
        <icon-undo :class="{ 'icon-disabled': !canUndo }" />
      </a-tooltip>
    </ControlButton>

    <!-- 重做 -->
    <ControlButton :disabled="!canRedo" @click="redo">
      <a-tooltip :content="canRedo ? '重做 (Ctrl+Y)' : '无可重做操作'">
        <icon-redo :class="{ 'icon-disabled': !canRedo }" />
      </a-tooltip>
    </ControlButton>

    <!-- 变更历史 -->
    <ControlButton>
      <WorkflowHistory
        :past-states="historyPastStates"
        :future-states="historyFutureStates"
        :current-state-index="currentHistoryIndex"
        @jump-to-state="handleJumpToHistoryState"
        @jump-to-index="handleJumpToHistoryIndex"
      />
    </ControlButton>

    <!-- 分隔线 -->
    <!-- <div class="control-divider" /> -->

    <!-- 添加节点 -->
    <a-dropdown v-model="popoverInstance" :popup-max-height="1200" :trigger="['click']">
      <ControlButton>
        <a-tooltip content="添加节点">
          <icon-plus-circle />
        </a-tooltip>
      </ControlButton>
      <template #content>
        <div class="node-list" :style="{ width: divWidth }">
          <a-tabs v-model="tabvalue" @tab-click="tabClick">
            <a-tab-pane key="1" title="节点">
              <a-tooltip v-for="item in nodeList" :key="item.type" position="right" :content="item.title">
                <div class="node-list-item" @click="() => selectTool(item)">
                  <div class="node-list-item-icon" :style="{ backgroundColor: nodeColor[item.type] }">
                    <AiSvgIcon style="width: 14px; height: 14px" :name="`workflow-${item.type}`" />
                    <!-- <icon :icon="`icon: ${item.icon}`" /> -->
                  </div>
                  <div class="node-list-item-text">
                    {{ item.title }}
                  </div>
                </div>
              </a-tooltip>
            </a-tab-pane>
            <a-tab-pane key="2" title="工具">
              <a-space direction="vertical" size="large">
                <a-radio-group v-model="toolType" type="button" size="mini">
                  <a-radio value="1">全部</a-radio>
                  <!-- <a-radio value="Shanghai">Shanghai</a-radio>
                      <a-radio value="Guangzhou">Guangzhou</a-radio> -->
                  <a-radio value="4">工作流</a-radio>
                </a-radio-group>
              </a-space>
              <a-collapse
                v-if="toolworkflowList.length > 0"
                :default-active-key="['1']"
                expand-icon-position="left"
                :bordered="false"
              >
                <a-collapse-item v-for="item in toolworkflowList" :key="item.id" :header="item.name">
                  <div
                    v-for="(item1, index1) in item.tools"
                    :key="index1"
                    class="node-list-item"
                    @click="() => selectToolWork(item, item1)"
                  >
                    {{ item1.label.zh_Hans }}
                  </div>
                </a-collapse-item>
              </a-collapse>
              <div v-if="toolworkflowList.length === 0" class="empty-state">
                <icon-empty class="empty-icon" />
                <div class="empty-text">暂无工作流</div>
              </div>
            </a-tab-pane>
          </a-tabs>
        </div>
      </template>
    </a-dropdown>

    <!-- 添加注释 -->
    <ControlButton @click="addNote">
      <a-tooltip content="添加注释">
        <icon-message />
      </a-tooltip>
    </ControlButton>

    <!-- 分隔线 -->
    <!-- <div class="control-divider" /> -->

    <!-- 导出图片 -->
    <a-dropdown :trigger="['click']" position="top">
      <ControlButton>
        <a-tooltip content="导出图片">
          <icon-export />
        </a-tooltip>
      </ControlButton>
      <template #content>
        <div class="export-menu">
          <div class="export-item" @click="exportToImage('png')">
            <icon-image class="export-icon" />
            <span>导出为 PNG</span>
          </div>
          <div class="export-item" @click="exportToImage('jpg')">
            <icon-image class="export-icon" />
            <span>导出为 JPG</span>
          </div>
          <div class="export-item" @click="exportToImage('svg')">
            <icon-image class="export-icon" />
            <span>导出为 SVG</span>
          </div>
        </div>
      </template>
    </a-dropdown>

    <!-- 整理节点 -->
    <ControlButton @click="layoutGraph">
      <a-tooltip content="自动整理节点">
        <icon-sort />
      </a-tooltip>
    </ControlButton>
  </Controls>
</template>

<script setup lang="ts">
import { toolsworkflow } from '@/apis'
import type { NodeProps } from '@vue-flow/core'
import { ref, computed, nextTick, withDefaults, defineProps, defineEmits, onUnmounted } from 'vue'
import { useVueFlow } from '@vue-flow/core'
import { Controls, ControlButton } from '@vue-flow/controls'
import '@vue-flow/controls/dist/style.css'
import { nanoid } from 'nanoid'
import { Message } from '@arco-design/web-vue'

// 导入本地模块
import nodeUtils from '../utils/node-utils'
import { nodeColor, BlockEnum } from '../types/workflow'
import WorkflowHistory from '../components/workflow-history/index.vue'
import type { WorkflowHistoryEvent } from '../components/workflow-history/index.vue'

// 历史状态接口
interface HistoryState {
  event: WorkflowHistoryEvent
  timestamp: number
  stepCount: number
  isCurrent?: boolean
}

// 导入节点默认配置
import nodeDefault from '../nodes/llm/default'
import nodeDefaultHttp from '../nodes/http/default'
import nodeDefaultCode from '../nodes/code/default'
import nodeDefaultInteration from '../nodes/iteration/default'
import nodeDefaultParameter from '../nodes/parameter-extractor/default'
import { getModelDefaultHttp } from '@/apis/model-mgmt'
import { useNodesStore } from '@/stores/modules/workflow/nodes'
// API 导入 - 暂时注释掉不存在的API
// import { getModelDefaultHttp } from '@/apis/model-mgmt'
//
// Props 定义
//  = defineProps<NodeProps>()
const props = withDefaults(
  defineProps<{
    nodeData?: NodeProps
    canUndo?: boolean
    canRedo?: boolean
    historyPastStates?: HistoryState[]
    historyFutureStates?: HistoryState[]
    currentHistoryIndex?: number
  }>(),
  {
    canUndo: false,
    canRedo: false,
    historyPastStates: () => [],
    historyFutureStates: () => [],
    currentHistoryIndex: 0
  }
)
// 响应式数据
const toolType = ref('1')
const tabvalue = ref('1')
const toolworkflowList = ref([])
const divWidth = ref('150px')

// Vue Flow 实例
const { addNodes, getNodes, getEdges, zoomIn, zoomOut, fitView, getViewport, setViewport } = useVueFlow()
// const nodesStore = useNodesStore()
const { addNodeToflow, newaddNodeToflow } = nodeUtils()

// 计算可用的节点类型
const availableBlocks = computed(() => {
  return Object.values(BlockEnum)
})

// 根据app模式过滤节点列表
const nodeList = computed(() => {
  // 暂时设置为默认模式，支持多种模式
  const mode: 'workflow' | 'advanced-chat' | 'chat' = 'workflow' // appInfo.value?.mode
  const allNodeList = [
    {
      title: 'LLM',
      type: 'llm',
      desc: '',
      ...nodeDefault.defaultValue
    },
    {
      title: '知识检索',
      type: 'knowledge-retrieval',
      desc: '',
      query_variable_selector: [],
      dataset_ids: [],
      retrieval_mode: 'single'
    },
    {
      title: '结束',
      type: 'end',
      desc: '',
      outputs: []
    },
    {
      title: '直接回复',
      type: 'answer',
      desc: '',
      answer: '',
      variables: []
    },
    {
      title: '迭代',
      type: 'iteration',
      desc: '',
      ...nodeDefaultInteration.defaultValue
    },
    {
      title: '条件分支',
      type: 'if-else',
      cases: [
        {
          id: 'true',
          case_id: 'true',
          logical_operator: 'and',
          conditions: []
        }
      ]
    },
    {
      title: '代码执行',
      type: 'code',
      desc: '',
      ...nodeDefaultCode.defaultValue
    },
    {
      title: '文档提取',
      type: 'document-extractor',
      desc: ''
    },
    {
      title: '变量赋值',
      type: 'assigner',
      desc: ''
    },
    {
      title: '变量聚合',
      type: 'variable-aggregator',
      desc: '',
      variables: [],
      output_type: 'any'
    },
    {
      title: '参数提取',
      type: 'parameter-extractor',
      desc: '',
      ...nodeDefaultParameter.defaultValue
    },
    {
      title: 'http请求',
      type: 'http-request',
      desc: '',
      ...nodeDefaultHttp.defaultValue
    }
  ]

  return allNodeList.filter((node) => {
    // 根据mode字段判断节点显示
    if (mode === ('advanced-chat' as 'workflow' | 'advanced-chat' | 'chat')) {
      // advanced-chat模式：不显示结束节点，显示answer节点
      return node.type !== 'end'
    } else if (mode === 'workflow') {
      // workflow模式：显示结束节点，不显示answer节点
      return node.type !== 'answer'
    } else {
      return true
    }
  })
})

// 标签页切换处理
const tabClick = (value: string) => {
  if (value === '2') {
    divWidth.value = '300px'
  } else {
    divWidth.value = '150px'
  }
}
const popoverInstance = ref(false)
// 处理下拉菜单显示状态变化
const handleDropdownVisibleChange = (visible: boolean) => {
  popoverInstance.value = visible
  if (visible) {
    AddtoolWorkFlow()
  }
}

// 关闭下拉框
const handleClose = () => {
  console.log('是否触发')

  popoverInstance.value = false
  console.log(popoverInstance.value)
}

const PopStateEvent = () => {
  console.log('触发')
  // 不需要手动切换状态，由 a-dropdown 的 v-model 和 @visible-change 自动管理
}

const AddtoolWorkFlow = () => {
  toolsworkflow()
    .then((res) => {
      // console.log(res);
      toolworkflowList.value = [...res]
    })
    .catch((error) => {
      console.error('Failed to fetch tool workflow data:', error)
      // 可选：通知用户或触发重试机制
    })
}
const selectToolWork = async (item, item1) => {
  let toolvalue = {
    desc: '',
    is_team_authorization: true,
    paramSchemas: item1.parameters,
    params: {},
    provider_id: item.id,
    provider_name: item.name,
    provider_type: item.type,
    retry_config: {
      max_retries: 3,
      retry_enabled: false,
      retry_interval: 1000
    },
    selected: true,
    title: item1.label.zh_Hans,
    tool_configurations: {},
    tool_description: item1.description.zh_Hans,
    tool_label: item1.label.zh_Hans,
    tool_name: item1.name,
    tool_parameters: {},
    type: 'tool'
  }

  toolvalue.params = item1.parameters.reduce((acc, obj) => {
    acc[obj.name] = '' // 将 name 作为键，对象作为值
    return acc
  }, {})

  const { newNodeProps } = await newaddNodeToflow({}, toolvalue)
  handleClose()
}
const selectTool = async (toolItem: any) => {
  // try {
  // 暂时注释掉API调用
  if (toolItem.type === BlockEnum.LLM || toolItem.type === BlockEnum.ParameterExtractor) {
    // LLM的场景下：需要查询默认的llm，然后赋值。
    const { data: res } = await getModelDefaultHttp('llm')
    toolItem.model.provider = res.provider.provider
    toolItem.model.name = res.model
  }
  console.log(toolItem)
  const { newNodeProps } = await newaddNodeToflow({}, toolItem)
  console.log(newNodeProps)
  const nodesStore = useNodesStore()
  // 如果是迭代类型，动态添加node Cannot read properties of undefined (reading 'position')
  nextTick(() => {
    if (newNodeProps.type === 'iteration') {
      addNodes({
        id: `node_${nanoid()}`,
        type: 'iteration-start',
        position: { x: 40, y: 80 },
        data: { title: '', desc: '', type: 'iteration-start', isInIteration: true },
        parentNode: newNodeProps.id,
        extent: 'parent',
        expandParent: true
      })
    }
  })
  nodesStore.setNodes(getNodes.value)
  nodesStore.setEdges(getEdges.value)

  // Message.success(`已添加${toolItem.title}节点`)
  handleClose()
  // }
  // catch (error) {
  //   console.error('添加节点失败:', error)
  //   Message.error('添加节点失败')
  // }
}
// 事件定义
const emit = defineEmits<{
  (e: 'undo'): void
  (e: 'redo'): void
  (e: 'history'): void
  (e: 'exportToImage', imgtype: string): void
  (e: 'layoutGraph'): void
  (e: 'addNote'): void
  (e: 'zoomIn'): void
  (e: 'zoomOut'): void
  (e: 'fitView'): void
  (e: 'nodeAdd', type: BlockEnum, toolDefaultValue?: any): void
  (e: 'jumpToHistoryState', state: HistoryState): void
  (e: 'jumpToHistoryIndex', index: number): void
}>()

// 基础操作函数
const undo = () => {
  emit('undo')
}

const redo = () => {
  emit('redo')
}

const history = () => {
  emit('history')
}

// 添加注释节点
const addNote = () => {
  emit('addNote')
}

// 导出图片功能
const exportToImage = (imgtype: string) => {
  emit('exportToImage', imgtype)
}

// 自动整理节点布局
const layoutGraph = () => {
  emit('layoutGraph')
}

// 缩放控制
const handleZoomIn = () => {
  emit('zoomIn')
}

const handleZoomOut = () => {
  emit('zoomOut')
}

const handleFitView = () => {
  emit('fitView')
}

// 处理节点添加
const handleNodeAdd = (type: BlockEnum, toolDefaultValue?: any) => {
  emit('nodeAdd', type, toolDefaultValue)
}

// 处理历史状态跳转
const handleJumpToHistoryState = (state: HistoryState) => {
  emit('jumpToHistoryState', state)
}

// 处理历史索引跳转
const handleJumpToHistoryIndex = (index: number) => {
  console.log('=== Controls 收到 jump-to-index 事件 ===')
  console.log('索引:', index)
  console.log('转发给父组件，发送 jumpToHistoryIndex 事件')
  emit('jumpToHistoryIndex', index)
}

// 暴露方法给父组件
const closeDropdown = () => {
  handleClose()
}

// 组件卸载时清理事件监听器
// onUnmounted(() => {
//   // 清理逻辑已移除
// })

// 暴露方法给父组件
defineExpose({
  closeDropdown
})

// 更新撤销重做状态 - 现在由父组件管理
// const updateUndoRedoState = () => {
//   // 状态由父组件通过 props 传递
// }
</script>

<style scoped lang="scss">
// 禁用图标样式
.icon-disabled {
  opacity: 0.4;
  color: var(--color-text-4) !important;
  cursor: not-allowed;
}

.vue-flow__controls {
  position: absolute;
  left: 0;
  display: flex;
  gap: 2px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: white;
  padding: 8px;
  border: none !important;

  // 移除所有按钮的边框
  button {
    border: none !important;
    border-bottom: none !important;
    border-top: none !important;
    border-left: none !important;
    border-right: none !important;
  }

  // 特别针对 Vue Flow 的控制按钮
  .vue-flow__controls-button {
    border: none !important;
    border-bottom: none !important;

    &.vue-flow__controls-zoomin,
    &.vue-flow__controls-zoomout,
    &.vue-flow__controls-fitview {
      border: none !important;
      border-bottom: none !important;
    }
  }

  // 移除所有可能的边框
  * {
    border: none !important;
  }
}

// 全局覆盖 Vue Flow 控制按钮的边框样式
:deep(.vue-flow__controls) {
  border: none !important;

  button {
    border: none !important;
    border-bottom: none !important;
  }

  .vue-flow__controls-button {
    border: none !important;
    border-bottom: none !important;
  }
}

// 如果还有边框，使用更强的全局样式
:global(.vue-flow__controls button) {
  border: none !important;
  border-bottom: none !important;
}

// 最强力的边框移除 - 针对所有可能的 Vue Flow 控制按钮
:global(.vue-flow__controls-button),
:global(.vue-flow__controls-zoomin),
:global(.vue-flow__controls-zoomout),
:global(.vue-flow__controls-fitview) {
  border: none !important;
  border-bottom: none !important;
  border-top: none !important;
  border-left: none !important;
  border-right: none !important;
  box-shadow: none !important;
}
.control-btn {
  color: var(--color-text-2);
  // border: none;
  background: transparent;

  // &:hover {
  //   color: var(--color-text-1);
  //   background-color: var(--color-fill-2);
  // }

  &:disabled {
    color: var(--color-text-4);
    cursor: not-allowed;
  }
}

.control-divider {
  width: 100%;
  height: 1px;
  background-color: var(--color-border-2);
  margin: 4px 0;
}

.node-list {
  display: flex;
  flex-direction: column;
  min-height: 300px;
  // max-height: 400px;
  overflow-y: auto;

  .node-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 4px;
    padding: 8px;
  }

  .node-list-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background-color: var(--color-fill-2);
      transform: translateY(-1px);
    }

    &-icon {
      margin-right: 12px;
      height: 24px;
      width: 24px;
      border-radius: 6px;
      background-color: var(--color-fill-3);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
    }

    &-text {
      font-size: 14px;
      color: var(--color-text-2);
      font-weight: 500;
    }
  }
}

.tool-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  padding: 8px;

  .tool-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 12px 8px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid var(--color-border-2);

    &:hover {
      background-color: var(--color-fill-2);
      border-color: var(--color-border-3);
      transform: translateY(-1px);
    }

    .tool-icon {
      width: 24px;
      height: 24px;
      margin-bottom: 8px;
      color: var(--color-text-2);
    }

    .tool-name {
      font-size: 12px;
      color: var(--color-text-2);
      text-align: center;
    }
  }
}

.export-menu {
  min-width: 160px;
  padding: 4px;

  .export-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: var(--color-fill-2);
    }

    .export-icon {
      margin-right: 8px;
      width: 16px;
      height: 16px;
      color: var(--color-text-3);
    }

    span {
      font-size: 14px;
      color: var(--color-text-2);
    }
  }
}
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--color-text-3);

  .empty-icon {
    font-size: 32px;
    margin-bottom: 12px;
    opacity: 0.5;
  }

  .empty-text {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 4px;
  }

  .empty-desc {
    font-size: 12px;
    text-align: center;
    line-height: 1.4;
  }
}
</style>
