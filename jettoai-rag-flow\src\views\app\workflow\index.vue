<template>
  <div id="parentNode" style="width: 100%; height: 100%; position: relative; overflow: hidden">
    <!-- <a-button @click="save">保存</a-button> -->
    <VueFlow
      :id="props.flowId"
      v-model:nodes="nodesData"
      v-model:edges="edgesData"
      :apply-default="false"
      class="custom-vue-flow"
      :initial-zoom="0.7"
      :default-viewport="{ zoom: 0.7 }"
      :min-zoom="0.3"
      elevate-edges-on-select
      :connection-mode="'strict' as ConnectionMode.Strict"
      @viewport-change="onViewportChange"
      @node-click="onNodesClick"
      @connect="handleConnect"
    >
      <template #node-start="StartNodeProps">
        <StartNode v-bind="StartNodeProps" />
      </template>
      <template #node-custom="customNodeProps">
        <CustomNode v-bind="customNodeProps" />
      </template>
      <template #node-iteration-start="iterationStartNodeProps">
        <IterationiStartNode v-bind="iterationStartNodeProps" />
      </template>
      <template #node-iteration="iterationNodeProps">
        <IterationNode v-bind="iterationNodeProps" />
      </template>
      <template #node-http-request="httpRequestNodeProps">
        <HttpRequestNode v-bind="httpRequestNodeProps" />
      </template>
      <template #node-knowledge-retrieval="KnowledgRetrievalNodeProps">
        <KnowledgRetrievalNode v-bind="KnowledgRetrievalNodeProps" />
      </template>
      <template #node-end="EndNodeProps">
        <EndNode v-bind="EndNodeProps" />
      </template>
      <template #node-llm="llmNodeProps">
        <LlmNode v-bind="llmNodeProps" />
      </template>
      <template #node-answer="answerNodeProps">
        <AnswerNode v-bind="answerNodeProps" />
      </template>
      <template #node-variable-aggregator="variableNodeProps">
        <VariableAggregatorNode v-bind="variableNodeProps" />
      </template>
      <template #node-code="codeNodeProps">
        <CodeNode v-bind="codeNodeProps" />
      </template>
      <template #edge-custom="CustomEdgeProps">
        <CustomEdge v-bind="CustomEdgeProps" />
      </template>
      <template #node-assigner="AssignerNodeProps">
        <AssignerNode v-bind="AssignerNodeProps" />
      </template>
      <template #node-document-extractor="DocumenextractorNodeProps">
        <DocumentextractorNode v-bind="DocumenextractorNodeProps" />
      </template>
      <template #node-if-else="IfElseNodeProps">
        <IfElseNode v-bind="IfElseNodeProps" />
      </template>
      <template #node-parameter-extractor="ParameterExtractorNodeProps">
        <ParameterExtractorNode v-bind="ParameterExtractorNodeProps" />
      </template>
      <template #node-question-classifier="QuestionClassifierNodeProps">
        <QuestionClassifierNode v-bind="QuestionClassifierNodeProps" />
      </template>
      <template #node-variable-assigner="variableAssignerExtractorNodeProps">
        <variableAssignerExtractorNode v-bind="variableAssignerExtractorNodeProps" />
      </template>
      <template #node-tool="ToolNodeProps">
        <ToolNode v-bind="ToolNodeProps" />
      </template>
      <template #node-note="customNoteProps">
        <CustomNote v-bind="customNoteProps" />
      </template>
      <Background :gap="[14, 14]" :size="2" pattern-color="#eaeaea" class="vue-flow__background" />
      <MiniMap
        v-if="props.showMap"
        position="bottom-left"
        pannable
        zoomable
        class="vue-flow__minimap"
        :style="{
          width: 102,
          height: 72,
          bottom: '50px'
        }"
      />
      <Controls
        v-if="props.showControls"
        ref="controlsRef"
        :can-undo="canUndo"
        :can-redo="canRedo"
        :history-past-states="historyPastStates"
        :history-future-states="historyFutureStates"
        :current-history-index="currentHistoryIndex"
        @undo="undo"
        @redo="redo"
        @history="controlshistory"
        @export-to-image="exportToImage"
        @layout-graph="layoutGraph"
        @add-note="addNote"
        @zoom-in="handleZoomIn"
        @zoom-out="handleZoomOut"
        @fit-view="handleFitView"
        @node-add="handleNodeAdd"
        @jump-to-history-state="handleJumpToHistoryState"
        @jump-to-history-index="handleJumpToHistoryIndex"
      />
      <Panel ref="PaneDrawer" :node="nodeInfo" popup-container="#parentNode" @change="panelChange" />
      <RunPanel v-if="showRunPanelDrawer" ref="runPanelRef" @hideRunPanel="hideRunPanel" />
      <RunhistoryPanel
        v-if="showHistoryPanel"
        ref="HistoryPanelRef"
        :historyData="historyData"
        @hideRunPanel="hideHistoryPanel"
      />
      <PublishTollModal ref="PublishTollModalRef" :flowData="flowData" :workflowInfo="workflowInfo" />
      <versionHistory
        v-if="showversionHistoryPanel"
        ref="versionHistoryref"
        :newHash="newHash"
        :versionData="versionData"
        @changeworkflow="changeworkflowpanel"
        @hideversionPanel="hideVersionhistoryPanel"
      />
    </VueFlow>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, provide, watch } from 'vue'
import dagre from 'dagre'
import type { Edge, Node, ConnectionMode } from '@vue-flow/core'
import type { EdgeChange, EdgeRemoveChange, NodeChange, VueFlowStore } from '@vue-flow/core'

import { useVueFlow, VueFlow } from '@vue-flow/core'
import { Background } from '@vue-flow/background'
import { MiniMap } from '@vue-flow/minimap'
import { toJpeg, toPng, toSvg } from 'html-to-image'
import Controls from './controls/index.vue'
import CustomNode from './nodes/CustomNode.vue'
import StartNode from './nodes/start/index.vue'
import CustomNote from './nodes/custom-node/index.vue'
import EndNode from './nodes/end/index.vue'
import AssignerNode from './nodes/assigner/index.vue'
import DocumentextractorNode from './nodes/document-extractor/index.vue'
import IfElseNode from './nodes/if-else/index.vue'
import IterationNode from './nodes/iteration/index.vue'
import ParameterExtractorNode from './nodes/parameter-extractor/index.vue'
import QuestionClassifierNode from './nodes/question-classifier/index.vue'
import variableAssignerExtractorNode from './nodes/variable-assigner/index.vue'
import HttpRequestNode from './nodes/http/index.vue'
import LlmNode from './nodes/llm/index.vue'
import AnswerNode from './nodes/answer/index.vue'
import VariableAggregatorNode from './nodes/variable-aggregator/index.vue'
import KnowledgRetrievalNode from './nodes/knowledge-retrieval/index.vue'
import CodeNode from './nodes/code/index.vue'
import IterationiStartNode from './nodes/IterationiStartNode.vue'
import ToolNode from './nodes/tool/index.vue'
import versionHistory from './version/index.vue'
import CustomEdge from './edges/index.vue'

import '@vue-flow/minimap/dist/style.css'
import Panel from './nodes/panel.vue'
import RunPanel from './run/index.vue'
import RunhistoryPanel from './run/history.vue'
import { nanoid } from 'nanoid'
import { getWorkflow, saveWorkflow, draftConfig, defaultConfig, getWorkflowInfo } from '@/apis'
import { debounce } from 'lodash-es'
import PublishTollModal from './modal/PublishTollModal.vue'
import { Message } from '@arco-design/web-vue'
import { NodeType } from './types/node'
import useNodeHook from './hooks/useNodeHook'
import useEdgeHook from './hooks/useEdgeHook'
import { useNodesStore } from '@/stores/modules/workflow/nodes'
import nodeUtils from './utils/node-utils'
import { log } from 'node:console'
defineOptions({ name: 'Workflow' })

const PublishTollModalRef = ref<InstanceType<typeof PublishTollModal>>()
const controlsRef = ref()

const props = withDefaults(
  defineProps<{
    showMap?: boolean
    showControls?: boolean
    flowId?: string
  }>(),
  {
    showMap: true,
    showControls: true,
    flowId: ''
  }
)
const instance = ref()
const route = useRoute()
const emits = defineEmits(['workflowChange'])

const nodesStore = useNodesStore()

const appId = route.params.appId as string
const { addEdgesFn, onEdgeHoverStart, onEdgeHoverEnd } = useEdgeHook(instance, appId)
const { onNodeClick, showConfirmRemoveModal, handleNodeDrag, checkCyclicConnection } = useNodeHook(instance, appId)
interface VueFlowChanges {
  nodeChange: NodeChange[]
  edgeChange: EdgeChange[]
}

const onViewportChange = (viewport) => {
  // console.log('Viewport:', viewport)
}
const {
  onNodesChange,
  onEdgesChange,
  onConnect,
  nodes,
  edges,
  fitView,
  findNode,
  getNodes,
  addEdges,
  getEdges,
  getSelectedEdges,
  getSelectedNodes,
  updateNodeData,
  onPaneReady,
  onPaneClick,
  applyNodeChanges,
  applyEdgeChanges,
  setNodes,
  setEdges,
  addNodes
  // onNodesChange,
  // onEdgesChange
} = useVueFlow()

function configChange(changes: Array<EdgeChange | NodeChange>, type: 'node' | 'edge') {
  if (changes.length === 0) return false
  new Promise((resolve: (value: VueFlowChanges) => any) => {
    const license_change: VueFlowChanges = {
      nodeChange: [],
      edgeChange: []
    }
    changes.forEach(async (change, index) => {
      if (change.type === 'remove') {
        switch (type) {
          case 'node': {
            change as NodeChange
            const removeNode = findNode(change.id)
            if (removeNode) {
              switch (removeNode.data.type) {
                case NodeType.开始: {
                  Message.warning('开始节点无法删除')
                  break
                }
                case NodeType.迭代: {
                  const title = removeNode.data.type === NodeType.循环 ? '循环' : '迭代'
                  const childNodes = getNodes.value.filter((n) => n.parentNode === removeNode.id)
                  if (childNodes.length > 1) {
                    const confirmRemove = await showConfirmRemoveModal(title)
                    if (confirmRemove) {
                      license_change.nodeChange.push(change)
                      childNodes.forEach((n) => {
                        if (n.parentNode === removeNode.id) {
                          license_change.nodeChange.push({
                            id: n.id,
                            type: 'remove'
                          })
                        }
                      })
                    }
                  } else {
                    license_change.nodeChange.push(change)
                    childNodes.forEach((n) => {
                      if (n.parentNode === removeNode.id) {
                        license_change.nodeChange.push({
                          id: n.id,
                          type: 'remove'
                        })
                      }
                    })
                  }
                  break
                }
                default: {
                  license_change.nodeChange.push(change)
                  break
                }
              }

              const remove_node_ids = license_change.nodeChange
                .filter((change) => change.type === 'remove')
                .map((i) => i.id)
              const removeEdges = getEdges.value.filter(
                (e) => remove_node_ids.includes(e.source) || remove_node_ids.includes(e.target)
              )
              removeEdges.forEach((e) => {
                const { id, source, target } = e
                license_change.edgeChange.push({
                  id,
                  source,
                  sourceHandle: e.sourceHandle!,
                  target,
                  targetHandle: e.targetHandle!,
                  type: 'remove'
                } as EdgeChange)
              })
            }
            break
          }
          case 'edge': {
            if (getSelectedEdges.value.length === 0) {
              // 在删除节点时，默认会删除上下游的线，此处需要等待节点删除完毕再判断被删除线的上下游节点是否存在
              await nextTick()
              const sourceNode = findNode((change as EdgeRemoveChange).source)
              const targetNode = findNode((change as EdgeRemoveChange).target)
              if (!sourceNode || !targetNode) {
                license_change.edgeChange.push(change as EdgeChange)
              }
            } else {
              license_change.edgeChange.push(change as EdgeChange)
            }
            break
          }
        }
      } else {
        switch (type) {
          case 'node': {
            license_change.nodeChange.push(change as NodeChange)
            break
          }
          case 'edge': {
            license_change.edgeChange.push(change as EdgeChange)
            break
          }
        }
      }
      if (index === changes.length - 1) resolve(license_change)
    })
  }).then((license_change) => {
    license_change.nodeChange.length > 0 && applyNodeChanges(license_change.nodeChange)
    license_change.edgeChange.length > 0 && applyEdgeChanges(license_change.edgeChange)
  })
}
onNodesChange((changes) => configChange(changes, 'node'))
onEdgesChange((changes) => configChange(changes, 'edge'))
const nodesData = ref<Node[]>([])

const edgesData = ref<Edge[]>([])
const popoverInstance = ref(true)
provide('popoverInstance', popoverInstance)

const PaneDrawer = ref<InstanceType<typeof Panel>>()

const nodeInfo = ref()
const runPanelRef = ref()
const HistoryPanelRef = ref()
const versionHistoryref = ref()
const showRunPanelDrawer = ref(false)
const showHistoryPanel = ref(false)
const showversionHistoryPanel = ref(false)

const historyData = ref({})
const onNodesClick = ({ event, node }) => {
  console.log(node)
  if (node.type === 'note') return false
  PaneDrawer.value?.handleClick(event, node)
  nodeInfo.value = node
  onNodeClick(node)
}
const panelChange = (e) => {
  updateNodeData(nodeInfo.value.id, e)
}

// 节点拖拽事件处理已移动到 onNodesChange 监听器中

const maxHistorySize = 50 // 限制历史记录大小

// 计算撤销重做状态 - 基于变更历史
const canUndo = computed(() => historyPastStates.value.length > 0)
const canRedo = computed(() => historyFutureStates.value.length > 0)

// 工作流历史事件类型 - 参考 dify 的实现
enum WorkflowHistoryEvent {
  NodeTitleChange = 'NodeTitleChange',
  NodeDescriptionChange = 'NodeDescriptionChange',
  NodeDragStop = 'NodeDragStop',
  NodeChange = 'NodeChange',
  NodeConnect = 'NodeConnect',
  NodePaste = 'NodePaste',
  NodeDelete = 'NodeDelete',
  EdgeDelete = 'EdgeDelete',
  EdgeDeleteByDeleteBranch = 'EdgeDeleteByDeleteBranch',
  NodeAdd = 'NodeAdd',
  NodeResize = 'NodeResize',
  NoteAdd = 'NoteAdd',
  NoteChange = 'NoteChange',
  NoteDelete = 'NoteDelete',
  LayoutOrganize = 'LayoutOrganize'
}

// 历史状态接口
interface HistoryState {
  event: WorkflowHistoryEvent
  timestamp: number
  stepCount: number
  nodes: any[]
  edges: any[]
  isCurrent?: boolean
}

// 历史状态管理 - 简化版本，初始为空
const historyPastStates = ref<HistoryState[]>([])
const historyFutureStates = ref<HistoryState[]>([])
const currentHistoryIndex = ref(0)

// 防抖保存历史状态的引用
const saveStateToHistoryDebounced = ref<any>(null)

// 保存操作前的状态作为历史状态
let stateBeforeOperation: HistoryState | null = null

// 在操作前保存当前状态
const saveStateBeforeOperation = (event: WorkflowHistoryEvent) => {
  if (!getNodes || !getEdges) {
    console.warn('VueFlow instance not ready yet.')
    return
  }

  stateBeforeOperation = {
    event,
    timestamp: Date.now(),
    stepCount: 0,
    nodes: JSON.parse(JSON.stringify(getNodes.value)),
    edges: JSON.parse(JSON.stringify(getEdges.value))
  }
}

// 实际保存历史状态的函数（保存操作前的状态）
const doSaveStateToHistory = (event: WorkflowHistoryEvent) => {
  if (!stateBeforeOperation) {
    console.warn('No state before operation saved.')
    return
  }

  // 将操作前的状态添加到过去状态
  historyPastStates.value.push(stateBeforeOperation)

  // 清空未来状态（因为有了新的操作）
  historyFutureStates.value = []

  // 更新当前历史索引 - 当前状态应该是0（最新状态）
  currentHistoryIndex.value = 0

  // 限制历史记录大小
  if (historyPastStates.value.length > maxHistorySize) {
    historyPastStates.value.shift()
  }

  // 更新步数计算
  updateHistoryStepCounts()

  // 清空临时状态
  stateBeforeOperation = null

  console.log('保存历史状态完成，当前索引:', currentHistoryIndex.value, '历史状态数量:', historyPastStates.value.length)
}

// 参考 dify 的防抖保存历史状态
const saveStateToHistory = (event: WorkflowHistoryEvent) => {
  console.log(event)

  // 初始化防抖函数
  if (!saveStateToHistoryDebounced.value) {
    saveStateToHistoryDebounced.value = debounce((event: WorkflowHistoryEvent) => {
      doSaveStateToHistory(event)
    }, 500)
  }

  // 根据事件类型决定是否需要防抖
  switch (event) {
    case WorkflowHistoryEvent.NoteChange:
      saveStateToHistoryDebounced.value(event)
      break
    case WorkflowHistoryEvent.NodeTitleChange:
    case WorkflowHistoryEvent.NodeDescriptionChange:
    case WorkflowHistoryEvent.NodeDragStop:
    case WorkflowHistoryEvent.NodeChange:
    case WorkflowHistoryEvent.NodeConnect:
    case WorkflowHistoryEvent.NodePaste:
    case WorkflowHistoryEvent.NodeDelete:
    case WorkflowHistoryEvent.EdgeDelete:
    case WorkflowHistoryEvent.EdgeDeleteByDeleteBranch:
    case WorkflowHistoryEvent.NodeAdd:
    case WorkflowHistoryEvent.NodeResize:
    case WorkflowHistoryEvent.NoteAdd:
    case WorkflowHistoryEvent.LayoutOrganize:
    case WorkflowHistoryEvent.NoteDelete:
      saveStateToHistoryDebounced.value(event)
      break
    default:
      // 不为其他事件创建历史状态
      break
  }
}

// 更新历史状态的步数计算
const updateHistoryStepCounts = () => {
  // 为过去状态设置负步数（撤销）
  historyPastStates.value.forEach((state, index) => {
    state.stepCount = -(historyPastStates.value.length - index)
  })

  // 为未来状态设置正步数（重做）
  historyFutureStates.value.forEach((state, index) => {
    state.stepCount = index + 1
  })
}

// 旧的 saveState 函数已移除，现在使用变更历史系统
// 节点拖拽状态管理
const dragNodeStartPosition = ref({ x: 0, y: 0 })
const dragNodeId = ref<string | null>(null)
const isDragging = ref(false)

// 监听节点和边的变化，保存历史状态
onNodesChange((changes) => {
  // 根据变化类型保存到历史状态 - 参考 dify 的实现
  changes.forEach((change) => {
    switch (change.type) {
      case 'add':
        // 节点添加在 handleNodeAdd 中已经处理
        break
      case 'remove':
        saveStateToHistory(WorkflowHistoryEvent.NodeDelete)
        break
      case 'position':
        // 处理位置变化的开始和结束
        if (change.dragging && !isDragging.value) {
          // 拖拽开始 - 保存操作前状态
          const node = getNodes.value.find((n) => n.id === change.id)
          if (node) {
            // 保存拖拽前的状态
            saveStateBeforeOperation(WorkflowHistoryEvent.NodeDragStop)

            // 记录拖拽开始位置
            dragNodeStartPosition.value = { x: node.position.x, y: node.position.y }
            dragNodeId.value = change.id
            isDragging.value = true
            console.log('Drag start - node:', change.id, 'saved state before operation')
          }
        } else if (!change.dragging && isDragging.value && dragNodeId.value === change.id) {
          const node = getNodes.value.find((n) => n.id === change.id)
          const { x, y } = dragNodeStartPosition.value
          // 拖拽结束 - 检查是否需要保存历史
          const newPos = node.position

          console.log('Drag end - start:', { x, y }, 'end:', newPos)

          // 只有当位置真正发生变化时才保存历史状态
          if (!(x === newPos.x && y === newPos.y)) {
            // 检查是否是有效的拖拽
            if (x !== 0 && y !== 0) {
              console.log('Saving drag stop history')
              doSaveStateToHistory(WorkflowHistoryEvent.NodeDragStop)
            }
          } else {
            // 位置没有变化，清空临时保存的状态
            stateBeforeOperation = null
          }

          // 重置拖拽状态
          isDragging.value = false
          dragNodeId.value = null
          dragNodeStartPosition.value = { x: 0, y: 0 }
        }
        break
      default:
        // 其他变化暂时不记录历史
        break
    }
  })
})

onEdgesChange((changes) => {
  // 根据变化类型保存到历史状态
  changes.forEach((change) => {
    switch (change.type) {
      case 'add':
        // 连接事件在 onConnect 中处理
        break
      case 'remove':
        saveStateToHistory(WorkflowHistoryEvent.EdgeDelete)
        break
      default:
        break
    }
  })
})

const conhistorylist = ref([])

// 基于变更历史的撤销逻辑
const undo = () => {
  if (historyPastStates.value.length === 0) {
    console.log('没有可撤销的历史状态')
    return
  }

  // 保存当前状态到未来状态（用于重做）
  const currentState: HistoryState = {
    event: WorkflowHistoryEvent.NodeChange,
    timestamp: Date.now(),
    stepCount: 0,
    nodes: JSON.parse(JSON.stringify(getNodes.value)),
    edges: JSON.parse(JSON.stringify(getEdges.value))
  }
  historyFutureStates.value.unshift(currentState)

  // 从过去状态中取出最后一个状态
  const prevState = historyPastStates.value.pop()
  if (!prevState) return

  // 应用历史状态
  setNodes(prevState.nodes)
  setEdges(prevState.edges)
  edgesData.value = prevState.edges
  nodesData.value = prevState.nodes.map((e: any) => {
    if (e?.parentId) {
      e.parentNode = e.parentId
      e['extent'] = 'parent'
      e['expandParent'] = true
    }
    e.type = e.data.type
    return e
  })

  // 更新当前历史索引 - 撤销后当前状态索引应该增加1（向历史方向移动）
  currentHistoryIndex.value = currentHistoryIndex.value + 1

  // 更新步数计算
  updateHistoryStepCounts()

  console.log('撤销完成，当前历史索引:', currentHistoryIndex.value, '历史状态数量:', historyPastStates.value.length)
}

// 基于变更历史的重做逻辑
const redo = () => {
  if (historyFutureStates.value.length === 0) {
    console.log('没有可重做的历史状态')
    return
  }

  // 保存当前状态到过去状态（用于撤销）
  const currentState: HistoryState = {
    event: WorkflowHistoryEvent.NodeChange,
    timestamp: Date.now(),
    stepCount: 0,
    nodes: JSON.parse(JSON.stringify(getNodes.value)),
    edges: JSON.parse(JSON.stringify(getEdges.value))
  }
  historyPastStates.value.push(currentState)

  // 从未来状态中取出第一个状态
  const nextState = historyFutureStates.value.shift()
  if (!nextState) return

  // 应用历史状态
  setNodes(nextState.nodes)
  setEdges(nextState.edges)
  edgesData.value = nextState.edges
  nodesData.value = nextState.nodes.map((e: any) => {
    if (e?.parentId) {
      e.parentNode = e.parentId
      e['extent'] = 'parent'
      e['expandParent'] = true
    }
    e.type = e.data.type
    return e
  })

  // 更新当前历史索引 - 重做后当前状态索引应该减少1（向当前方向移动）
  currentHistoryIndex.value = Math.max(0, currentHistoryIndex.value - 1)

  // 更新步数计算
  updateHistoryStepCounts()

  console.log('重做完成，当前历史索引:', currentHistoryIndex.value, '历史状态数量:', historyPastStates.value.length)
}

//变更历史
const controlshistory = async () => {}
const exportToImage = async (type: string) => {
  try {
    console.log(workflowInfo)
    const flowElement = document.querySelector('.custom-vue-flow') as HTMLElement // 获取画布 DOM 元素
    if (!flowElement) {
      Message.error('未找到工作流画布')
      return
    }

    let dataUrl: string
    const options = {
      quality: 1,
      pixelRatio: 2,
      backgroundColor: '#ffffff'
    }

    switch (type) {
      case 'png':
        dataUrl = await toPng(flowElement, options)
        break
      case 'jpeg':
      case 'jpg':
        dataUrl = await toJpeg(flowElement, options)
        break
      case 'svg':
        dataUrl = await toSvg(flowElement)
        break
      default:
        dataUrl = await toPng(flowElement, options)
    }

    const link = document.createElement('a')
    link.href = dataUrl
    link.download = (workflowInfo.value?.name || 'workflow') + '.' + type
    link.click()

    Message.success(`工作流已导出为 ${type.toUpperCase()} 格式`)
  } catch (error) {
    console.error('导出图片失败:', error)
    Message.error('导出图片失败')
  }
}

// 添加注释节点
const addNote = () => {
  try {
    const { getViewport } = useVueFlow()
    const viewport = getViewport()
    const noteNode = {
      id: `note_${nanoid()}`,
      type: 'note',
      // sourcePosition: 'right',
      // targetPosition: 'left',
      position: {
        x: viewport.x + 100,
        y: viewport.y + 100
      },
      // positionAbsolute: {
      //   x: viewport.x + 100,
      //   y: viewport.y + 100
      // },
      // width: 240,
      // height: 88,
      data: {
        author: 'jettech',
        desc: '',
        height: 88,
        selected: true,
        showAuthor: true,
        theme: 'blue',
        title: '注释',
        type: 'note',
        width: 240,
        text: ''
      }
    }

    // 添加到节点数组
    nodesData.value.push(noteNode)
    nodesStore.setNodes(nodesData.value)

    // 保存状态到历史记录
    saveStateToHistory(WorkflowHistoryEvent.NoteAdd)

    Message.success('已添加注释节点')
  } catch (error) {
    console.error('添加注释失败:', error)
    Message.error('添加注释失败')
  }
}

// 缩放控制函数
const handleZoomIn = () => {
  const { zoomIn } = useVueFlow()
  zoomIn()
}

const handleZoomOut = () => {
  const { zoomOut } = useVueFlow()
  zoomOut()
}

const handleFitView = () => {
  fitView({ padding: 0.1 })
}

// 处理节点添加
const handleNodeAdd = async (type: any, toolDefaultValue?: any) => {
  try {
    // 保存操作前状态
    saveStateBeforeOperation(WorkflowHistoryEvent.NodeAdd)

    // 创建新节点的数据
    const nodeData = {
      type,
      title: getNodeTitle(type),
      ...toolDefaultValue
    }

    // 使用现有的节点添加逻辑
    const { newNodeProps } = await nodeUtils().addNodeToflow({}, nodeData, '0')

    // 如果是迭代类型，动态添加子节点
    if (type === 'iteration') {
      nextTick(() => {
        addNodes({
          id: `node_${nanoid()}`,
          type: 'iteration-start',
          position: { x: 40, y: 80 },
          data: { title: '', desc: '', type: 'iteration-start', isInIteration: true },
          parentNode: newNodeProps.id,
          extent: 'parent',
          expandParent: true
        })
      })
    }

    // 保存操作前状态到历史记录
    doSaveStateToHistory(WorkflowHistoryEvent.NodeAdd)

    Message.success(`已添加${nodeData.title}节点`)
  } catch (error) {
    console.error('添加节点失败:', error)
    Message.error('添加节点失败')
  }
}

// 获取节点标题
const getNodeTitle = (type: string) => {
  const titles: Record<string, string> = {
    llm: 'LLM',
    'knowledge-retrieval': '知识检索',
    answer: '直接回复',
    end: '结束',
    'if-else': '条件分支',
    iteration: '迭代',
    code: '代码执行',
    'http-request': 'HTTP请求',
    'parameter-extractor': '参数提取',
    'variable-aggregator': '变量聚合',
    assigner: '变量赋值',
    'document-extractor': '文档提取',
    tool: '工具'
  }
  return titles[type] || type
}

// 处理历史状态跳转
const handleJumpToHistoryState = (state: HistoryState) => {
  try {
    console.log('跳转到历史状态:', state)

    // 如果是当前状态，不需要跳转
    if (state.isCurrent) {
      console.log('点击的是当前状态，无需跳转')
      return
    }

    // 根据 timestamp 在历史数组中找到真正的历史状态数据
    let targetState: HistoryState | null = null
    let targetIndex = -1

    // 先在过去状态中查找
    const pastIndex = historyPastStates.value.findIndex((s) => s.timestamp === state.timestamp)
    if (pastIndex !== -1) {
      targetState = historyPastStates.value[pastIndex]
      // 在新的索引系统中，过去状态的索引是从1开始的（0是当前状态）
      // pastIndex 0 对应显示索引 1，pastIndex 1 对应显示索引 2，以此类推
      targetIndex = pastIndex + 1
    } else {
      // 在未来状态中查找
      const futureIndex = historyFutureStates.value.findIndex((s) => s.timestamp === state.timestamp)
      if (futureIndex !== -1) {
        targetState = historyFutureStates.value[futureIndex]
        // 未来状态的索引需要根据具体情况计算
        targetIndex = -1 // 暂时设为-1，表示未来状态
      }
    }

    if (!targetState) {
      console.error('未找到目标历史状态')
      Message.error('未找到目标历史状态')
      return
    }

    console.log('找到目标历史状态:', targetState, '目标索引:', targetIndex)

    // 应用找到的历史状态数据
    setNodes(targetState.nodes)
    setEdges(targetState.edges)
    edgesData.value = targetState.edges
    nodesData.value = targetState.nodes.map((e: any) => {
      if (e?.parentId) {
        e.parentNode = e.parentId
        e['extent'] = 'parent'
        e['expandParent'] = true
      }
      e.type = e.data.type
      return e
    })

    // 更新当前历史索引
    if (targetIndex !== -1) {
      currentHistoryIndex.value = targetIndex
    }

    // 更新步数计算
    updateHistoryStepCounts()

    console.log('跳转完成，当前历史索引:', currentHistoryIndex.value)
  } catch (error) {
    console.error('跳转历史状态失败:', error)
    // Message.error('跳转历史状态失败')
  }
}

// 处理历史索引跳转
const handleJumpToHistoryIndex = (index: number) => {
  console.log('=== 主文件收到 jumpToHistoryIndex 事件 ===')
  try {
    console.log('=== 主文件收到 jumpToHistoryIndex 事件 ===')
    console.log('跳转到索引:', index)
    console.log('当前历史索引:', currentHistoryIndex.value)
    console.log('历史状态数量:', historyPastStates.value.length)

    // 如果点击的是当前状态（索引0），只需要更新索引
    if (index === 0) {
      console.log('跳转到当前状态')
      currentHistoryIndex.value = 0
      updateHistoryStepCounts()
      console.log('跳转完成，当前历史索引:', currentHistoryIndex.value)
      return
    }

    // 根据索引获取对应的历史状态
    // 索引1对应historyPastStates[0]（第一个历史状态）
    // 索引2对应historyPastStates[1]（第二个历史状态），以此类推
    const pastStateIndex = index - 1

    if (pastStateIndex < 0 || pastStateIndex >= historyPastStates.value.length) {
      console.error('无效的历史状态索引:', index, '历史状态数量:', historyPastStates.value.length)
      return
    }

    const targetState = historyPastStates.value[pastStateIndex]
    if (!targetState) {
      console.error('未找到目标历史状态')
      return
    }

    console.log('找到目标历史状态:', targetState, '数组索引:', pastStateIndex)

    // 应用历史状态
    setNodes(targetState.nodes)
    setEdges(targetState.edges)
    edgesData.value = targetState.edges
    nodesData.value = targetState.nodes.map((e: any) => {
      if (e?.parentId) {
        e.parentNode = e.parentId
        e['extent'] = 'parent'
        e['expandParent'] = true
      }
      e.type = e.data.type
      return e
    })

    // 直接设置当前历史索引为用户点击的索引
    currentHistoryIndex.value = index

    // 更新步数计算
    updateHistoryStepCounts()

    console.log('跳转完成，当前历史索引:', currentHistoryIndex.value)
  } catch (error) {
    console.error('跳转历史索引失败:', error)
  }
}
const direction = ref('LR') // 或 'LR'
const layoutGraph = async () => {
  const g = new dagre.graphlib.Graph()
  g.setGraph({ rankdir: direction.value, nodesep: 50, ranksep: 70 })
  g.setDefaultEdgeLabel(() => ({}))
  getNodes.value.forEach((node) => {
    g.setNode(node.id, { width: node.width || 100, height: node.height || 50 })
  })
  getEdges.value.forEach((edge) => {
    g.setEdge(edge.source, edge.target)
  })
  dagre.layout(g)
  const updatedNodes = getNodes.value.map((node) => {
    const { x, y } = g.node(node.id)
    return { ...node, position: { x, y } }
  })
  setNodes(updatedNodes)

  // 保存状态到历史记录
  saveStateToHistory(WorkflowHistoryEvent.LayoutOrganize)

  nextTick(() => {
    fitView()
  })
}
const save = () => {
  console.log(nodes)
  console.log(edges)
}
const newHash = ref()

const flowData = ref()
const isEnd = ref(false)
const workflowInfo = ref()
const getDraftConfig = async () => {
  const res = await getWorkflowInfo(appId)
  workflowInfo.value = res
}

const changeworkflowpanel = (item) => {
  edgesData.value = item.graph.edges
  const nodes = item.graph.nodes as any
  nodesData.value = nodes.map((e) => {
    if (e?.parentId) {
      e.parentNode = e.parentId
      e['extent'] = 'parent'
      e['expandParent'] = true
    }
    e.type = e.data.type
    return e
  })
}

const getWorkflowData = async () => {
  try {
    const res = await getWorkflow(appId)
    flowData.value = res
    const nodes = res.graph.nodes as any
    nodesData.value = nodes.map((e) => {
      if (e?.parentId) {
        e.parentNode = e.parentId
        e['extent'] = 'parent'
        e['expandParent'] = true
      }
      if (e.type == 'custom-note') {
        e.data.type = 'note'
        e.type = 'note'
      } else {
        e.type = e.data.type
      }

      return e
    })
    edgesData.value = res.graph.edges
    // nodes和edges放在store中
    nodesStore.setNodes(nodesData.value)
    nodesStore.setEdges(edgesData.value)
    isEnd.value = true
    newHash.value = res.hash
    nextTick(() => {
      fitView()
      // 数据加载完成后保存初始状态
      setTimeout(() => {
        saveInitialState()
      }, 500)
    })
  } catch (error: any) {
    if (error && error.response && error.response.data) {
      if (error.response.data.code == 'draft_workflow_not_exist') {
        const params = {
          graph: {
            nodes: [
              {
                id: `node_${nanoid()}`,
                type: 'custom',
                position: { x: 80, y: 282 },
                data: { type: 'start', title: '开始', desc: '', variables: [] },
                sourcePosition: 'right',
                targetPosition: 'left'
              }
            ],
            edges: []
          },
          features: {
            retriever_resource: { enabled: true }
          },
          environment_variables: [],
          conversation_variables: []
        }
        saveWorkflow(appId, JSON.stringify(params)).then(() => {
          getWorkflowData()
        })
      }
    }
  }
}
const saveData = debounce(async (newflowData) => {
  try {
    const res = await saveWorkflow(appId, JSON.stringify(newflowData))
    newHash.value = res.hash
  } catch (error: any) {
    if (error && error.json && !error.bodyUsed) {
      error.json().then((err: any) => {
        if (err.code === 'draft_workflow_not_sync') getWorkflowData()
      })
    }
  }
}, 4000)
const saveDatas = async (newflowData) => {
  console.log('saveData', newflowData)

  try {
    const res = await saveWorkflow(appId, JSON.stringify(newflowData))
    newHash.value = res.hash
    getWorkflowData()
  } catch (error: any) {
    if (error && error.json && !error.bodyUsed) {
      error.json().then((err: any) => {
        if (err.code === 'draft_workflow_not_sync') getWorkflowData()
      })
    }
  }
}
provide('saveworkFlowversion', { saveDatas })

watch(
  () => ({ nodes: nodesData.value, edges: edgesData.value }),
  (newVal) => {
    if (showversionHistoryPanel.value) {
      return false
    }
    if (isEnd.value) {
      const cloneNodes = JSON.parse(JSON.stringify(newVal.nodes))
      // console.log('新增的值', cloneNodes)

      const newNodes = cloneNodes.map((e) => {
        if (e.data.type == 'iteration-start') {
          e.type = 'custom-iteration-start'
        } else if (e.data.type == 'note') {
          e.type = 'custom-note'
        } else {
          e.type = 'custom'
        }
        return e
      })
      const newflowData = {
        conversation_variables: flowData.value.conversation_variables,
        environment_variables: flowData.value.environment_variables,
        features: flowData.value,
        graph: {
          nodes: newNodes,
          edges: newVal.edges
        },
        hash: newHash.value
      }
      saveData(newflowData)
    }
  },
  { deep: true }
)
const handleConnect = (connection: any) => {
  console.log('handleConnect connection:', connection)

  // 安全检查
  if (!connection) {
    console.warn('handleConnect: connection is undefined')
    return
  }

  addEdges([
    {
      ...connection,
      type: 'custom',
      markerEnd: '',
      style: { strokeWidth: 2 }
    }
  ])

  // 连接完成后保存历史状态
  console.log('Saving connect history')
  saveStateToHistory(WorkflowHistoryEvent.NodeConnect)
}
// 保存初始状态到历史记录
const saveInitialState = () => {
  // 如果已经有历史记录，则不重复保存初始状态
  if (historyPastStates.value.length > 0) {
    console.log('历史记录已存在，跳过初始状态保存')
    return
  }

  if (!getNodes || !getEdges) {
    console.warn('VueFlow instance not ready yet.')
    return
  }

  // 初始状态不需要保存到历史记录中，因为当前状态就是初始状态
  // 只需要设置当前历史索引为0（表示没有历史记录，当前是初始状态）
  currentHistoryIndex.value = 0

  // 更新步数计算
  updateHistoryStepCounts()

  console.log('初始状态设置完成，当前历史索引:', currentHistoryIndex.value)
}

onMounted(() => {
  getWorkflowData()
  getDraftConfig()
  onConnect((connection) => handleConnect(connection))

  // 等待数据加载完成后保存初始状态
  nextTick(() => {
    setTimeout(() => {
      saveInitialState()
    }, 1000) // 延迟1秒确保数据完全加载
  })
})

// 显示运行panel
const showRunPanel = () => {
  showRunPanelDrawer.value = true
  nextTick(() => {
    runPanelRef.value.visible = true
  })
}
const versionData = ref({})
const versionHistoryPanel = (item) => {
  versionData.value = item
  showversionHistoryPanel.value = true
  nextTick(() => {
    versionHistoryref.value.visible = true
  })
}
const showRunHistoryPanel = (item) => {
  historyData.value = item
  showHistoryPanel.value = true
  nextTick(() => {
    HistoryPanelRef.value.visible = true
  })
}
// 显示发布为工具panel
const showPublishTollPanel = () => {
  nextTick(() => {
    PublishTollModalRef.value?.onAdd()
  })
}
const hideRunPanel = () => {
  showRunPanelDrawer.value = false
}
const hideHistoryPanel = () => {
  showHistoryPanel.value = false
}
const hideVersionhistoryPanel = () => {
  showHistoryPanel.value = false
}

defineExpose({
  showRunPanel,
  showRunHistoryPanel,
  showPublishTollPanel,
  getWorkflowData,
  versionHistoryPanel
})
</script>

<style scoped lang="scss">
.vue-flow__background {
  background: #f5f7fd;
}

.vue-flow__minimap {
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
</style>
