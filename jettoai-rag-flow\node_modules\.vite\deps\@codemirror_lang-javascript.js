import {
  autoCloseTags,
  completionPath,
  esLint,
  javascript,
  javascriptLanguage,
  jsxLanguage,
  localCompletionSource,
  scopeCompletionSource,
  snippets,
  tsxLanguage,
  typescriptLanguage,
  typescriptSnippets
} from "./chunk-I2JL5HOM.js";
import "./chunk-LLTEISZ4.js";
import "./chunk-JNWS3XKP.js";
import "./chunk-3IKZHP4X.js";
import "./chunk-ME6EH2OZ.js";
import "./chunk-ILC3BVFH.js";
import "./chunk-SNAQBZPT.js";
export {
  autoCloseTags,
  completionPath,
  esLint,
  javascript,
  javascriptLanguage,
  jsxLanguage,
  localCompletionSource,
  scopeCompletionSource,
  snippets,
  tsxLanguage,
  typescriptLanguage,
  typescriptSnippets
};
