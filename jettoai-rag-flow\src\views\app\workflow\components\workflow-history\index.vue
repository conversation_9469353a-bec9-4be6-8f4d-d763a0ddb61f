<template>
  <a-dropdown
    :visible="open"
    :trigger="['click']"
    position="top"
    :popup-max-height="400"
    @visible-change="handleOpenChange"
  >
    <!-- <a-button type="text" class="history-trigger" @click="handleTrigger">
      <template #icon> -->
    <icon-history @click="handleTrigger" />
    <!-- </template>
    </a-button> -->

    <template #content>
      <div class="history-panel">
        <div class="history-header">
          <div class="header-left">
            <div class="header-title">变更历史</div>
            <div class="header-info">
              {{ props.currentStateIndex === 0 ? '当前' : `历史 ${props.currentStateIndex}` }} /
              {{ allHistoryStates.length > 0 ? `${allHistoryStates.length} 个历史` : '无历史' }}
            </div>
          </div>
          <!-- <div class="header-right">
            <button v-if="allHistoryStates.length > 1" class="clear-btn" @click="handleClearHistory">清空</button>
          </div> -->
        </div>

        <div class="history-content">
          <div class="history-section">
            <!-- 当前状态 -->
            <div
              :class="[
                'history-item',
                {
                  'is-current': props.currentStateIndex === 0
                }
              ]"
              @click="() => handleJumpToCurrentState()"
            >
              <div class="item-content">
                <div class="item-title">
                  当前状态
                  <span v-if="props.currentStateIndex === 0" class="current-badge">当前</span>
                </div>
                <div class="item-time">{{ formatTime(Date.now()) }}</div>
              </div>
              <div class="item-step">当前</div>
            </div>

            <!-- 历史状态 -->
            <div
              v-for="(item, index) in allHistoryStates"
              :key="`history-${index}`"
              :class="[
                'history-item',
                {
                  'is-current': index === realCurrentIndex,
                  'is-future': false,
                  'is-past': index !== realCurrentIndex
                }
              ]"
              @click="() => handleJumpToHistoryIndex(index + 1)"
            >
              <div class="item-content">
                <div class="item-title">
                  {{ getEventLabel(item.event) }}
                  <span v-if="index === realCurrentIndex" class="current-badge">当前</span>
                </div>
                <div class="item-time">{{ formatTime(item.timestamp) }}</div>
              </div>
              <div class="item-step">历史 {{ index + 1 }}</div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="allHistoryStates.length === 0" class="empty-state">
            <icon-history class="empty-icon" />
            <div class="empty-text">暂无历史记录</div>
            <div class="empty-desc">开始编辑工作流后将显示变更历史</div>
          </div>
        </div>
      </div>
    </template>
  </a-dropdown>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 工作流历史事件类型
enum WorkflowHistoryEvent {
  NodeAdd = 'NodeAdd',
  NodeDelete = 'NodeDelete',
  NodeChange = 'NodeChange',
  NodeMove = 'NodeMove',
  EdgeAdd = 'EdgeAdd',
  EdgeDelete = 'EdgeDelete',
  NoteAdd = 'NoteAdd',
  NoteChange = 'NoteChange',
  NoteDelete = 'NoteDelete',
  LayoutOrganize = 'LayoutOrganize'
}

interface HistoryState {
  event: WorkflowHistoryEvent
  timestamp: number
  stepCount: number
  nodes?: any[]
  edges?: any[]
  isCurrent?: boolean
}

interface Props {
  // 统一的历史状态数组，包含所有历史记录
  allHistoryStates?: HistoryState[]
  // 当前状态在历史数组中的索引
  currentStateIndex?: number
  // 兼容旧接口
  pastStates?: HistoryState[]
  futureStates?: HistoryState[]
}

interface Emits {
  (e: 'jump-to-state', state: HistoryState): void
  (e: 'jump-to-index', index: number): void
  (e: 'clear-history'): void
}

const props = withDefaults(defineProps<Props>(), {
  allHistoryStates: () => [],
  currentStateIndex: 0,
  pastStates: () => [],
  futureStates: () => []
})

const emit = defineEmits<Emits>()

// 响应式数据
const open = ref(false)

// 计算属性 - 统一的历史状态数组
const allHistoryStates = computed(() => {
  // 如果直接传入了统一的历史数组，直接使用
  if (props.allHistoryStates && props.allHistoryStates.length > 0) {
    return props.allHistoryStates
  }

  // 否则从 pastStates 和 futureStates 构建统一数组（兼容旧接口）
  const combined: HistoryState[] = []

  // 添加过去状态（保持原始顺序）
  if (props.pastStates) {
    combined.push(...props.pastStates)
  }

  // 添加未来状态（如果有的话）
  if (props.futureStates) {
    combined.push(...props.futureStates)
  }

  return combined
})

// 计算在新数组结构中的真实当前索引
const realCurrentIndex = computed(() => {
  // 现在数组结构是：
  // - 索引0：pastStates[0]（第一个历史状态）
  // - 索引1：pastStates[1]（第二个历史状态）
  // - ...
  //
  // 传入的 currentStateIndex 表示：
  // - 0：当前状态（不在数组中，是实际的当前状态）
  // - 1：对应数组索引0（pastStates[0]）
  // - 2：对应数组索引1（pastStates[1]）

  if (props.currentStateIndex === 0) {
    // 当前状态，不在历史数组中，返回-1表示没有选中的历史项
    return -1
  } else {
    // 历史状态，在数组中的索引是 currentStateIndex - 1
    return props.currentStateIndex - 1
  }
})

// 方法
const handleOpenChange = (visible: boolean) => {
  open.value = visible
}

const handleTrigger = (e: Event) => {
  e.stopPropagation()
  open.value = !open.value
}

// 跳转到当前状态
const handleJumpToCurrentState = () => {
  console.log('=== 点击当前状态 ===')
  console.log('当前索引:', props.currentStateIndex)
  console.log('发送 jump-to-index 事件，索引: 0')
  emit('jump-to-index', 0)
  console.log('关闭下拉框')
  open.value = false
}

// 跳转到历史状态
const handleJumpToHistoryIndex = (index: number) => {
  console.log('=== 点击历史状态 ===')
  console.log('点击的索引:', index)
  console.log('当前状态索引:', props.currentStateIndex)
  console.log('发送 jump-to-index 事件，索引:', index)
  emit('jump-to-index', index)
  console.log('关闭下拉框')
  open.value = false
}

// 清空历史记录
const handleClearHistory = () => {
  emit('clear-history')
  open.value = false
}

const getEventLabel = (event: WorkflowHistoryEvent) => {
  const labels: Record<WorkflowHistoryEvent, string> = {
    [WorkflowHistoryEvent.NodeAdd]: '添加节点',
    [WorkflowHistoryEvent.NodeDelete]: '删除节点',
    [WorkflowHistoryEvent.NodeChange]: '修改节点',
    [WorkflowHistoryEvent.NodeMove]: '移动节点',
    [WorkflowHistoryEvent.EdgeAdd]: '添加连线',
    [WorkflowHistoryEvent.EdgeDelete]: '删除连线',
    [WorkflowHistoryEvent.NoteAdd]: '添加注释',
    [WorkflowHistoryEvent.NoteChange]: '修改注释',
    [WorkflowHistoryEvent.NoteDelete]: '删除注释',
    [WorkflowHistoryEvent.LayoutOrganize]: '整理布局'
  }
  return labels[event] || '未知操作'
}

// 计算步数标签
const getStepLabel = (stepDiff: number) => {
  if (stepDiff === 0) return '当前'
  const count = Math.abs(stepDiff)
  return stepDiff > 0 ? `前进 ${count} 步` : `后退 ${count} 步`
}

const formatTime = (timestamp: number) => {
  const now = Date.now()
  const diff = now - timestamp

  if (diff < 60000) {
    // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) {
    // 1小时内
    return `${Math.floor(diff / 60000)} 分钟前`
  } else if (diff < 86400000) {
    // 1天内
    return `${Math.floor(diff / 3600000)} 小时前`
  } else {
    return new Date(timestamp).toLocaleDateString()
  }
}
</script>

<style scoped lang="scss">
.history-trigger {
  color: var(--color-text-2);

  &:hover {
    color: var(--color-text-1);
    background-color: var(--color-fill-2);
  }
}

.history-panel {
  width: 300px;
  max-height: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  // border: 1px solid var(--color-border-2);
}

.history-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid var(--color-border-2);

  .header-left {
    .header-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--color-text-1);
      margin-bottom: 4px;
    }

    .header-info {
      font-size: 12px;
      color: var(--color-text-3);
    }
  }

  .header-right {
    .clear-btn {
      padding: 4px 8px;
      font-size: 12px;
      color: var(--color-text-3);
      background: transparent;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: var(--color-fill-2);
        color: var(--color-text-2);
      }
    }
  }
}

.history-content {
  max-height: 320px;
  overflow-y: auto;
}

.history-section {
  padding: 12px 16px;
  //  border-bottom: 1px solid var(--color-border-1);

  &:last-child {
    border-bottom: none;
  }
}

.section-title {
  font-size: 12px;
  font-weight: 600;
  color: var(--color-text-3);
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.history-items {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.history-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 2px 0;

  &:hover {
    background-color: var(--color-fill-2);
  }

  &.is-current {
    background-color: var(--color-primary-light-1);
    border: 1px solid var(--color-primary-light-3);
    cursor: pointer;

    .item-title {
      color: var(--color-primary);
      font-weight: 500;
    }
  }

  &.is-future {
    opacity: 0.7;

    .item-title {
      color: var(--color-text-3);
    }
  }

  &.is-past {
    .item-title {
      color: var(--color-text-2);
    }
  }

  .item-content {
    flex: 1;
    min-width: 0;

    .item-title {
      font-size: 14px;
      color: var(--color-text-2);
      margin-bottom: 2px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: flex;
      align-items: center;
      gap: 8px;

      .current-badge {
        display: inline-block;
        padding: 2px 6px;
        font-size: 10px;
        background: var(--color-primary);
        color: white;
        border-radius: 10px;
        font-weight: 500;
      }
    }

    .item-time {
      font-size: 12px;
      color: var(--color-text-4);
    }
  }

  .item-step {
    font-size: 12px;
    color: var(--color-text-3);
    flex-shrink: 0;
    margin-left: 8px;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--color-text-3);

  .empty-icon {
    font-size: 32px;
    margin-bottom: 12px;
    opacity: 0.5;
  }

  .empty-text {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 4px;
  }

  .empty-desc {
    font-size: 12px;
    text-align: center;
    line-height: 1.4;
  }
}
</style>
