<template>
  <a-modal :visible="true" :footer="false" @ok="handleOk" @cancel="handleCancel">
    <template #title>选择工具</template>
    <div>
      <a-tabs :type="'capsule'" :size="'small'" :active-key="activeKey" @change="handleChangeTab">
        <a-tab-pane v-for="tab in tabs" :key="tab.key" :title="tab.label">
          <a-collapse>
            <a-collapse-item
              v-for="(group) in mergedTools"
              :key="group.name"
              :header="renderName(group.label)"
            >
              <template #extra>
                {{ groupName(group) }}
              </template>

              <div
                v-for="(tool, index) in group.tools"
                :key="index"
              >
                <a-trigger position="top" auto-fit-position :unmount-on-close="false">
                  <div
                    class="tool-item flex cursor-pointer items-center justify-between rounded-lg pr-1 hover:bg-state-base-hover"
                    :class="{ disabled: getIsDisabled(group, tool) }"
                    @click="handleSelectTool(group, tool)">
                    <div
                      class="system-sm-medium h-8 truncate border-l-2 border-divider-subtle pl-4 leading-8 text-text-secondary">
                      {{ renderName(tool.label) }}
                    </div>
                    <!--v-if="getIsDisabled(group, tool)"-->
                    <a-tag class="added">已添加</a-tag>
                  </div>
                  <template #content>
                    <div class="demo-basic">
                      <div class="mb-1 text-sm leading-5 text-text-primary">
                        {{ renderName(tool.label) }}
                      </div>
                      <div class="text-xs leading-[18px] text-text-secondary">
                        {{ renderName(tool.description) }}
                      </div>
                    </div>
                  </template>
                </a-trigger>
              </div>


            </a-collapse-item>
          </a-collapse>
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-modal>
</template>
<script setup lang="ts">
// 定义了两个，还不一样。。
import { ToolTypeEnum, CollectionType } from '@/views/app/configuration/types'
import { renderName } from '@/views/app/workflow/utils/configuration'
import { getToolsHttp } from '@/apis/workflow/configuration'
// 'collectionList'
const props = defineProps(['selectedTools'])
const emits = defineEmits(['closeToolPickerModal', 'addTool'])

const tabs = [
  { label: '全部', key: CollectionType.all },
  { label: '插件', key: CollectionType.builtIn },
  { label: '自定义', key: CollectionType.custom },
  { label: '工作流', key: CollectionType.workflow }
]
const activeKey = ref('all')
const handleChangeTab = (key) => {
  console.log('key:', key)
  activeKey.value = key
}
const mergedTools = computed(() => {
  let collectionListTemp = []
  if (activeKey.value === CollectionType.all) {
    collectionListTemp = collectionList.value
  } else if (activeKey.value === CollectionType.builtIn) {
    collectionListTemp = collectionList.value.filter(v => v.type === CollectionType.builtIn)
  } else if (activeKey.value === CollectionType.custom) {
    collectionListTemp = collectionList.value.filter(v => v.type === CollectionType.custom)
  } else if (activeKey.value === CollectionType.workflow) {
    collectionListTemp = collectionList.value.filter(v => v.type === CollectionType.workflow)
  } else {
  }
  return collectionListTemp
})
const groupName = computed(() => {
  return (item) => {
    if ((item.type === CollectionType.builtIn)) {
      if (item.author && item.author.toUpperCase() === 'DIFY') {
        return '内置'
      } else {
        return item.author
      }
    } else if (item.type === CollectionType.custom) {
      return '自定义'
    } else if (item.type === CollectionType.workflow) {
      return '工作流'
    }
  }
})
const getIsDisabled = computed(() => {
  return (group, tool) => {
    return props.selectedTools.some(v => v.provider_name === group.name && v.tool_name === tool.name)
  }
})
const handleOk = () => {
  emits('closeToolPickerModal', 'ok')
}
const handleCancel = () => {
  emits('closeToolPickerModal', 'cancel')
}

/**
 * 获取工具。在这里重新获取吧，要不然tools不一定有值，还是要重新调用一次。不如直接在这里调用。
 */
// 源码是定义了三个变量，然后分别组合。==> 顺序问题的话，还是三个变量ok。
const collectionList = ref([])
const getTools = async (type) => {
  const res = await getToolsHttp(type)
  collectionList.value.push(...res)
}

/**
 * 选择工具后，直接同步了。不需要点确认按钮。
 * @param provider
 * @param payload
 */
const handleSelectTool = (provider, payload) => {
  const isDisabled = props.selectedTools.some(v => v.provider_name === provider.name && v.tool_name === payload.name)
  if (isDisabled) {
    return false
  }
  const params: Record<string, string> = {}
  if (payload.parameters) {
    payload.parameters.forEach((item) => {
      params[item.name] = ''
    })
  }
  const tool = {
    provider_id: provider.id,
    provider_type: provider.type,
    provider_name: provider.name,
    tool_name: payload.name,
    tool_label: renderName(payload.label),
    tool_description: renderName(payload.description),
    title: renderName(payload.label),
    notAuthor: provider.is_team_authorization,
    enabled: true,
    tool_parameters: params
  }
  emits('addTool', tool)
}
onMounted(() => {
  getTools(CollectionType.builtIn)
  getTools(CollectionType.custom)
  getTools(CollectionType.workflow)
})
</script>
<style scoped lang="scss">
.tool-item {
  &:hover {
    background: #c8cedd33;
  }

  .added {
    display: none;
  }

  &.disabled {
    opacity: 0.5;
    //pointer-events: none;

    .added {
      display: inline-block;
    }
  }
}
.demo-basic {
  padding: 10px;
  width: 200px;
  background-color: var(--color-bg-popup);
  border-radius: 4px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);
}
</style>
