<template>
  <div>
    <!--form要组件处理了：value和formSchemas两个参数。-->
    <!--TODO：1.类型没有处理完。2.内容较多，没有具体看完，需要细节处理；3.简单处理了，后续补作业。-->
    <a-form ref="formRef" :model="formValues" layout="vertical">
      <template v-for="(item, index) in formSchema" :key="index">
        <a-form-item :field="item.variable" :label="renderName(item.label)" :tooltip="renderName(item.tooltip)">
          <template v-if="item.type === FormTypeEnum.textInput">
            <!--default没有值，会取value里面的值-->
            <a-input v-model="formValues[item.variable]" placeholder="请输入" allow-clear />
          </template>
          <template v-if="item.type === FormTypeEnum.secretInput">
            <a-input-password v-model="formValues[item.variable]" allow-clear />
          </template>
          <template v-if="item.type === FormTypeEnum.textNumber">
            <a-input-number v-model="formValues[item.variable]" allow-clear />
          </template>
          <template v-if="item.type === FormTypeEnum.radio">
            <a-radio-group v-model="formValues[item.variable]">
              <a-radio v-for="(option, optionIndex) in item.options" :key="optionIndex" :value="option.value">
                {{ renderName(option.label) }}
              </a-radio>
            </a-radio-group>
          </template>
          <template v-if="item.type === FormTypeEnum.select">
            <a-select v-model="formValues[item.variable]" placeholder="请选择" allow-clear>
              <a-option v-for="(option, optionIndex) in item.options" :key="optionIndex" :value="option.value">
                {{ renderName(option.label) }}
              </a-option>
            </a-select>
          </template>
          <template v-if="item.type === FormTypeEnum.boolean">
            <a-radio-group v-model="formValues[item.variable]" type="button">
              <a-radio :value="1">True</a-radio>
              <a-radio :value="0">False</a-radio>
            </a-radio-group>
          </template>
        </a-form-item>
        <!--  其他类型：modelSelector、toolSelector、multiToolSelector、appSelector -->
      </template>
    </a-form>
  </div>
</template>
<script setup lang="ts">
import { FormTypeEnum } from '@/apis/model-mgmt/type'
import { renderName } from '@/views/app/workflow/utils/configuration'

const props = defineProps(['settingSchemas', 'formValues'])
const formSchema = ref({ ...props.settingSchemas })
const formValues = ref({ ...props.formValues })
defineExpose({
  formValues
})
</script>

<style scoped lang="scss"></style>
