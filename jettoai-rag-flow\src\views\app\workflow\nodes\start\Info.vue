<template>
  <div class="field-info">
    <div class="flex items-center justify-between mb-1">
      <span>输入字段</span>
      <a-button style="width: 24px; height: 24px" type="text" @click="onAdd">
        <template #icon>
          <icon-plus />
        </template>
      </a-button>
    </div>
    <Fieldlist :list="fieldList" @edit="onEdit" @delete="onDelete" />
    <AddFieldModal ref="AddFieldModalRef" @save-success="onSave" />
    <EditFieldModal ref="EditFieldModalRef" @save-success="onEditSave" />
  </div>
</template>

<script setup lang="ts">
import Fieldlist from './components/FieldList.vue'
import AddFieldModal from './components/AddFieldModal.vue'
import EditFieldModal from './components/EditFieldModal.vue'

interface FieldType {
  label: string
  max_length: number
  options: string[]
  required: boolean
  type: string
  variable: string
  allowed_file_types?: string[]
  allowed_file_upload_methods?: string[]
  allowed_file_extensions?: string[]
}
const props = withDefaults(
  defineProps<{
    node?: any
  }>(),
  {
    node: {}
  }
)
const emit = defineEmits<{
  (e: 'change', data: any): void
}>()
const AddFieldModalRef = ref<InstanceType<typeof AddFieldModal>>()
const EditFieldModalRef = ref<InstanceType<typeof EditFieldModal>>()

// 新增
const onAdd = () => {
  AddFieldModalRef.value?.onAdd()
}

// 编辑
const onEdit = (item: FieldType, index: number) => {
  EditFieldModalRef.value?.onEdit(item, index)
}

// 删除
const onDelete = (index: number) => {
  fieldList.value.splice(index, 1)
  console.log('删除字段，当前列表:', fieldList.value)
}

const fieldList = ref<FieldType[]>(props.node.variables)

const onSave = (e: any) => {
  fieldList.value.unshift(e)
  console.log('添加的字段:', e)
}

// 编辑保存
const onEditSave = (e: any) => {
  // 更新指定索引的字段
  if (e.index !== undefined && e.index >= 0 && e.index < fieldList.value.length) {
    fieldList.value[e.index] = e
    console.log('编辑的字段:', e, '索引:', e.index)
  }
}
</script>
<style scoped lang="scss">
.field-info {
  display: flex;
  flex-direction: column;
  background-color: var(--color-bg-1);
}
</style>
