<template>
  <div class="field-info">
    <a-form size="small" :model="form" layout="vertical">
      <a-form-item field="variable_selector" label="输入变量">
        <VariableSelector v-model:value-selector="form.variable_selector" :nodeId="nodeId" />
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
import VariableSelector from '@/views/app/workflow/components/variable-selector'

const props = withDefaults(
  defineProps<{
    node?: any
    nodeId?: string
  }>(),
  {
    node: {},
    nodeId: ''
  }
)
const form = ref({
  variable_selector: []
})
form.value = props.node
</script>
<style scoped lang="scss">
.field-info {
  display: flex;
  flex-direction: column;
  background-color: var(--color-bg-1);
}

:deep(.arco-select-option-content) {
  width: 100%;
}
</style>
