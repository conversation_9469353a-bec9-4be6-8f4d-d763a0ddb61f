<template>
  <div ref="editorContainerRef" class="ai-prompt-editor" :class="{ focused: isFocused }">
    <div ref="editorWrapperRef" class="editor-wrapper" />
    <!-- 用于关键字触发的自定义组件插槽 -->
    <slot name="keyword-trigger" :trigger="keywordTrigger" :position="triggerPosition" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onBeforeUnmount } from 'vue'
import {
  EditorView,
  keymap,
  highlightSpecialChars,
  drawSelection,
  Decoration,
  WidgetType,
  ViewPlugin,
  DecorationSet
} from '@codemirror/view'
import { EditorState, Extension, StateField, StateEffect, RangeSetBuilder } from '@codemirror/state'
import { history, historyKeymap } from '@codemirror/commands'
import { markdown, markdownLanguage } from '@codemirror/lang-markdown'
import { defaultKeymap, indentWithTab, insertNewlineAndIndent } from '@codemirror/commands'
import { syntaxHighlighting, defaultHighlightStyle } from '@codemirror/language'

defineOptions({
  name: 'AiPromptEditor'
})

// 属性定义
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '在此输入提示词...'
  },
  height: {
    type: String,
    default: '200px'
  },
  triggerChars: {
    type: Array as () => string[],
    default: () => []
  },
  maxHeight: {
    type: String,
    default: '200px'
  },
  minHeight: {
    type: String,
    default: '100px'
  },
  autoHeight: {
    type: Boolean,
    default: true
  },
  tabSize: {
    type: Number,
    default: 2
  },
  // 添加变量空间功能开关
  enableVariableSpace: {
    type: Boolean,
    default: false
  }
})

// 事件定义
const emit = defineEmits([
  'update:modelValue',
  'change',
  'ready',
  'focus',
  'blur',
  'keyword-trigger',
  'trigger-close',
  'metadata-update',
  'variable-update' // 添加变量更新事件
])

// 编辑器内容和引用
const editorContent = ref(props.modelValue)
const editorInstance = ref<EditorView | null>(null)
const editorContainerRef = ref<HTMLElement | null>(null)
const editorWrapperRef = ref<HTMLElement | null>(null)
const isFocused = ref(false)

// 关键字触发状态
const keywordTrigger = ref<string | null>(null)
const triggerPosition = ref({ x: 0, y: 0, line: 0, ch: 0 })
const triggerText = ref('')

// 变量空间相关状态
const variables = ref<string[]>([])

// 元数据存储
const metadata = ref<Record<number, { tag: string; attributes?: Record<string, string> }>>({})

// 模板小部件存储
interface TemplateWidgetData {
  id: string
  from: number
  to: number
  text: string
  tag: string
  attributes?: Record<string, string>
}

const templateWidgets = ref<TemplateWidgetData[]>([])

// 为模板小部件创建唯一ID
const createWidgetId = (() => {
  let counter = 0
  return () => `template-widget-${counter++}`
})()

// 添加模板小部件效果
const addTemplateWidgetEffect = StateEffect.define<TemplateWidgetData>()
const removeTemplateWidgetEffect = StateEffect.define<string>()

// 模板小部件状态字段
const templateWidgetField = StateField.define<DecorationSet>({
  create() {
    return Decoration.none
  },
  update(value, tr) {
    // 首先映射现有装饰以适应文档变更
    value = value.map(tr.changes)

    // 处理添加和删除效果
    for (const effect of tr.effects) {
      if (effect.is(addTemplateWidgetEffect)) {
        const { id, from, to, text } = effect.value

        // 使用replace装饰，但设置为不可编辑，这样可以防止文本重复
        const widget = Decoration.replace({
          widget: new TemplateWidget(text, id),
          inclusive: false, // 不包含在编辑操作中
          block: false // 不是块级元素
        }).range(from, to)

        value = value.update({ add: [widget] })
      } else if (effect.is(removeTemplateWidgetEffect)) {
        const id = effect.value
        value = value.update({
          filter: (from, to, value) => {
            return (value as any).spec?.widget?.id !== id
          }
        })
      }
    }

    return value
  },
  provide: (field) => EditorView.decorations.from(field)
})

// 模板小部件类
class TemplateWidget extends WidgetType {
  constructor(
    readonly text: string,
    readonly id: string
  ) {
    super()
  }

  eq(other: TemplateWidget) {
    return other.text === this.text && other.id === this.id
  }

  toDOM() {
    const span = document.createElement('span')
    span.textContent = this.text
    span.dataset.widgetId = this.id
    span.className = 'template-widget'

    // 防止文本选择，提高用户体验
    span.style.userSelect = 'none'

    return span
  }

  // 允许编辑器处理事件，这样光标可以正确定位
  ignoreEvent(event: Event) {
    // 允许鼠标点击事件，以便用户可以通过点击定位光标
    if (event.type === 'mousedown' || event.type === 'mouseup' || event.type === 'click') {
      return false
    }

    // 对于其他事件，默认处理
    return false
  }
}

// 监听外部值变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue !== editorContent.value) {
      editorContent.value = newValue
      updateEditorContent(newValue)
    }
  }
)

// 监听变量空间功能启用状态变化
watch(
  () => props.enableVariableSpace,
  () => {
    // 当变量空间功能状态变化时，重新更新编辑器视图
    if (editorInstance.value) {
      // 强制编辑器重新渲染以应用或移除变量空间相关的装饰
      editorInstance.value.dispatch({
        effects: StateEffect.reconfigure.of(extensions.value)
      })
    }
  }
)

// 更新编辑器内容而不触发变更事件
const updateEditorContent = (content: string) => {
  if (!editorInstance.value) return

  const currentValue = editorInstance.value.state.doc.toString()
  if (content !== currentValue) {
    editorInstance.value.dispatch({
      changes: {
        from: 0,
        to: currentValue.length,
        insert: content
      }
    })
  }
}

// 自定义主题以移除活动行高亮
const customTheme = EditorView.theme({
  '&': {
    height: props.autoHeight ? 'auto' : props.height,
    minHeight: props.minHeight,
    maxHeight: props.maxHeight,
    fontSize: '14px'
  },
  '.cm-content': {
    fontFamily: 'inherit',
    caretColor: 'inherit'
  },
  '.cm-line': {
    padding: '0 4px',
    lineHeight: '1.6'
  },
  // 移除活动行高亮
  '.cm-activeLine': {
    backgroundColor: 'transparent'
  },
  // 自定义占位符样式
  '.cm-placeholder': {
    color: '#aaa',
    display: 'inline-block'
  }
})

// 自定义占位符组件
class PlaceholderWidget extends WidgetType {
  constructor(private readonly text: string) {
    super()
  }

  toDOM() {
    const span = document.createElement('span')
    span.textContent = this.text
    span.className = 'cm-placeholder'
    return span
  }

  eq(other: PlaceholderWidget) {
    return other.text === this.text
  }
}

// 占位符扩展
const placeholderExt = EditorView.decorations.of((view) => {
  if (view.state.doc.length > 0) return Decoration.none
  return Decoration.set([
    Decoration.widget({
      widget: new PlaceholderWidget(props.placeholder),
      side: 1
    }).range(0)
  ])
})

// 自定义回车键映射
const enterKeymap = keymap.of([
  {
    key: 'Enter',
    run: (view) => {
      // 在当前光标位置插入换行符
      const pos = view.state.selection.main.head
      view.dispatch({
        changes: { from: pos, insert: '\n' },
        selection: { anchor: pos + 1 }
      })
      return true
    }
  }
])

// 自定义按键映射
const deleteKeymap = keymap.of([
  {
    key: 'Backspace',
    run: (view) => {
      // 获取当前光标位置
      const pos = view.state.selection.main.head

      // 如果有选择范围，不处理小部件删除
      if (!view.state.selection.main.empty) {
        return false
      }

      // 检查是否有模板小部件在光标前面
      const widget = findWidgetAtPosition(pos - 1)

      if (widget && pos - 1 === widget.to - 1) {
        // 只有当光标紧贴在小部件后面时才删除小部件
        deleteWidget(widget, view)
        return true // 阻止默认行为
      }

      return false // 继续默认行为
    }
  },
  {
    key: 'Delete',
    run: (view) => {
      // 获取当前光标位置
      const pos = view.state.selection.main.head

      // 如果有选择范围，不处理小部件删除
      if (!view.state.selection.main.empty) {
        return false
      }

      // 检查是否有模板小部件在光标位置
      const widget = findWidgetAtPosition(pos)

      if (widget && (pos === widget.from || (pos > widget.from && pos < widget.to))) {
        // 只有当光标紧贴在小部件前面或在小部件内部时才删除小部件
        deleteWidget(widget, view)
        return true // 阻止默认行为
      }

      return false // 继续默认行为
    }
  }
])

// 查找指定位置的模板小部件
const findWidgetAtPosition = (pos: number) => {
  // 对于模板小部件，我们需要精确检查位置
  // 1. 对于Backspace键（pos-1），只有当光标紧贴小部件后面时才删除小部件
  // 2. 对于Delete键（pos），只有当光标紧贴小部件前面或在小部件内部时才删除小部件

  return templateWidgets.value.find((widget) => {
    // 检查是否在小部件内部
    if (pos > widget.from && pos < widget.to) {
      return true
    }

    // 检查是否在小部件前面（Delete键）
    if (pos === widget.from) {
      return true
    }

    // 检查是否在小部件后面（Backspace键）
    // 注意：这里必须是严格等于，否则会误删除
    if (pos - 1 === widget.to - 1) {
      return true
    }

    return false
  })
}

// 删除模板小部件
const deleteWidget = (widget: TemplateWidgetData, view: EditorView) => {
  if (!widget) return

  // 删除元数据
  delete metadata.value[widget.from]

  // 从列表中移除
  templateWidgets.value = templateWidgets.value.filter((w) => w.id !== widget.id)

  // 创建删除效果
  const effects = [removeTemplateWidgetEffect.of(widget.id)]

  // 删除文本内容
  view.dispatch({
    changes: { from: widget.from, to: widget.to, insert: '' },
    effects
  })

  // 触发元数据更新事件
  emit('metadata-update', { ...metadata.value })
}

// 焦点处理扩展
const focusHandlingExt = EditorView.domEventHandlers({
  focus: () => {
    isFocused.value = true
    emit('focus')
    return false
  },
  blur: () => {
    isFocused.value = false
    emit('blur')
    return false
  }
})

// 添加花括号自动补全的按键映射
const bracesKeymap = keymap.of([
  {
    key: '{',
    run: (view) => {
      // 如果未启用变量空间功能，不处理
      if (!props.enableVariableSpace) return false

      try {
        // 获取当前光标位置
        const pos = view.state.selection.main.head

        // 在选择范围内的情况
        if (!view.state.selection.main.empty) {
          // 获取选择范围
          const { from, to } = view.state.selection.main
          const selectedText = view.state.doc.sliceString(from, to)

          // 用花括号包围选中的文本
          view.dispatch({
            changes: {
              from,
              to,
              insert: `{${selectedText}}`
            },
            selection: { anchor: to + 1 } // 光标位置在结束花括号前
          })
          return true
        }

        // 插入一对花括号，并将光标定位在中间
        view.dispatch({
          changes: { from: pos, insert: '{}' },
          selection: { anchor: pos + 1 }
        })
        return true
      } catch (error) {
        console.error('花括号自动补全出错:', error)
        return false // 让默认行为处理
      }
    }
  }
])

// 变量空间状态字段
const variableSpaceField = StateField.define<DecorationSet>({
  create() {
    return Decoration.none
  },
  update(value, tr) {
    // 如果未启用变量空间功能，则返回空装饰集
    if (!props.enableVariableSpace) return Decoration.none

    try {
      // 如果文档没有变化且没有配置变更，仅映射现有装饰以适应可能的文档变更
      if (!tr.docChanged) return value.map(tr.changes)

      // 映射现有装饰以适应文档变更
      value = value.map(tr.changes)

      // 创建一个新的 RangeSet builder
      const builder = new RangeSetBuilder<Decoration>()
      const doc = tr.state.doc
      const content = doc.toString()

      // 使用更简单的正则表达式匹配 {{变量名}} 模式
      const regex = /\{\{([a-zA-Z0-9_]+)\}\}/g
      const foundVariables: string[] = []

      let match
      while ((match = regex.exec(content)) !== null) {
        const from = match.index
        const to = from + match[0].length
        const variableName = match[1]

        // 确保我们不会尝试装饰文档范围之外的区域
        if (from >= 0 && to <= content.length) {
          // 将变量添加到列表中
          foundVariables.push(variableName)

          // 使用最简单和最可靠的方式创建装饰，避免复杂的链式调用
          const exprDeco = Decoration.mark({ class: 'jinja-expression' })
          builder.add(from, to, exprDeco)

          const varDeco = Decoration.mark({ class: 'valid' })
          builder.add(from + 2, to - 2, varDeco)
        }
      }

      // 只在变量列表发生变化时更新
      if (JSON.stringify(variables.value) !== JSON.stringify(foundVariables)) {
        variables.value = foundVariables
        nextTick(() => {
          emit('variable-update', foundVariables)
        })
      }

      return builder.finish()
    } catch (error) {
      console.error('变量空间处理出错:', error)
      // 发生错误时返回一个安全的空装饰集
      return Decoration.none
    }
  },
  provide: (field) => EditorView.decorations.from(field)
})

// 双花括号处理插件 - 更稳定的实现
const doubleBracesHandler = ViewPlugin.fromClass(
  class {
    constructor(_view) {
      // 无需初始化
    }

    update(update) {
      // 如果功能未启用或文档未变化，直接返回
      if (!props.enableVariableSpace || !update.docChanged) return

      try {
        // 只在最近的变更是用户输入时处理
        const isUserChange = update.transactions.some((tr) => tr.isUserEvent('input'))
        if (!isUserChange) return

        // 获取当前光标位置
        const pos = update.state.selection.main.head

        // 确保我们有足够的字符来检查
        if (pos < 2) return

        const doc = update.state.doc.toString()
        const prevTwoChars = doc.substring(pos - 2, pos)

        // 检查是否刚刚输入了 {{
        if (prevTwoChars === '{{') {
          // 自动补全为 {{}}，并将光标放在中间
          update.view.dispatch({
            changes: { from: pos, insert: '}}' },
            selection: { anchor: pos }
          })
        }
      } catch (error) {
        // 确保错误不会阻塞编辑器功能
        console.error('双花括号处理出错:', error)
      }
    }
  }
)

// 定义编辑器扩展
const extensions = computed<Extension[]>(() => [
  // 基本编辑器设置
  highlightSpecialChars(),
  history(),
  drawSelection(),
  syntaxHighlighting(defaultHighlightStyle),

  // 模板小部件字段
  templateWidgetField,

  // 条件性添加变量空间功能
  ...(props.enableVariableSpace ? [variableSpaceField, doubleBracesHandler] : []),

  // 自定义按键映射必须在默认键映射之前以覆盖它
  bracesKeymap,
  deleteKeymap,
  enterKeymap,

  // 默认键映射
  keymap.of([...defaultKeymap, ...historyKeymap, indentWithTab]),

  // Markdown 支持
  markdown({
    base: markdownLanguage,
    codeLanguages: []
  }),

  // 自定义扩展
  EditorView.lineWrapping,
  customTheme,
  placeholderExt,
  focusHandlingExt,

  // 隐藏行号
  EditorView.theme({
    '.cm-gutters': { display: 'none' }
  }),

  // 禁用活动行高亮
  EditorView.theme({
    '.cm-activeLine': { backgroundColor: 'transparent' }
  }),

  // 设置制表符大小
  EditorState.tabSize.of(props.tabSize),

  // 监听变化并更新模型
  EditorView.updateListener.of((update) => {
    if (update.docChanged) {
      const value = update.state.doc.toString()
      editorContent.value = value
      emit('update:modelValue', value)
      emit('change', value)

      // 检查关键字触发
      checkForTriggers(value)

      // 处理模板小部件的删除
      handleTemplateWidgetChanges(update)
    }
  })
])

// 初始化编辑器
const initEditor = () => {
  if (!editorWrapperRef.value) return

  // 创建编辑器状态
  const state = EditorState.create({
    doc: props.modelValue,
    extensions: extensions.value
  })

  // 创建编辑器视图
  editorInstance.value = new EditorView({
    state,
    parent: editorWrapperRef.value
  })

  // 触发就绪事件
  nextTick(() => {
    emit('ready', { view: editorInstance.value })
  })
}

// 在组件卸载时清理编辑器
const destroyEditor = () => {
  if (editorInstance.value) {
    editorInstance.value.destroy()
    editorInstance.value = null
  }
}

// 在挂载时初始化编辑器
onMounted(() => {
  initEditor()
})

// 在卸载前销毁编辑器
onBeforeUnmount(() => {
  destroyEditor()
})

// 获取光标在视口中的坐标
const getCursorCoords = () => {
  if (!editorInstance.value) return { x: 0, y: 0 }

  const view = editorInstance.value
  const pos = view.state.selection.main.head
  const coords = view.coordsAtPos(pos)

  if (!coords) return { x: 0, y: 0 }

  // 获取编辑器元素位置
  const editorRect = view.dom.getBoundingClientRect()

  return {
    x: coords.left - editorRect.left,
    y: coords.bottom - editorRect.top
  }
}

// 获取光标在文档中的坐标（行，列）
const getCursorPosition = () => {
  if (!editorInstance.value) return { line: 0, ch: 0 }

  const view = editorInstance.value
  const pos = view.state.selection.main.head
  const line = view.state.doc.lineAt(pos)

  return {
    line: line.number - 1, // 基于0的行号
    ch: pos - line.from // 基于0的列号
  }
}

// 检查关键字触发
const checkForTriggers = (content: string) => {
  if (!content || content.length === 0) {
    closeTrigger()
    return
  }
  const view = editorInstance.value
  const pos = view.state.selection.main.head
  const prevChar = content.substring(pos - 1, pos)

  if (props.triggerChars.includes(prevChar)) {
    const { x, y } = getCursorCoords()
    const { line, ch } = getCursorPosition()

    // 更新触发位置
    triggerPosition.value = { x, y, line, ch }
    keywordTrigger.value = prevChar
    triggerText.value = ''

    // 触发事件
    emit('keyword-trigger', {
      trigger: prevChar,
      position: { x, y, line, ch },
      text: ''
    })
  } else if (keywordTrigger.value) {
    // 如果已经处于触发状态，更新触发文本
    const triggerChar = keywordTrigger.value
    const parts = content.split(triggerChar)

    if (parts.length > 1) {
      const lastPart = parts[parts.length - 1]

      // 检查是否有空格，这将结束触发
      if (lastPart.includes(' ')) {
        closeTrigger()
      } else {
        triggerText.value = lastPart

        // 触发更新事件
        emit('keyword-trigger', {
          trigger: triggerChar,
          position: triggerPosition.value,
          text: lastPart
        })
      }
    } else {
      closeTrigger()
    }
  }
}

// 关闭触发
const closeTrigger = () => {
  if (keywordTrigger.value) {
    keywordTrigger.value = null
    triggerText.value = ''
    emit('trigger-close')
  }
}

// 在光标处插入文本
const insertText = (text: string) => {
  if (!editorInstance.value) return

  const view = editorInstance.value
  const pos = view.state.selection.main.head

  view.dispatch({
    changes: { from: pos, insert: text }
  })

  nextTick(() => {
    view.focus()
  })
}

// 在光标位置插入换行符
const insertNewline = () => {
  if (!editorInstance.value) return

  const view = editorInstance.value
  const pos = view.state.selection.main.head

  view.dispatch({
    changes: { from: pos, insert: '\n' },
    selection: { anchor: pos + 1 }
  })
}

// 用提供的文本替换触发文本
const replaceTrigger = (text: string) => {
  if (!editorInstance.value || !keywordTrigger.value) return

  const view = editorInstance.value
  const content = editorContent.value
  const triggerChar = keywordTrigger.value

  // 查找最后一个触发字符的位置
  const lastTriggerPos = content.lastIndexOf(triggerChar)

  if (lastTriggerPos >= 0) {
    // 从触发字符到当前光标位置替换
    view.dispatch({
      changes: {
        from: lastTriggerPos,
        to: view.state.selection.main.head,
        insert: text
      }
    })

    nextTick(() => {
      view.focus()
      closeTrigger()
    })
  }
}

// 聚焦编辑器
const focus = () => {
  editorInstance.value?.focus()
}

// 取消编辑器焦点
const blur = () => {
  if (editorInstance.value && editorInstance.value.dom) {
    editorInstance.value.dom.blur()
  }
}

// 模板插入接口定义
interface TemplateOptions {
  text: string
  tag: string
  attributes?: Record<string, string>
  cursorOffset?: number
}

// 插入模板
const insertTemplate = (options: string | TemplateOptions) => {
  if (!editorInstance.value) return

  // 如果是字符串，直接插入
  if (typeof options === 'string') {
    insertText(options)
    return
  }

  const { text, tag, attributes, cursorOffset = text.length } = options

  // 插入模板小部件（原数据模版）
  const view = editorInstance.value
  const pos = view.state.selection.main.head

  // 生成唯一ID
  const widgetId = createWidgetId()

  // 先插入文本，然后立即用装饰替换它
  view.dispatch({
    changes: { from: pos, insert: text },
    effects: addTemplateWidgetEffect.of({
      id: widgetId,
      from: pos,
      to: pos + text.length,
      text,
      tag,
      attributes
    })
  })

  // 保存模板小部件数据
  templateWidgets.value.push({
    id: widgetId,
    from: pos,
    to: pos + text.length,
    text,
    tag,
    attributes
  })

  // 保存元数据
  metadata.value[pos] = { tag, attributes }

  // 触发元数据更新事件
  emit('metadata-update', { ...metadata.value })

  // 设置光标位置到模板后面
  nextTick(() => {
    const newPos = pos + text.length
    view.dispatch({
      selection: { anchor: newPos }
    })
    view.focus()
  })
}

// 获取元数据
const getMetadata = () => {
  return { ...metadata.value }
}

// 清除元数据
const clearMetadata = () => {
  metadata.value = {}

  // 清除所有模板小部件
  if (editorInstance.value) {
    const effects = templateWidgets.value.map((widget) => removeTemplateWidgetEffect.of(widget.id))

    if (effects.length > 0) {
      editorInstance.value.dispatch({
        effects
      })
    }
  }

  templateWidgets.value = []
  emit('metadata-update', {})
}

// 获取编辑器内容，包括HTML标签
const getContentWithHtml = () => {
  if (!editorInstance.value) return editorContent.value

  let content = editorInstance.value.state.doc.toString()

  // 按照位置倒序排序，这样插入标签不会影响后续位置
  const sortedWidgets = [...templateWidgets.value].sort((a, b) => b.from - a.from)

  for (const widget of sortedWidgets) {
    const { from, to, text, tag, attributes } = widget
    const attrStr = attributes
      ? ' ' +
        Object.entries(attributes)
          .map(([k, v]) => `${k}="${v}"`)
          .join(' ')
      : ''

    const htmlTag = `<${tag}${attrStr}>${text}</${tag}>`
    content = content.substring(0, from) + htmlTag + content.substring(to)
  }

  // 再次检查并移除可能在处理过程中产生的widgetBuffer标签
  content = content.replace(/<img class="cm-widgetBuffer" aria-hidden="true">/g, '')

  return content
}

// 处理模板小部件的变更
const handleTemplateWidgetChanges = (update: any) => {
  if (!update.docChanged || templateWidgets.value.length === 0) return

  // 获取变更信息
  const changes = update.changes
  const changedWidgets: TemplateWidgetData[] = []
  const widgetsToRemove: string[] = []

  // 检查每个模板小部件是否受到影响
  templateWidgets.value.forEach((widget) => {
    // 获取小部件的新位置
    const newFrom = changes.mapPos(widget.from, 1) // 1表示偏向右侧
    const newTo = changes.mapPos(widget.to, -1) // -1表示偏向左侧

    // 如果位置变为负数或长度为0，说明小部件被删除了
    if (newFrom < 0 || newTo <= newFrom) {
      widgetsToRemove.push(widget.id)
      // 同时删除相关元数据
      delete metadata.value[widget.from]
    } else if (newFrom !== widget.from || newTo !== widget.to) {
      // 位置发生变化，需要更新
      changedWidgets.push({
        ...widget,
        from: newFrom,
        to: newTo
      })

      // 如果元数据位置发生变化，需要更新
      if (metadata.value[widget.from]) {
        const metaData = metadata.value[widget.from]
        delete metadata.value[widget.from]
        metadata.value[newFrom] = metaData
      }
    }
  })

  // 处理需要删除的小部件
  if (widgetsToRemove.length > 0) {
    // 从模板小部件列表中移除
    templateWidgets.value = templateWidgets.value.filter((widget) => !widgetsToRemove.includes(widget.id))

    // 如果编辑器实例存在，发送删除效果
    if (editorInstance.value && widgetsToRemove.length > 0) {
      const effects = widgetsToRemove.map((id) => removeTemplateWidgetEffect.of(id))
      editorInstance.value.dispatch({ effects })
    }

    // 触发元数据更新事件
    emit('metadata-update', { ...metadata.value })
  }

  // 更新位置变化的小部件
  if (changedWidgets.length > 0) {
    changedWidgets.forEach((newWidget) => {
      const index = templateWidgets.value.findIndex((w) => w.id === newWidget.id)
      if (index >= 0) {
        templateWidgets.value[index] = newWidget
      }
    })

    // 触发元数据更新事件
    emit('metadata-update', { ...metadata.value })
  }
}

// 获取变量列表
const getVariables = () => {
  return [...variables.value]
}

// 公开可通过模板引用访问的方法
defineExpose({
  focus,
  blur,
  getEditor: () => editorInstance.value,
  insertText,
  insertNewline,
  replaceTrigger,
  closeTrigger,
  insertTemplate,
  getMetadata,
  clearMetadata,
  getContentWithHtml,
  getVariables // 添加获取变量列表方法
})
</script>

<style scoped lang="scss">
.ai-prompt-editor {
  border: 1px solid var(--color-border-3);
  border-radius: 4px;
  position: relative;
  transition: border-color 0.2s ease;
  width: 100%;

  &.focused {
    border-color: rgb(var(--primary-6));
  }

  .editor-wrapper {
    height: 100%;
    width: 100%;
  }

  :deep(.cm-editor) {
    outline: none;
  }

  :deep(.cm-scroller) {
    font-family: inherit;
    line-height: 1.55;
  }

  :deep(.cm-content) {
    white-space: pre-wrap;
    word-break: break-word;
  }

  // 模板小部件样式
  :deep(.template-widget) {
    display: inline-block;
    padding: 0 4px;
    margin: 0 1px;
    border-radius: 3px;
    background-color: rgba(59, 130, 246, 0.1);
    border-bottom: 1px dashed #3b82f6;
    cursor: pointer;
    position: relative;
    z-index: 2;
    white-space: nowrap;
    overflow: hidden;
    vertical-align: middle;

    &:hover {
      background-color: rgba(59, 130, 246, 0.2);
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }

    &:after {
      content: '';
      position: absolute;
      right: 0;
      top: 0;
      height: 100%;
      width: 2px;
      background-color: transparent;
    }
  }

  // 变量空间样式
  :deep(.jinja-expression) {
    display: inline;
    color: #0f766e;
  }

  // Markdown 元素样式
  :deep(.cm-header) {
    color: #1a56db;
    font-weight: bold;
  }

  :deep(.cm-strong) {
    font-weight: bold;
  }

  :deep(.cm-emphasis) {
    font-style: italic;
  }

  :deep(.cm-link) {
    color: #2563eb;
    text-decoration: underline;
  }

  :deep(.cm-url) {
    color: #64748b;
  }

  :deep(.cm-quote) {
    color: #4b5563;
    font-style: italic;
    border-left: 3px solid #e5e7eb;
    padding-left: 8px;
    margin-left: 0;
  }

  :deep(.cm-code) {
    background-color: #f1f5f9;
    border-radius: 3px;
    padding: 0 4px;
    font-family: monospace;
  }
}
</style>
