<template>
  <div style="display: flex; justify-content: space-between">
    <template v-if="nodeitem.key == 'type'">
      <a-select
        v-model="nodeitem.value"
        :style="{ width: '100%' }"
        placeholder="Please select ..."
        @update:model-value="$emit('update:nodeitem', { ...nodeitem, value: $event })"
        @change="changeselect"
      >
        <a-option v-for="item in typelist" :key="item.value" :value="item.value" :label="item.label" />
      </a-select>
    </template>
    <template v-else-if="nodeitem.key == 'size'">
      <a-select v-model="sizetype" :style="{ width: '38%' }" placeholder="Please select ..." @change="handlesizeChange">
        <a-option v-for="item in sizelist" :key="item.value" :value="item.value" :label="item.label" />
      </a-select>
      <a-input
        v-model="nodeitem.value"
        :style="{ width: '60%' }"
        type="number"
        placeholder="Please input ..."
        @update:model-value="$emit('update:nodeitem', { ...nodeitem, value: $event })"
      />
      <!-- <a-select :style="{width:'50%'}" placeholder="Please select ...">
             <a-option v-for="item in  defaultList"  :key="item.value" :value="item.value" :label="item.label"></a-option>
       </a-select> -->
    </template>
    <template v-else-if="nodeitem.key == 'transfer_method'">
      <a-select
        v-model="nodeitem.value"
        :style="{ width: '100%' }"
        placeholder="Please select ..."
        @update:model-value="$emit('update:nodeitem', { ...nodeitem, value: $event })"
      >
        <a-option v-for="item in transfer_methodlist" :key="item.value" :value="item.value" :label="item.label" />
      </a-select>
    </template>
    <template v-else>
      <a-input
        v-model="nodeitem.value"
        placeholder="Please input ..."
        @update:model-value="$emit('update:nodeitem', { ...nodeitem, value: $event })"
      />
    </template>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  nodeitem?: any
  defaultList?: any[]
}>()
const emit = defineEmits(['update:nodeitem'])
const sizetype = ref('')
const typelist = [
  {
    value: 'image',
    label: '图片'
  },
  {
    value: 'document',
    label: '文档'
  },
  {
    value: 'audio',
    label: '音频'
  },
  {
    value: 'video',
    label: '视频'
  }
]
const sizelist = [
  {
    value: 'Constant',
    label: 'Constant'
  },
  {
    value: 'variable',
    label: 'variable'
  }
]
const transfer_methodlist = [
  {
    value: 'local_file',
    label: '本地上传'
  },
  {
    value: 'remote_url',
    label: 'URL'
  }
]
const handlesizeChange = () => {
  if (sizetype.value === 'variable') {
    emit('update:nodeitem', { ...props.nodeitem, numberVarType: sizetype.value })
  }
  emit('update:nodeitem', { ...props.nodeitem, varType: 'number' })
}
const changeselect = (value: string) => {
  console.log(value)

  emit('update:nodeitem', { ...props.nodeitem, value: [value] })
}
// watch(() => props.nodeitem.key, (newValue) => {
//   console.log(newValue)
// })
</script>

<style scoped lang="scss">
/* 你可以根据需要添加样式 */
</style>
