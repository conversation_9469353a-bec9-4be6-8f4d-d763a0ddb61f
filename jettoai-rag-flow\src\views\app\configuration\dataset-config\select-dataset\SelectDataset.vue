<template>
  <div>
    <a-modal :visible="true" title="选择引用知识库" @cancel="handleOk('cancel')" @ok="handleOk('ok')">
      <div>
        <a-list
          :scrollbar="true"
          :bordered="false"
          :max-height="240"
          class="table-container"
          @reach-bottom="fetchData"
        >
          <template #scroll-loading>
            <div v-if="bottom">已全部加载</div>
            <a-spin v-else />
          </template>
          <!--overflow-y-auto max-h-[286px]-->
          <div class="mt-7 space-y-4">
            <div
              v-for="item in datasets"
              :key="item.id"
              class="flex h-10 cursor-pointer items-center justify-between rounded-lg border-[1px] border-components-panel-border-subtle bg-components-panel-on-panel-item-bg px-2 shadow-xs hover:border-components-panel-border hover:bg-components-panel-on-panel-item-bg-hover hover:shadow-sm"
              :class="{ active: selectedList.some(i=> i.id === item.id),disabled: !item.embedding_available}"
              @click="toggleSelect(item)"
            >
              <div class="max-w-[200px] truncate text-[13px] font-medium text-text-secondary">
                {{ item.name }}
              </div>

              <div class="mr-1 flex items-center overflow-hidden">
                <span
                  v-if="!item.embedding_available"
                  class="ml-1 shrink-0 rounded-md border border-divider-deep px-1 text-xs font-normal leading-[18px] text-text-tertiary">
                  不可用
                </span>
                <div v-if="item.indexing_technique" class="ml-4">
                  <a-tag size="small">
                    {{
                      formatIndexingTechniqueAndMethod(item.indexing_technique, item.retrieval_model_dict?.search_method)
                    }}
                  </a-tag>
                </div>
                <div v-if="item.provider === 'external'">
                  <a-tag size="small">外部</a-tag>
                </div>
              </div>
            </div>
          </div>
        </a-list>
      </div>
    </a-modal>
  </div>
</template>
<script setup lang="ts">
import { getDatasetList } from '@/apis'
import { formatIndexingTechniqueAndMethod } from '@/views/app/workflow/utils/configuration'

const props = defineProps(['selectedDatasetList'])
const emits = defineEmits(['closeDatasetDialog'])
// 所有选中的知识库
const selectedList = ref([...props.selectedDatasetList])

const handleOk = (type: string): void => {
  emits('closeDatasetDialog', type, selectedList)
}
// 所有知识库（接口查询的）
const datasets = ref([])
const page = ref(1)
const hasMore = ref(true)
const isLoading = ref(false)
const bottom = ref(false)
const getDatasetListFn = async () => {
  const params = {
    page: page.value,
    limit: 5
  }
  isLoading.value = true
  const { data, has_more } = await getDatasetList(params)
  hasMore.value = has_more
  datasets.value.push(...data)
  page.value++
  isLoading.value = false
  if (!hasMore.value) {
    bottom.value = true
  }
}
getDatasetListFn()

const fetchData = () => {
  if (hasMore.value && !isLoading.value) {
    getDatasetListFn()
  } else if (!hasMore.value) {
    bottom.value = true
  }
}

// 切换是否选中知识库
const toggleSelect = (item) => {
  const isSelected = selectedList.value.some(i => i.id === item.id)
  if (isSelected) {
    selectedList.value = selectedList.value.filter(i => i.id !== item.id)
  } else {
    selectedList.value.push(item)
  }
}
</script>
<style scoped lang="scss">
.table-container {
  //height: 300px;
  //overflow: auto;
}

.active {
  background: #eff4ff;
  border-color: #269dff;
}
.disabled {
  pointer-events: none;
  opacity: 0.6;
}
</style>
