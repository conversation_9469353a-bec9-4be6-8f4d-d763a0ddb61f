#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/f/ai大模型平台/node_modules/.pnpm/xss@1.0.15/node_modules/xss/bin/node_modules:/mnt/f/ai大模型平台/node_modules/.pnpm/xss@1.0.15/node_modules/xss/node_modules:/mnt/f/ai大模型平台/node_modules/.pnpm/xss@1.0.15/node_modules:/mnt/f/ai大模型平台/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/f/ai大模型平台/node_modules/.pnpm/xss@1.0.15/node_modules/xss/bin/node_modules:/mnt/f/ai大模型平台/node_modules/.pnpm/xss@1.0.15/node_modules/xss/node_modules:/mnt/f/ai大模型平台/node_modules/.pnpm/xss@1.0.15/node_modules:/mnt/f/ai大模型平台/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/xss" "$@"
else
  exec node  "$basedir/../../bin/xss" "$@"
fi
