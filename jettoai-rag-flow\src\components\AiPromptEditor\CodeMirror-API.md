# CodeMirror 6 API 使用说明文档

本文档提供了 CodeMirror 6 的详细 API 使用说明，特别针对在 Vue 3 组件中使用 CodeMirror 6 进行开发。

## 目录

- [基本概念](#基本概念)
- [核心模块](#核心模块)
- [编辑器状态](#编辑器状态)
- [编辑器视图](#编辑器视图)
- [文档操作](#文档操作)
- [选择与光标](#选择与光标)
- [命令系统](#命令系统)
- [事件处理](#事件处理)
- [扩展系统](#扩展系统)
- [装饰系统](#装饰系统)
- [语言支持](#语言支持)
- [主题与样式](#主题与样式)
- [实用工具](#实用工具)
- [Vue 3 集成](#vue-3-集成)
- [常见问题与解决方案](#常见问题与解决方案)

## 基本概念

CodeMirror 6 是一个完全重写的版本，采用了模块化设计和不可变数据结构。它的核心理念包括：

- **状态与视图分离**：编辑器状态（`EditorState`）与视图（`EditorView`）是分离的
- **不可变数据结构**：所有状态更改通过事务（`Transaction`）进行
- **扩展系统**：通过扩展（`Extension`）增强编辑器功能
- **装饰系统**：通过装饰（`Decoration`）改变文本的显示方式

### 架构概览

```
┌─────────────────┐
│   EditorState   │  ◄── 包含文档内容、选择、扩展状态
└────────┬────────┘
         │
         │ 更新
         ▼
┌─────────────────┐
│   Transaction   │  ◄── 描述状态变化
└────────┬────────┘
         │
         │ 应用
         ▼
┌─────────────────┐
│   EditorView    │  ◄── 负责渲染和用户交互
└─────────────────┘
```

## 核心模块

CodeMirror 6 被拆分为多个核心模块：

- `@codemirror/state`：状态管理
- `@codemirror/view`：DOM 渲染和事件处理
- `@codemirror/commands`：编辑命令
- `@codemirror/language`：语言支持基础设施
- `@codemirror/highlight`：语法高亮
- `@codemirror/fold`：代码折叠
- `@codemirror/gutter`：行号和装饰条
- `@codemirror/history`：撤销/重做历史
- `@codemirror/search`：搜索和替换
- `@codemirror/lint`：代码检查
- `@codemirror/autocomplete`：自动完成
- `@lezer/common`：语法树接口
- `@lezer/lr`：解析器基础设施

## 编辑器状态

### EditorState

`EditorState` 是编辑器的核心数据结构，包含文档内容、选择状态和所有扩展的状态。

#### 创建状态

```javascript
import { EditorState } from '@codemirror/state'

const state = EditorState.create({
  doc: '初始文档内容',
  extensions: [
    // 扩展数组
  ]
})
```

#### 状态属性

- `state.doc`：当前文档（Text 对象）
- `state.selection`：当前选择（EditorSelection 对象）
- `state.facet(facetName)`：获取指定 facet 的值
- `state.field(fieldName)`：获取指定状态字段的值

#### 状态更新

```javascript
// 创建事务
const transaction = state.update({
  changes: { from: 0, to: 5, insert: '新文本' },
  selection: { anchor: 5, head: 8 }
})

// 获取新状态
const newState = transaction.state
```

### Transaction

事务描述对编辑器状态的一组更改。

#### 事务属性

- `tr.changes`：文档变更
- `tr.selection`：选择变更
- `tr.effects`：状态效果
- `tr.annotations`：事务注解
- `tr.startState`：起始状态
- `tr.state`：结果状态

#### 事务规格 (TransactionSpec)

```javascript
{
  changes: { from: 10, to: 15, insert: '替换文本' },
  selection: { anchor: 10, head: 15 },
  effects: [someEffect.of(value)],
  annotations: [Transaction.userEvent.of('input')],
  scrollIntoView: true
}
```

## 编辑器视图

### EditorView

`EditorView` 负责将编辑器状态渲染到 DOM 并处理用户交互。

#### 创建视图

```javascript
import { EditorView } from '@codemirror/view'

const view = new EditorView({
  state,
  parent: document.querySelector('#editor')
})
```

#### 视图方法

- `view.dispatch(transaction)`：应用事务更新视图
- `view.setState(newState)`：用新状态替换当前状态
- `view.focus()`：聚焦编辑器
- `view.hasFocus`：检查编辑器是否有焦点
- `view.dom`：编辑器的 DOM 元素
- `view.contentDOM`：可编辑内容的 DOM 元素
- `view.destroy()`：销毁视图，清理资源

#### 视图更新

```javascript
// 单个事务
view.dispatch({
  changes: { from: 0, to: 5, insert: '新文本' }
})

// 多个事务规格
view.dispatch(
  state.update({ changes: { from: 0, insert: '文本1' } }),
  state.update({ changes: { from: 10, insert: '文本2' } })
)
```

## 文档操作

### Text

`Text` 是 CodeMirror 中表示文档的不可变数据结构。

#### 创建文本

```javascript
import { Text } from '@codemirror/state'

const text = Text.of(['第一行', '第二行', '第三行'])
```

#### 文本方法

- `text.length`：文本总长度
- `text.lines`：文本行数
- `text.line(n)`：获取第 n 行（1-based）
- `text.lineAt(pos)`：获取包含位置 pos 的行
- `text.sliceString(from, to)`：提取子字符串
- `text.slice(from, to)`：提取子文本对象
- `text.replace(from, to, replacement)`：创建替换部分文本后的新文本对象

### 更改操作

#### 创建变更

```javascript
import { ChangeSet } from '@codemirror/state'

// 单个变更
const changes = { from: 10, to: 15, insert: '新文本' }

// 多个变更
const changes = [
  { from: 10, to: 15, insert: '文本1' },
  { from: 20, to: 20, insert: '文本2' }
]

// 使用 ChangeSet
const changeSet = ChangeSet.of(changes, document.length)
```

#### 应用变更

```javascript
// 在事务中应用变更
view.dispatch({
  changes
})

// 获取应用变更后的文档
const newDoc = changes.apply(state.doc)
```

## 选择与光标

### EditorSelection

`EditorSelection` 表示编辑器中的一个或多个选择范围。

#### 创建选择

```javascript
import { EditorSelection } from '@codemirror/state'

// 单个光标
const selection = EditorSelection.cursor(10)

// 单个范围选择
const selection = EditorSelection.range(10, 15)

// 多选
const selection = EditorSelection.create([
  EditorSelection.range(10, 15),
  EditorSelection.range(20, 25)
])
```

#### 选择属性和方法

- `selection.ranges`：所有选择范围的数组
- `selection.main`：主选择范围
- `selection.anchor`：主选择的锚点位置
- `selection.head`：主选择的活动端位置
- `selection.from`：主选择的起始位置
- `selection.to`：主选择的结束位置
- `selection.empty`：是否为空选择（光标）

#### 选择操作

```javascript
// 在事务中设置选择
view.dispatch({
  selection: EditorSelection.create([
    EditorSelection.range(10, 15)
  ])
})

// 映射选择（通过文档变更）
const newSelection = selection.map(changeDesc)
```

## 命令系统

命令是可以应用于编辑器的操作，通常绑定到键盘快捷键。

### 基本命令

```javascript
import { deleteCharBackward, insertNewline } from '@codemirror/commands'

// 执行命令
deleteCharBackward(view)

// 在事务中调用命令
view.dispatch(
  view.state.update({
    changes: { from: 10, insert: '\n' },
    selection: { anchor: 11 }
  })
)
```

### 常用命令

- `insertNewline`：插入换行
- `deleteCharBackward`：向后删除字符（退格键）
- `deleteCharForward`：向前删除字符（删除键）
- `cursorLineUp`：光标上移一行
- `cursorLineDown`：光标下移一行
- `cursorCharLeft`：光标左移一字符
- `cursorCharRight`：光标右移一字符
- `undo`：撤销
- `redo`：重做

### 自定义命令

```javascript
const myCommand = (view) => {
  // 检查命令是否适用
  if (!view.state.selection.main.empty) return false
  
  // 执行命令操作
  view.dispatch({
    changes: { from: view.state.selection.main.from, insert: '自定义文本' }
  })
  
  // 返回 true 表示命令已处理
  return true
}
```

## 事件处理

### DOM 事件处理

```javascript
import { EditorView } from '@codemirror/view'

// 注册 DOM 事件处理器
const domEventHandlers = EditorView.domEventHandlers({
  click: (event, view) => {
    console.log('点击事件', event)
    // 返回 true 阻止默认处理
    return false
  },
  keydown: (event, view) => {
    if (event.key === 'Tab') {
      // 自定义 Tab 键处理
      return true
    }
    return false
  }
})
```

### 编辑器事件

```javascript
// 更新监听器
const updateListener = EditorView.updateListener.of(update => {
  if (update.docChanged) {
    console.log('文档已更改')
  }
  if (update.selectionSet) {
    console.log('选择已更改')
  }
})
```

## 扩展系统

扩展是 CodeMirror 6 的核心机制，用于添加或修改编辑器功能。

### 创建扩展

```javascript
import { Extension } from '@codemirror/state'

// 组合多个扩展
const myExtension = [
  basicSetup,
  markdown(),
  EditorView.lineWrapping,
  myCustomExtension
]
```

### Facet

Facet 是一种从多个来源收集值的机制。

```javascript
import { Facet } from '@codemirror/state'

// 定义 Facet
const myFacet = Facet.define({
  combine: values => values.join(', ')
})

// 提供 Facet 值
const myExtension = myFacet.of('值1')

// 读取 Facet 值
const value = state.facet(myFacet)
```

### StateField

StateField 用于在编辑器状态中存储自定义数据。

```javascript
import { StateField } from '@codemirror/state'

// 定义状态字段
const myField = StateField.define({
  create: state => '初始值',
  update: (value, transaction) => {
    // 根据事务更新值
    if (transaction.docChanged) {
      return '新值'
    }
    return value
  },
  provide: field => {
    // 提供基于此字段的扩展
    return EditorView.decorations.from(field)
  }
})

// 访问字段值
const value = state.field(myField)
```

### StateEffect

StateEffect 用于在事务中传递不直接影响文档或选择的效果。

```javascript
import { StateEffect } from '@codemirror/state'

// 定义状态效果
const myEffect = StateEffect.define()

// 创建效果实例
const effect = myEffect.of(value)

// 在事务中应用效果
view.dispatch({
  effects: effect
})

// 在状态字段中处理效果
update: (value, tr) => {
  for (let effect of tr.effects) {
    if (effect.is(myEffect)) {
      // 处理效果
      return effect.value
    }
  }
  return value
}
```

## 装饰系统

装饰用于改变文本的显示方式，而不修改实际文档内容。

### Decoration 类型

- `Decoration.mark`：为文本范围添加样式
- `Decoration.widget`：在指定位置插入 DOM 部件
- `Decoration.replace`：用自定义 DOM 替换文本范围
- `Decoration.line`：为整行添加样式

### 创建装饰

```javascript
import { Decoration, EditorView } from '@codemirror/view'

// 标记装饰
const markDecoration = Decoration.mark({
  attributes: { class: 'highlighted' }
})

// 部件装饰
const widgetDecoration = Decoration.widget({
  widget: new MyWidget(),
  side: 1 // 1 表示在位置右侧
})

// 替换装饰
const replaceDecoration = Decoration.replace({
  widget: new MyReplaceWidget()
})

// 行装饰
const lineDecoration = Decoration.line({
  attributes: { class: 'highlighted-line' }
})
```

### 应用装饰

```javascript
import { RangeSet } from '@codemirror/state'

// 创建装饰集
const decorations = RangeSet.of([
  markDecoration.range(10, 20),
  widgetDecoration.range(25),
  lineDecoration.range(30, 30)
])

// 通过状态字段提供装饰
const decorationField = StateField.define({
  create: state => RangeSet.empty,
  update: (decorations, tr) => {
    // 更新装饰
    return decorations.map(tr.changes)
  },
  provide: field => EditorView.decorations.from(field)
})
```

### 自定义部件

```javascript
import { WidgetType } from '@codemirror/view'

class MyWidget extends WidgetType {
  constructor(value) {
    super()
    this.value = value
  }
  
  eq(other) {
    return this.value === other.value
  }
  
  toDOM() {
    const element = document.createElement('span')
    element.textContent = this.value
    element.className = 'my-widget'
    return element
  }
  
  ignoreEvent(event) {
    // 返回 true 表示忽略事件
    return false
  }
}
```

## 语言支持

### 语言包

CodeMirror 6 提供了多种语言支持包：

- `@codemirror/lang-javascript`：JavaScript/TypeScript
- `@codemirror/lang-html`：HTML
- `@codemirror/lang-css`：CSS
- `@codemirror/lang-markdown`：Markdown
- `@codemirror/lang-json`：JSON
- `@codemirror/lang-python`：Python
- 等等

```javascript
import { javascript } from '@codemirror/lang-javascript'
import { markdown } from '@codemirror/lang-markdown'

// 使用语言支持
const extensions = [
  javascript(),
  markdown()
]
```

### 语法高亮

```javascript
import { syntaxHighlighting, defaultHighlightStyle } from '@codemirror/language'

// 使用默认高亮样式
const extensions = [
  syntaxHighlighting(defaultHighlightStyle)
]

// 自定义高亮样式
import { HighlightStyle, tags } from '@codemirror/highlight'

const myHighlightStyle = HighlightStyle.define([
  { tag: tags.keyword, color: '#5c6166' },
  { tag: tags.comment, color: '#93a1a1', fontStyle: 'italic' }
])

const extensions = [
  syntaxHighlighting(myHighlightStyle)
]
```

### 语法树

```javascript
import { syntaxTree } from '@codemirror/language'

// 获取语法树
const tree = syntaxTree(state)

// 查询节点
tree.cursor().iterate(node => {
  if (node.name === 'Keyword') {
    console.log('找到关键字', state.doc.sliceString(node.from, node.to))
  }
})
```

## 主题与样式

### 主题

```javascript
import { EditorView } from '@codemirror/view'

// 创建主题
const myTheme = EditorView.theme({
  '&': {
    fontSize: '14px',
    backgroundColor: '#f8f9fa'
  },
  '.cm-content': {
    fontFamily: 'Consolas, monospace',
    caretColor: '#0074d9'
  },
  '.cm-line': {
    padding: '0 4px'
  }
})

// 暗色主题
const darkTheme = EditorView.theme({
  '&': {
    backgroundColor: '#282c34',
    color: '#abb2bf'
  }
}, { dark: true })
```

### 基础主题

```javascript
import { EditorView } from '@codemirror/view'

// 基础主题（应用于所有主题）
const baseTheme = EditorView.baseTheme({
  '&.cm-editor': {
    // 所有编辑器通用样式
  },
  '&light .cm-content': {
    // 只应用于亮色主题
  },
  '&dark .cm-content': {
    // 只应用于暗色主题
  }
})
```

## 实用工具

### 文本操作

```javascript
// 计算列位置
import { countColumn, findColumn } from '@codemirror/text'

const column = countColumn('  hello', 4, 2) // tab 大小为 2

// 查找列位置
const pos = findColumn('  hello', 2, 2) // 在列 2 的位置
```

### 字符处理

```javascript
// 代码点操作
import { codePointAt, fromCodePoint, codePointSize } from '@codemirror/text'

const code = codePointAt('😀', 0) // 获取代码点
const char = fromCodePoint(128512) // 从代码点获取字符
const size = codePointSize(code) // 获取代码点大小（1 或 2）
```

## Vue 3 集成

### 基本集成

```vue
<template>
  <div ref="editorRef"></div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import { EditorState } from '@codemirror/state'
import { EditorView } from '@codemirror/view'
import { basicSetup } from 'codemirror'
import { javascript } from '@codemirror/lang-javascript'

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue', 'change', 'ready'])

const editorRef = ref(null)
const editorView = ref(null)

onMounted(() => {
  const startState = EditorState.create({
    doc: props.modelValue,
    extensions: [
      basicSetup,
      javascript(),
      EditorView.updateListener.of(update => {
        if (update.docChanged) {
          const doc = update.state.doc.toString()
          emit('update:modelValue', doc)
          emit('change', doc)
        }
      })
    ]
  })

  editorView.value = new EditorView({
    state: startState,
    parent: editorRef.value
  })

  emit('ready', { view: editorView.value })
})

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (!editorView.value) return
    
    const currentValue = editorView.value.state.doc.toString()
    if (newValue !== currentValue) {
      editorView.value.dispatch({
        changes: {
          from: 0,
          to: currentValue.length,
          insert: newValue
        }
      })
    }
  }
)

// 组件销毁时清理
onBeforeUnmount(() => {
  if (editorView.value) {
    editorView.value.destroy()
    editorView.value = null
  }
})

// 暴露方法
defineExpose({
  getEditor: () => editorView.value
})
</script>
```

### 自定义扩展

```javascript
// 创建自定义扩展
const myExtension = [
  // 自动高度
  EditorView.theme({
    '&': {
      height: 'auto'
    },
    '.cm-scroller': {
      overflow: 'hidden'
    }
  }),
  
  // 更新监听器
  EditorView.updateListener.of(update => {
    if (update.heightChanged) {
      // 处理高度变化
    }
  }),
  
  // DOM 事件处理
  EditorView.domEventHandlers({
    focus: (event, view) => {
      // 处理焦点事件
    }
  })
]
```

## 常见问题与解决方案

### 1. 如何处理大文件性能问题？

```javascript
import { EditorState, EditorView } from '@codemirror/basic-setup'
import { ViewPlugin } from '@codemirror/view'

// 懒加载插件
const lazyLoadPlugin = ViewPlugin.fromClass(class {
  constructor(view) {
    this.view = view
    this.loadedRanges = []
  }
  
  update(update) {
    // 根据可见范围动态加载内容
    const viewport = update.view.viewport
    this.ensureLoaded(viewport.from, viewport.to)
  }
  
  ensureLoaded(from, to) {
    // 实现加载逻辑
  }
})
```

### 2. 如何实现自定义自动完成？

```javascript
import { autocompletion, CompletionContext, CompletionResult } from '@codemirror/autocomplete'

// 自定义自动完成源
const myCompletions = autocompletion({
  override: [
    (context: CompletionContext) => {
      const word = context.matchBefore(/\w*/)
      if (!word) return null
      
      return {
        from: word.from,
        options: [
          { label: 'option1', type: 'keyword' },
          { label: 'option2', type: 'variable' }
        ]
      }
    }
  ]
})
```

### 3. 如何处理协同编辑？

```javascript
import { collab } from '@codemirror/collab'

// 设置协同编辑
const collaborationExtension = collab({
  clientID: 'user-123',
  version: 1
})

// 接收远程更改
function receiveUpdates(updates) {
  view.dispatch(collab.receiveUpdates(view.state, updates))
}

// 发送本地更改
function sendUpdates() {
  const sendable = collab.sendableUpdates(view.state)
  if (sendable.length) {
    // 发送到服务器
  }
}
```

### 4. 如何处理编辑器大小变化？

```javascript
// 监听大小变化
const resizeObserver = new ResizeObserver(() => {
  if (editorView.value) {
    editorView.value.requestMeasure()
  }
})

onMounted(() => {
  if (editorRef.value) {
    resizeObserver.observe(editorRef.value)
  }
})

onBeforeUnmount(() => {
  resizeObserver.disconnect()
})
```

### 5. 如何优化首次加载性能？

```javascript
// 按需加载扩展
const loadExtensions = async () => {
  const [basicSetup, javascript] = await Promise.all([
    import('codemirror').then(m => m.basicSetup),
    import('@codemirror/lang-javascript').then(m => m.javascript)
  ])
  
  return [basicSetup, javascript()]
}

// 异步初始化编辑器
const initEditor = async () => {
  const extensions = await loadExtensions()
  
  editorView.value = new EditorView({
    state: EditorState.create({
      doc: props.modelValue,
      extensions
    }),
    parent: editorRef.value
  })
}
```

## 参考资源

- [CodeMirror 6 官方文档](https://codemirror.net/docs/)
- [CodeMirror 6 示例](https://codemirror.net/examples/)
- [CodeMirror 6 API 参考](https://codemirror.net/docs/ref/)
- [CodeMirror 6 系统指南](https://codemirror.net/docs/guide/)
- [CodeMirror 6 讨论论坛](https://discuss.codemirror.net/) 