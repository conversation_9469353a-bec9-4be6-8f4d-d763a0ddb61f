<template>
  <div>
    <FeaturePanel class="mt-2">
      <template #title>
        <div>
          工具
          <a-tooltip content="使用工具可以扩展代理的能力，比如搜索互联网或科学计算">
            <icon-question-circle />
          </a-tooltip>
        </div>
      </template>
      <template #headerRight>
        <div>
          <div class="flex items-center">
            <span class="text-sm font-normal leading-[18px] text-text-tertiary">
              {{ tools.filter((item) => !!item.enabled).length }} / {{ tools.length }}&nbsp; 启用
            </span>
            <template v-if="tools.length < 10">
              <a-divider direction="vertical" />
              <a-button size="small" @click="handleAddTool">添加</a-button>
            </template>
          </div>
        </div>
      </template>
      <template #default>
        <div class="grid grid-cols-1 flex-wrap items-center justify-between gap-1 2xl:grid-cols-2">
          <div
            v-for="(item, index) in tools"
            :key="item.id"
            class="tool-item cursor group relative flex w-full items-center justify-between rounded-lg border-[0.5px] border-components-panel-border-subtle p-1.5 pr-2 shadow-xs hover:shadow-sm"
          >
            <div class="flex w-0 grow items-center">
              <!--icon：TODO:支持emoji的展示-->
              <img
                v-if="typeof item.icon === 'string'"
                :src="item.icon"
                class="w-[16px] h-[16px]"
                alt="tool-icon"
              />
              <div v-else :style="{ backgroundColor: item.icon ? item.icon.background : '#ffffff' }">
                {{ item.icon?.content }}
              </div>
              <!--name和label-->
              <div class="system-xs-regular ml-1.5 flex w-0 grow items-center truncate">
                <div class="system-xs-medium ml-1.5 pr-1.5 text-text-secondary">
                  {{
                    item.provider_type === CollectionType.builtIn ? item.provider_name.split('/').pop() : item.tool_label
                  }}
                </div>
                <div class="text-text-tertiary ml-1.5">{{ item.tool_label }}</div>
              </div>
              <!--helper 提示-->
              <a-tooltip :content="'工具调用名称，用于 Agent 推理和提示词'">
                <template #content>
                  <div class="mb-1.5 text-text-secondary">{{ item.tool_name }}</div>
                  <div class="mb-1.5 text-text-tertiary">工具调用名称，用于 Agent 推理和提示词</div>
                  <!--<div>
                    <a-typography-paragraph copyable :copy-text="item.tool_name">
                      复制名称
                    </a-typography-paragraph>
                  </div>-->
                </template>
                <!--group-hover:inline-block hidden-->
                <icon-info-circle class="ml-1.5" />
              </a-tooltip>
            </div>

            <!--右侧：设置、删除、启用-->
            <div class="ml-1 flex shrink-0 items-center">
              <!--TODO：设置tool，要同步信息到发布接口。-->
              <div class="cursor-pointer rounded-md p-1 hover:bg-black/5" @click="setTool(item)">
                <a-tooltip :content="'信息和设置'">
                  <icon-settings />
                </a-tooltip>
              </div>
              <div class="cursor-pointer rounded-md p-1 hover:bg-black/5" @click="deleteTool(index)">
                <a-tooltip :content="'删除'">
                  <icon-delete />
                </a-tooltip>
              </div>
              <div class="ml-1.5">
                <a-switch v-model="item.enabled" size="small" type="round" @change="handleChangeState(item)" />
              </div>
            </div>
          </div>
        </div>
      </template>
    </FeaturePanel>

    <!--工具设置 drawer-->
    <SettingBuilt
      v-if="setToolVisible"
      :toolCollection="toolCollection"
      :toolName="toolName"
      :setting="toolParameters"
      @closeDrawer="closeDrawer"
    />

    <ToolPicker
      v-if="toolPickerVisible"
      :selectedTools="selectedTools"
      @closeToolPickerModal="closeToolPickerModal"
      @addTool="addTool"
    />
  </div>
</template>
<script setup lang="ts">
import FeaturePanel from '@/views/app/configuration/base/feature-panel/index.vue'
import { CollectionType } from '@/views/app/workflow/types/variable'
import { canFindTool } from '@/views/app/workflow/utils/configuration'
import SettingBuilt from '@/views/app/configuration/agent-tools/SettingBuiltInTool.vue'
import ToolPicker from '@/views/app/configuration/agent-tools/tool-picker/index.vue'

// collectionList：系统所有的toolList。selectedTools：在agent中用户选中的toolList（包括启用和禁用的）
const props = defineProps(['collectionList', 'selectedTools'])
const emits = defineEmits(['changeTool'])

const toolPickerVisible = ref(false)
const handleAddTool = () => {
  // TODO：打开选择tool的弹框，选择公用组件
  toolPickerVisible.value = true
}

/**
 * tools大概结构：
 * enabled:true
 * isDeleted:false
 * notAuthor:false
 * provider_id:"code"
 * provider_name:"code"
 * provider_type:"builtin"
 * tool_label:"代码解释器"
 * tool_name:"simple_code"
 * tool_parameters:{language: '', code: ''}
 */
const tools = ref([])

onMounted(() => {

})

watch([() => props.selectedTools, () => props.collectionList], () => {
  // tools: 选中的toolList。把系统的toolList字段放在 collection 里。
  tools.value = props.selectedTools.map((item: any) => {
    const collection = props.collectionList.find(
      (collection: any) =>
        canFindTool(collection.id, item.provider_id)
        && collection.type === item.provider_type
    )
    const icon = collection?.icon
    return {
      ...item,
      icon,
      collection
    }
  })
  if (tools.value.length >= 10) {
    toolPickerVisible.value = false
  }
})

const setToolVisible = ref(false)
// tool的collection字段
const toolCollection = ref({})
// 设置的form的values
const toolParameters = ref({})
// 当前setting的tool
const toolItem = ref<any>({})
const toolName = ref('')

// 打开弹框
const setTool = (item: any) => {
  setToolVisible.value = true
  toolItem.value = item
  toolCollection.value = item.collection || {}
  toolName.value = item.tool_name
  toolParameters.value = item.tool_parameters
}
// 更新状态
const handleChangeState = (item: any) => {
  // item.enabled = !item.enabled
  emits('changeTool', tools.value)
}
// 删除选中tool
const deleteTool = (index) => {
  tools.value.splice(index, 1)
  emits('changeTool', tools.value)
}
// 添加tool。暂时没有调用，需要调用选择tool的组件。
const addTool = (tool) => {
  tools.value.push(tool)
  emits('changeTool', tools.value)
}
// 关闭设置drawer
const closeDrawer = (type, parameters) => {
  if (type == 'ok') {
    toolItem.value.tool_parameters = parameters
    emits('changeTool', tools.value)
  }
  setToolVisible.value = false
}

const closeToolPickerModal = (type) => {
  console.log(type)
  toolPickerVisible.value = false
}
</script>

<style scoped lang="scss">
.tool-item {
  background: #ffffff;
}
</style>
