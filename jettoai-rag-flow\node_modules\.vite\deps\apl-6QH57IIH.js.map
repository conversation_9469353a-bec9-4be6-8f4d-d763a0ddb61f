{"version": 3, "sources": ["../../.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/apl.js"], "sourcesContent": ["var builtInFuncs = {\n  \"+\": [\"conjugate\", \"add\"],\n  \"−\": [\"negate\", \"subtract\"],\n  \"×\": [\"signOf\", \"multiply\"],\n  \"÷\": [\"reciprocal\", \"divide\"],\n  \"⌈\": [\"ceiling\", \"greaterOf\"],\n  \"⌊\": [\"floor\", \"lesserOf\"],\n  \"∣\": [\"absolute\", \"residue\"],\n  \"⍳\": [\"indexGenerate\", \"indexOf\"],\n  \"?\": [\"roll\", \"deal\"],\n  \"⋆\": [\"exponentiate\", \"toThePowerOf\"],\n  \"⍟\": [\"naturalLog\", \"logToTheBase\"],\n  \"○\": [\"piTimes\", \"circularFuncs\"],\n  \"!\": [\"factorial\", \"binomial\"],\n  \"⌹\": [\"matrixInverse\", \"matrixDivide\"],\n  \"<\": [null, \"lessThan\"],\n  \"≤\": [null, \"lessThanOrEqual\"],\n  \"=\": [null, \"equals\"],\n  \">\": [null, \"greaterThan\"],\n  \"≥\": [null, \"greaterThanOrEqual\"],\n  \"≠\": [null, \"notEqual\"],\n  \"≡\": [\"depth\", \"match\"],\n  \"≢\": [null, \"notMatch\"],\n  \"∈\": [\"enlist\", \"membership\"],\n  \"⍷\": [null, \"find\"],\n  \"∪\": [\"unique\", \"union\"],\n  \"∩\": [null, \"intersection\"],\n  \"∼\": [\"not\", \"without\"],\n  \"∨\": [null, \"or\"],\n  \"∧\": [null, \"and\"],\n  \"⍱\": [null, \"nor\"],\n  \"⍲\": [null, \"nand\"],\n  \"⍴\": [\"shapeOf\", \"reshape\"],\n  \",\": [\"ravel\", \"catenate\"],\n  \"⍪\": [null, \"firstAxisCatenate\"],\n  \"⌽\": [\"reverse\", \"rotate\"],\n  \"⊖\": [\"axis1Reverse\", \"axis1Rotate\"],\n  \"⍉\": [\"transpose\", null],\n  \"↑\": [\"first\", \"take\"],\n  \"↓\": [null, \"drop\"],\n  \"⊂\": [\"enclose\", \"partitionWithAxis\"],\n  \"⊃\": [\"diclose\", \"pick\"],\n  \"⌷\": [null, \"index\"],\n  \"⍋\": [\"gradeUp\", null],\n  \"⍒\": [\"gradeDown\", null],\n  \"⊤\": [\"encode\", null],\n  \"⊥\": [\"decode\", null],\n  \"⍕\": [\"format\", \"formatByExample\"],\n  \"⍎\": [\"execute\", null],\n  \"⊣\": [\"stop\", \"left\"],\n  \"⊢\": [\"pass\", \"right\"]\n};\n\nvar isOperator = /[\\.\\/⌿⍀¨⍣]/;\nvar isNiladic = /⍬/;\nvar isFunction = /[\\+−×÷⌈⌊∣⍳\\?⋆⍟○!⌹<≤=>≥≠≡≢∈⍷∪∩∼∨∧⍱⍲⍴,⍪⌽⊖⍉↑↓⊂⊃⌷⍋⍒⊤⊥⍕⍎⊣⊢]/;\nvar isArrow = /←/;\nvar isComment = /[⍝#].*$/;\n\nvar stringEater = function(type) {\n  var prev;\n  prev = false;\n  return function(c) {\n    prev = c;\n    if (c === type) {\n      return prev === \"\\\\\";\n    }\n    return true;\n  };\n};\n\nexport const apl = {\n  name: \"apl\",\n  startState: function() {\n    return {\n      prev: false,\n      func: false,\n      op: false,\n      string: false,\n      escape: false\n    };\n  },\n  token: function(stream, state) {\n    var ch;\n    if (stream.eatSpace()) {\n      return null;\n    }\n    ch = stream.next();\n    if (ch === '\"' || ch === \"'\") {\n      stream.eatWhile(stringEater(ch));\n      stream.next();\n      state.prev = true;\n      return \"string\";\n    }\n    if (/[\\[{\\(]/.test(ch)) {\n      state.prev = false;\n      return null;\n    }\n    if (/[\\]}\\)]/.test(ch)) {\n      state.prev = true;\n      return null;\n    }\n    if (isNiladic.test(ch)) {\n      state.prev = false;\n      return \"atom\";\n    }\n    if (/[¯\\d]/.test(ch)) {\n      if (state.func) {\n        state.func = false;\n        state.prev = false;\n      } else {\n        state.prev = true;\n      }\n      stream.eatWhile(/[\\w\\.]/);\n      return \"number\";\n    }\n    if (isOperator.test(ch)) {\n      return \"operator\"\n    }\n    if (isArrow.test(ch)) {\n      return \"operator\";\n    }\n    if (isFunction.test(ch)) {\n      state.func = true;\n      state.prev = false;\n      return builtInFuncs[ch] ? \"variableName.function.standard\" : \"variableName.function\"\n    }\n    if (isComment.test(ch)) {\n      stream.skipToEnd();\n      return \"comment\";\n    }\n    if (ch === \"∘\" && stream.peek() === \".\") {\n      stream.next();\n      return \"variableName.function\";\n    }\n    stream.eatWhile(/[\\w\\$_]/);\n    state.prev = true;\n    return \"keyword\";\n  }\n}\n"], "mappings": ";;;AAAA,IAAI,eAAe;AAAA,EACjB,KAAK,CAAC,aAAa,KAAK;AAAA,EACxB,KAAK,CAAC,UAAU,UAAU;AAAA,EAC1B,KAAK,CAAC,UAAU,UAAU;AAAA,EAC1B,KAAK,CAAC,cAAc,QAAQ;AAAA,EAC5B,KAAK,CAAC,WAAW,WAAW;AAAA,EAC5B,KAAK,CAAC,SAAS,UAAU;AAAA,EACzB,KAAK,CAAC,YAAY,SAAS;AAAA,EAC3B,KAAK,CAAC,iBAAiB,SAAS;AAAA,EAChC,KAAK,CAAC,QAAQ,MAAM;AAAA,EACpB,KAAK,CAAC,gBAAgB,cAAc;AAAA,EACpC,KAAK,CAAC,cAAc,cAAc;AAAA,EAClC,KAAK,CAAC,WAAW,eAAe;AAAA,EAChC,KAAK,CAAC,aAAa,UAAU;AAAA,EAC7B,KAAK,CAAC,iBAAiB,cAAc;AAAA,EACrC,KAAK,CAAC,MAAM,UAAU;AAAA,EACtB,KAAK,CAAC,MAAM,iBAAiB;AAAA,EAC7B,KAAK,CAAC,MAAM,QAAQ;AAAA,EACpB,KAAK,CAAC,MAAM,aAAa;AAAA,EACzB,KAAK,CAAC,MAAM,oBAAoB;AAAA,EAChC,KAAK,CAAC,MAAM,UAAU;AAAA,EACtB,KAAK,CAAC,SAAS,OAAO;AAAA,EACtB,KAAK,CAAC,MAAM,UAAU;AAAA,EACtB,KAAK,CAAC,UAAU,YAAY;AAAA,EAC5B,KAAK,CAAC,MAAM,MAAM;AAAA,EAClB,KAAK,CAAC,UAAU,OAAO;AAAA,EACvB,KAAK,CAAC,MAAM,cAAc;AAAA,EAC1B,KAAK,CAAC,OAAO,SAAS;AAAA,EACtB,KAAK,CAAC,MAAM,IAAI;AAAA,EAChB,KAAK,CAAC,MAAM,KAAK;AAAA,EACjB,KAAK,CAAC,MAAM,KAAK;AAAA,EACjB,KAAK,CAAC,MAAM,MAAM;AAAA,EAClB,KAAK,CAAC,WAAW,SAAS;AAAA,EAC1B,KAAK,CAAC,SAAS,UAAU;AAAA,EACzB,KAAK,CAAC,MAAM,mBAAmB;AAAA,EAC/B,KAAK,CAAC,WAAW,QAAQ;AAAA,EACzB,KAAK,CAAC,gBAAgB,aAAa;AAAA,EACnC,KAAK,CAAC,aAAa,IAAI;AAAA,EACvB,KAAK,CAAC,SAAS,MAAM;AAAA,EACrB,KAAK,CAAC,MAAM,MAAM;AAAA,EAClB,KAAK,CAAC,WAAW,mBAAmB;AAAA,EACpC,KAAK,CAAC,WAAW,MAAM;AAAA,EACvB,KAAK,CAAC,MAAM,OAAO;AAAA,EACnB,KAAK,CAAC,WAAW,IAAI;AAAA,EACrB,KAAK,CAAC,aAAa,IAAI;AAAA,EACvB,KAAK,CAAC,UAAU,IAAI;AAAA,EACpB,KAAK,CAAC,UAAU,IAAI;AAAA,EACpB,KAAK,CAAC,UAAU,iBAAiB;AAAA,EACjC,KAAK,CAAC,WAAW,IAAI;AAAA,EACrB,KAAK,CAAC,QAAQ,MAAM;AAAA,EACpB,KAAK,CAAC,QAAQ,OAAO;AACvB;AAEA,IAAI,aAAa;AACjB,IAAI,YAAY;AAChB,IAAI,aAAa;AACjB,IAAI,UAAU;AACd,IAAI,YAAY;AAEhB,IAAI,cAAc,SAAS,MAAM;AAC/B,MAAI;AACJ,SAAO;AACP,SAAO,SAAS,GAAG;AACjB,WAAO;AACP,QAAI,MAAM,MAAM;AACd,aAAO,SAAS;AAAA,IAClB;AACA,WAAO;AAAA,EACT;AACF;AAEO,IAAM,MAAM;AAAA,EACjB,MAAM;AAAA,EACN,YAAY,WAAW;AACrB,WAAO;AAAA,MACL,MAAM;AAAA,MACN,MAAM;AAAA,MACN,IAAI;AAAA,MACJ,QAAQ;AAAA,MACR,QAAQ;AAAA,IACV;AAAA,EACF;AAAA,EACA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI;AACJ,QAAI,OAAO,SAAS,GAAG;AACrB,aAAO;AAAA,IACT;AACA,SAAK,OAAO,KAAK;AACjB,QAAI,OAAO,OAAO,OAAO,KAAK;AAC5B,aAAO,SAAS,YAAY,EAAE,CAAC;AAC/B,aAAO,KAAK;AACZ,YAAM,OAAO;AACb,aAAO;AAAA,IACT;AACA,QAAI,UAAU,KAAK,EAAE,GAAG;AACtB,YAAM,OAAO;AACb,aAAO;AAAA,IACT;AACA,QAAI,UAAU,KAAK,EAAE,GAAG;AACtB,YAAM,OAAO;AACb,aAAO;AAAA,IACT;AACA,QAAI,UAAU,KAAK,EAAE,GAAG;AACtB,YAAM,OAAO;AACb,aAAO;AAAA,IACT;AACA,QAAI,QAAQ,KAAK,EAAE,GAAG;AACpB,UAAI,MAAM,MAAM;AACd,cAAM,OAAO;AACb,cAAM,OAAO;AAAA,MACf,OAAO;AACL,cAAM,OAAO;AAAA,MACf;AACA,aAAO,SAAS,QAAQ;AACxB,aAAO;AAAA,IACT;AACA,QAAI,WAAW,KAAK,EAAE,GAAG;AACvB,aAAO;AAAA,IACT;AACA,QAAI,QAAQ,KAAK,EAAE,GAAG;AACpB,aAAO;AAAA,IACT;AACA,QAAI,WAAW,KAAK,EAAE,GAAG;AACvB,YAAM,OAAO;AACb,YAAM,OAAO;AACb,aAAO,aAAa,EAAE,IAAI,mCAAmC;AAAA,IAC/D;AACA,QAAI,UAAU,KAAK,EAAE,GAAG;AACtB,aAAO,UAAU;AACjB,aAAO;AAAA,IACT;AACA,QAAI,OAAO,OAAO,OAAO,KAAK,MAAM,KAAK;AACvC,aAAO,KAAK;AACZ,aAAO;AAAA,IACT;AACA,WAAO,SAAS,SAAS;AACzB,UAAM,OAAO;AACb,WAAO;AAAA,EACT;AACF;", "names": []}