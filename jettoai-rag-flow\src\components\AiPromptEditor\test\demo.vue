<template>
  <div class="ai-prompt-editor-demo">
    <h2>AI 提示词编辑器演示</h2>

    <div class="editor-container">
      <div class="variable-space-toggle">
        <label>
          <input v-model="enableVariableSpace" type="checkbox" />
          启用变量空间功能
        </label>
        <div v-if="enableVariableSpace" class="toggle-hint">
          <p>启用后的功能：</p>
          <ol>
            <li>输入单个左花括号会自动补全并将光标置于中间</li>
            <li>输入两个连续的左花括号会自动补全成变量格式</li>
            <li>变量将以特殊样式显示，并被系统自动识别</li>
          </ol>
        </div>
      </div>

      <AiPromptEditor
        ref="editorRef"
        v-model="promptText"
        placeholder="在此输入提示词... (试试输入 @ 或 / 触发建议，输入 { 测试自动补全)"
        :trigger-chars="['@', '/']"
        height="200px"
        focus-border-color="#4f46e5"
        :enable-variable-space="enableVariableSpace"
        @keyword-trigger="handleKeywordTrigger"
        @trigger-close="handleTriggerClose"
        @change="handleChange"
        @focus="handleFocus"
        @blur="handleBlur"
        @metadata-update="handleMetadataUpdate"
        @variable-update="handleVariableUpdate"
      >
        <template #keyword-trigger="{ trigger, position }">
          <div
            v-if="showSuggestions && trigger"
            class="suggestions-panel"
            :style="{
              left: `${position.x}px`,
              top: `${position.y + 5}px`
            }"
          >
            <div class="suggestions-header">
              {{ trigger === '@' ? '提及某人' : '命令' }}
            </div>
            <div class="suggestions-list">
              <div
                v-for="item in filteredSuggestions"
                :key="item.id"
                class="suggestion-item"
                @click="insertSuggestion(item)"
              >
                <div class="suggestion-icon" :class="trigger === '@' ? 'user-icon' : 'command-icon'">
                  {{ trigger === '@' ? '@' : '/' }}
                </div>
                <div class="suggestion-content">
                  <div class="suggestion-title">{{ item.title }}</div>
                  <div class="suggestion-desc">{{ item.description }}</div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </AiPromptEditor>

      <div v-if="editorFocused" class="focus-indicator">编辑器已获取焦点</div>
      <div class="editor-tips">
        <strong>提示：</strong>
        插入模板小部件后，可以使用退格键（Backspace）或删除键（Delete）一键删除它们。
      </div>
    </div>

    <div class="preview-container">
      <h3>预览</h3>
      <div class="preview-content" v-html="renderedMarkdown" />
    </div>

    <div class="preview-container">
      <h3>带HTML标签的内容</h3>
      <div class="preview-content" v-html="htmlContent" />
    </div>

    <div v-if="hasMetadata" class="metadata-container">
      <h3>元数据</h3>
      <pre>{{ JSON.stringify(currentMetadata, null, 2) }}</pre>
    </div>

    <div v-if="enableVariableSpace && variables.length > 0" class="variables-container">
      <h3>检测到的变量 ({{ variables.length }})</h3>
      <div class="variables-list">
        <div
          v-for="(variable, index) in variables"
          :key="index"
          class="variable-item"
          title="点击复制变量用法"
          @click="copyToClipboard('{{' + variable + '}}')"
        >
          {{ variable }}
        </div>
      </div>
      <div class="variables-info">
        <p>点击变量可以复制其完整格式。</p>
      </div>
    </div>

    <div v-if="enableVariableSpace" class="template-example">
      <h3>变量模板示例</h3>
      <button @click="insertVariableExample1">插入问候变量示例</button>
      <button @click="insertVariableExample2">插入复杂模板变量示例</button>
    </div>

    <div class="controls">
      <button @click="insertTemplate('heading')">插入标题</button>
      <button @click="insertTemplate('list')">插入列表</button>
      <button @click="insertTemplate('code')">插入代码块</button>
      <button @click="insertTemplateWidget()">插入模板小部件</button>
      <button @click="getHtmlContent()">获取HTML内容</button>
      <button @click="getVariables()">获取变量列表</button>
      <button @click="clearEditor">清除</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import AiPromptEditor from '../index.vue'

// 编辑器内容
const promptText = ref('')
const htmlContent = ref('')
const editorRef = ref<InstanceType<typeof AiPromptEditor> | null>(null)
const editorFocused = ref(false)
const currentMetadata = ref({})
const variables = ref<string[]>([])
const enableVariableSpace = ref(false)

// 建议状态
const showSuggestions = ref(false)
const currentTrigger = ref<string | null>(null)
const searchText = ref('')

// 示例建议数据
const mentionSuggestions = [
  { id: 1, title: '张三', description: '产品经理', value: '@张三 ' },
  { id: 2, title: '李四', description: 'UI设计师', value: '@李四 ' },
  { id: 3, title: '王五', description: '开发工程师', value: '@王五 ' },
  { id: 4, title: '赵六', description: '数据科学家', value: '@赵六 ' }
]

const commandSuggestions = [
  { id: 1, title: 'help', description: '显示可用命令', value: '/help ' },
  { id: 2, title: 'image', description: '插入图片', value: '/image ' },
  { id: 3, title: 'table', description: '创建表格', value: '/table ' },
  { id: 4, title: 'code', description: '插入代码块', value: '/code ' }
]

// 根据触发字符和搜索文本过滤建议
const filteredSuggestions = computed(() => {
  const suggestions = currentTrigger.value === '@' ? mentionSuggestions : commandSuggestions

  if (!searchText.value) {
    return suggestions
  }

  return suggestions.filter(
    (item) =>
      item.title.toLowerCase().includes(searchText.value.toLowerCase()) ||
      item.description.toLowerCase().includes(searchText.value.toLowerCase())
  )
})

// 检查是否存在元数据
const hasMetadata = computed(() => {
  return Object.keys(currentMetadata.value).length > 0
})

// 简单的Markdown渲染器（在实际应用中，使用合适的Markdown库）
const renderedMarkdown = computed(() => {
  let html = promptText.value
    // 标题
    .replace(/^# (.+)$/gm, '<h1>$1</h1>')
    .replace(/^## (.+)$/gm, '<h2>$1</h2>')
    .replace(/^### (.+)$/gm, '<h3>$1</h3>')
    // 粗体
    .replace(/\*\*(.+?)\*\*/g, '<strong>$1</strong>')
    // 斜体
    .replace(/\*(.+?)\*/g, '<em>$1</em>')
    // 代码块
    .replace(/```(.+?)```/gs, '<pre><code>$1</code></pre>')
    // 内联代码
    .replace(/`(.+?)`/g, '<code>$1</code>')
    // 列表
    .replace(/^- (.+)$/gm, '<li>$1</li>')
    // 变量表达式
    .replace(/\{\{([a-zA-Z0-9_]+)\}\}/g, '<span class="variable">{{$1}}</span>')
    // 换行
    .replace(/\n/g, '<br>')

  return html
})

// 处理关键字触发
const handleKeywordTrigger = (event: { trigger: string; position: any; text: string }) => {
  showSuggestions.value = true
  currentTrigger.value = event.trigger
  searchText.value = event.text
}

// 处理触发关闭
const handleTriggerClose = () => {
  showSuggestions.value = false
  currentTrigger.value = null
  searchText.value = ''
}

// 插入建议
const insertSuggestion = (item: { value: string }) => {
  if (editorRef.value) {
    editorRef.value.replaceTrigger(item.value)
    handleTriggerClose()
  }
}

// 处理编辑器内容变化
const handleChange = (value: string) => {
  promptText.value = value
}

// 处理焦点事件
const handleFocus = () => {
  editorFocused.value = true
}

const handleBlur = () => {
  editorFocused.value = false
}

// 处理元数据更新
const handleMetadataUpdate = (metadata: any) => {
  currentMetadata.value = metadata
}

// 处理变量更新
const handleVariableUpdate = (newVariables: string[]) => {
  variables.value = newVariables
}

// 插入模板文本
const insertTemplate = (type: string) => {
  if (!editorRef.value) return

  let template = ''

  switch (type) {
    case 'heading':
      template = '## 标题\n\n'
      break
    case 'list':
      template = '- 项目1\n- 项目2\n- 项目3\n\n'
      break
    case 'code':
      template = '```\n// 在此输入代码\n```\n\n'
      break
    default:
      break
  }

  editorRef.value.insertText(template)
}

// 插入模板小部件
const insertTemplateWidget = () => {
  if (!editorRef.value) return

  editorRef.value.insertTemplate({
    text: '用户提及',
    tag: 'user',
    attributes: {
      id: 'user-123',
      role: 'admin'
    }
  })
}

// 获取带HTML标签的内容
const getHtmlContent = () => {
  if (editorRef.value) {
    htmlContent.value = editorRef.value.getContentWithHtml()
  }
}

// 获取变量列表
const getVariables = () => {
  if (editorRef.value && enableVariableSpace.value) {
    const vars = editorRef.value.getVariables()
    console.log('检测到的变量:', vars)
  }
}

// 插入变量示例
const insertVariableExample1 = () => {
  if (editorRef.value) {
    editorRef.value.insertText('你好，{{name}}！欢迎来到{{company}}。')
  }
}

const insertVariableExample2 = () => {
  if (editorRef.value) {
    editorRef.value.insertText(`
以下是一个邮件模板示例：

尊敬的{{customer_name}}：

感谢您在{{purchase_date}}购买了我们的{{product_name}}产品。您的订单号是 {{order_id}}。

如有任何问题，请随时联系我们的客服团队：{{support_email}}。

此致
{{company_name}}团队
    `)
  }
}

// 清除编辑器
const clearEditor = () => {
  promptText.value = ''
  htmlContent.value = ''
  variables.value = []
  if (editorRef.value) {
    editorRef.value.clearMetadata()
  }
}

// 复制到剪贴板
const copyToClipboard = (text: string) => {
  navigator.clipboard
    .writeText(text)
    .then(() => {
      alert(`已复制: ${text}`)
    })
    .catch((err) => {
      console.error('复制失败:', err)
      // 备用复制方法
      const textarea = document.createElement('textarea')
      textarea.value = text
      document.body.appendChild(textarea)
      textarea.select()
      document.execCommand('copy')
      document.body.removeChild(textarea)
      alert(`已复制: ${text}`)
    })
}
</script>

<style scoped lang="scss">
.ai-prompt-editor-demo {
  width: 600px;
  height: 100%;
  overflow-y: auto;
  margin: 0 auto;
  padding: 20px;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue',
    sans-serif;

  h2 {
    margin-bottom: 20px;
    color: #2d3748;
  }

  .editor-container {
    position: relative;
    margin-bottom: 20px;
  }

  .focus-indicator {
    margin-top: 8px;
    font-size: 12px;
    color: #4f46e5;
    font-weight: 500;
  }

  .editor-tips {
    margin-top: 8px;
    font-size: 12px;
    color: #718096;
    padding: 8px;
    background-color: #f7fafc;
    border-radius: 4px;
    border-left: 3px solid #4299e1;
  }

  .variable-space-toggle {
    margin-bottom: 10px;
    display: flex;
    flex-direction: column;

    label {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #4a5568;
      cursor: pointer;

      input {
        margin-right: 6px;
      }
    }

    .toggle-hint {
      margin-top: 8px;
      margin-left: 24px;
      font-size: 13px;
      color: #718096;
      background-color: #f0f4fa;
      padding: 8px 12px;
      border-radius: 4px;
      border-left: 3px solid #4f46e5;

      p {
        margin: 0 0 6px 0;
        font-weight: 500;
        color: #4a5568;
      }

      ol {
        margin: 0;
        padding-left: 16px;

        li {
          margin-bottom: 4px;
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }

  .preview-container,
  .metadata-container,
  .variables-container {
    margin-top: 30px;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    padding: 15px;
    background-color: #f8fafc;

    h3 {
      margin-top: 0;
      margin-bottom: 10px;
      color: #4a5568;
      font-size: 16px;
    }

    .preview-content {
      min-height: 100px;

      code {
        background-color: #edf2f7;
        padding: 2px 4px;
        border-radius: 3px;
        font-family: monospace;
      }

      pre {
        background-color: #edf2f7;
        padding: 10px;
        border-radius: 4px;
        overflow-x: auto;
      }

      .variable {
        color: #be185d;
        background-color: rgba(190, 24, 93, 0.1);
        padding: 0 2px;
        border-radius: 3px;
      }
    }

    pre {
      background-color: #edf2f7;
      padding: 10px;
      border-radius: 4px;
      overflow-x: auto;
      font-family: monospace;
      font-size: 12px;
    }
  }

  .variables-container {
    h3 {
      margin-top: 0;
      margin-bottom: 10px;
      color: #4a5568;
      font-size: 16px;
      display: flex;
      align-items: center;

      &::after {
        content: '';
        display: block;
        height: 1px;
        background-color: #e2e8f0;
        flex-grow: 1;
        margin-left: 10px;
      }
    }

    .variables-list {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-bottom: 15px;

      .variable-item {
        background-color: #f3e8ff;
        border: 1px solid #e9d5ff;
        color: #6b21a8;
        padding: 6px 10px;
        border-radius: 4px;
        font-size: 13px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;

        &:hover {
          background-color: #ede9fe;
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
      }
    }

    .variables-info {
      border-top: 1px dashed #e2e8f0;
      margin-top: 5px;
      padding-top: 10px;
      font-size: 12px;
      color: #64748b;

      p {
        margin: 0 0 5px 0;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .template-example {
    margin-top: 20px;

    h3 {
      margin-bottom: 10px;
      font-size: 16px;
      color: #4a5568;
    }

    button {
      margin-right: 8px;
      margin-bottom: 8px;
      padding: 6px 12px;
      background-color: #8b5cf6;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 14px;
      cursor: pointer;

      &:hover {
        background-color: #7c3aed;
      }
    }
  }

  .controls {
    margin-top: 20px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;

    button {
      padding: 8px 16px;
      background-color: #4299e1;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.2s;

      &:hover {
        background-color: #3182ce;
      }
    }
  }

  .suggestions-panel {
    position: absolute;
    width: 280px;
    max-height: 300px;
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 10;
    overflow: hidden;

    .suggestions-header {
      padding: 8px 12px;
      background-color: #f7fafc;
      border-bottom: 1px solid #e2e8f0;
      font-weight: 500;
      color: #4a5568;
      font-size: 14px;
    }

    .suggestions-list {
      max-height: 250px;
      overflow-y: auto;
    }

    .suggestion-item {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      cursor: pointer;

      &:hover {
        background-color: #f7fafc;
      }

      .suggestion-icon {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        font-weight: bold;

        &.user-icon {
          background-color: #ebf4ff;
          color: #4299e1;
        }

        &.command-icon {
          background-color: #e9f5f2;
          color: #38b2ac;
        }
      }

      .suggestion-content {
        flex: 1;

        .suggestion-title {
          font-weight: 500;
          color: #2d3748;
          font-size: 14px;
        }

        .suggestion-desc {
          color: #718096;
          font-size: 12px;
          margin-top: 2px;
        }
      }
    }
  }
}
</style>
