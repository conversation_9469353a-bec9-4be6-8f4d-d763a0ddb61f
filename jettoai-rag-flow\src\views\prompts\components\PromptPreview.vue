<template>
  <div class="preview">
    <div class="preview-header font-[500] pb-4">预览与调试</div>
    <div class="preview-form">
      <div class="preview-form-md" style="flex: 1; overflow-y: auto">流式输出</div>
      <div class="preview-form-sure">
        <AiPromptEditor
          v-model="previewContent"
          placeholder="请输入问题测试大模型回复，回车发送，Shift+回车换行"
          @change="promptChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'PromptPreview'
})

const previewContent = ref<string>('')

const promptChange = (content: string) => {
  previewContent.value = content
}
</script>

<style scoped lang="scss">
.preview {
  height: 100%;
  padding-bottom: 38px;
}
.preview-form {
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
  padding-bottom: 38px;
  &-md {
    border: 1px solid #e9ebf2;
    border-radius: 4px;
    padding: 8px;
  }
  &-sure {
    margin-top: 20px;
  }
}
</style>
