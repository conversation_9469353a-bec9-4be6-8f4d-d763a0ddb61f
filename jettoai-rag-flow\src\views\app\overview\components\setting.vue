<template>
  <a-modal :visible="showSetModal" title="WebApp 设置" :closable="false">
    <a-form ref="formRef" :model="form" :layout="'vertical'">
      <a-form-item :rules="[{ required: true, message: `WebApp名称是必填项` }]" field="title" label="WebApp 名称">
        <a-input v-model="form.title" />
      </a-form-item>
      <a-form-item :rules="[{ required: true, message: `WebApp描述是必填项` }]" field="description" label="WebApp 描述">
        <a-input v-model="form.description" />
      </a-form-item>
      <a-form-item
        :rules="[{ required: true, message: `请选择工作流详情` }]"
        field="show_workflow_steps"
        label="工作流详情"
      >
        <div class="steps">
          <span>在 WebApp 中展示或者隐藏工作流详情</span>
          <a-switch v-model="form.show_workflow_steps" size="small" type="round" />
        </div>
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="cancel">取消</a-button>
      <a-button @click="submit">保存</a-button>
    </template>
  </a-modal>
</template>
<script setup lang="ts">
import { siteSubmit } from '@/apis/workflow'

const form = ref({
  title: '',
  description: '',
  show_workflow_steps: true
})
const emit = defineEmits(['cancel'])
const route = useRoute()
const appId = route.params.appId as string
const formRef = ref()
const showSetModal = ref(true)
const rootMethods = inject('rootMethods')
const submit = () => {
  formRef.value
    .validate()
    .then(() => {
      console.log(appId)

      siteSubmit(appId, {
        ...form.value
      }).then((res) => {
        rootMethods.getAppConfigFn()
      })
    })
    .catch((error) => {
      console.error('表单验证失败:', error)
    })
}
const cancel = () => {
  emit('cancel')
}
</script>
<style scoped lang="scss">
.steps {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
</style>
