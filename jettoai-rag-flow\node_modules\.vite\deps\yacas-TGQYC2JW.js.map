{"version": 3, "sources": ["../../.pnpm/@codemirror+legacy-modes@6.5.1/node_modules/@codemirror/legacy-modes/mode/yacas.js"], "sourcesContent": ["function words(str) {\n  var obj = {}, words = str.split(\" \");\n  for (var i = 0; i < words.length; ++i) obj[words[i]] = true;\n  return obj;\n}\n\nvar bodiedOps = words(\"Assert BackQuote D Defun Deriv For ForEach FromFile \" +\n                      \"FromString Function Integrate InverseTaylor Limit \" +\n                      \"LocalSymbols Macro MacroRule MacroRulePattern \" +\n                      \"NIntegrate Rule RulePattern Subst TD TExplicitSum \" +\n                      \"TSum Taylor Taylor1 Taylor2 Taylor3 ToFile \" +\n                      \"ToStdout ToString TraceRule Until While\");\n\n// patterns\nvar pFloatForm  = \"(?:(?:\\\\.\\\\d+|\\\\d+\\\\.\\\\d*|\\\\d+)(?:[eE][+-]?\\\\d+)?)\";\nvar pIdentifier = \"(?:[a-zA-Z\\\\$'][a-zA-Z0-9\\\\$']*)\";\n\n// regular expressions\nvar reFloatForm    = new RegExp(pFloatForm);\nvar reIdentifier   = new RegExp(pIdentifier);\nvar rePattern      = new RegExp(pIdentifier + \"?_\" + pIdentifier);\nvar reFunctionLike = new RegExp(pIdentifier + \"\\\\s*\\\\(\");\n\nfunction tokenBase(stream, state) {\n  var ch;\n\n  // get next character\n  ch = stream.next();\n\n  // string\n  if (ch === '\"') {\n    state.tokenize = tokenString;\n    return state.tokenize(stream, state);\n  }\n\n  // comment\n  if (ch === '/') {\n    if (stream.eat('*')) {\n      state.tokenize = tokenComment;\n      return state.tokenize(stream, state);\n    }\n    if (stream.eat(\"/\")) {\n      stream.skipToEnd();\n      return \"comment\";\n    }\n  }\n\n  // go back one character\n  stream.backUp(1);\n\n  // update scope info\n  var m = stream.match(/^(\\w+)\\s*\\(/, false);\n  if (m !== null && bodiedOps.hasOwnProperty(m[1]))\n    state.scopes.push('bodied');\n\n  var scope = currentScope(state);\n\n  if (scope === 'bodied' && ch === '[')\n    state.scopes.pop();\n\n  if (ch === '[' || ch === '{' || ch === '(')\n    state.scopes.push(ch);\n\n  scope = currentScope(state);\n\n  if (scope === '[' && ch === ']' ||\n      scope === '{' && ch === '}' ||\n      scope === '(' && ch === ')')\n    state.scopes.pop();\n\n  if (ch === ';') {\n    while (scope === 'bodied') {\n      state.scopes.pop();\n      scope = currentScope(state);\n    }\n  }\n\n  // look for ordered rules\n  if (stream.match(/\\d+ *#/, true, false)) {\n    return 'qualifier';\n  }\n\n  // look for numbers\n  if (stream.match(reFloatForm, true, false)) {\n    return 'number';\n  }\n\n  // look for placeholders\n  if (stream.match(rePattern, true, false)) {\n    return 'variableName.special';\n  }\n\n  // match all braces separately\n  if (stream.match(/(?:\\[|\\]|{|}|\\(|\\))/, true, false)) {\n    return 'bracket';\n  }\n\n  // literals looking like function calls\n  if (stream.match(reFunctionLike, true, false)) {\n    stream.backUp(1);\n    return 'variableName.function';\n  }\n\n  // all other identifiers\n  if (stream.match(reIdentifier, true, false)) {\n    return 'variable';\n  }\n\n  // operators; note that operators like @@ or /; are matched separately for each symbol.\n  if (stream.match(/(?:\\\\|\\+|\\-|\\*|\\/|,|;|\\.|:|@|~|=|>|<|&|\\||_|`|'|\\^|\\?|!|%|#)/, true, false)) {\n    return 'operator';\n  }\n\n  // everything else is an error\n  return 'error';\n}\n\nfunction tokenString(stream, state) {\n  var next, end = false, escaped = false;\n  while ((next = stream.next()) != null) {\n    if (next === '\"' && !escaped) {\n      end = true;\n      break;\n    }\n    escaped = !escaped && next === '\\\\';\n  }\n  if (end && !escaped) {\n    state.tokenize = tokenBase;\n  }\n  return 'string';\n};\n\nfunction tokenComment(stream, state) {\n  var prev, next;\n  while((next = stream.next()) != null) {\n    if (prev === '*' && next === '/') {\n      state.tokenize = tokenBase;\n      break;\n    }\n    prev = next;\n  }\n  return 'comment';\n}\n\nfunction currentScope(state) {\n  var scope = null;\n  if (state.scopes.length > 0)\n    scope = state.scopes[state.scopes.length - 1];\n  return scope;\n}\n\nexport const yacas = {\n  name: \"yacas\",\n  startState: function() {\n    return {\n      tokenize: tokenBase,\n      scopes: []\n    };\n  },\n  token: function(stream, state) {\n    if (stream.eatSpace()) return null;\n    return state.tokenize(stream, state);\n  },\n  indent: function(state, textAfter, cx) {\n    if (state.tokenize !== tokenBase && state.tokenize !== null)\n      return null;\n\n    var delta = 0;\n    if (textAfter === ']' || textAfter === '];' ||\n        textAfter === '}' || textAfter === '};' ||\n        textAfter === ');')\n      delta = -1;\n\n    return (state.scopes.length + delta) * cx.unit;\n  },\n\n  languageData: {\n    electricInput: /[{}\\[\\]()\\;]/,\n    commentTokens: {line: \"//\", block: {open: \"/*\", close: \"*/\"}}\n  }\n};\n"], "mappings": ";;;AAAA,SAAS,MAAM,KAAK;AAClB,MAAI,MAAM,CAAC,GAAGA,SAAQ,IAAI,MAAM,GAAG;AACnC,WAAS,IAAI,GAAG,IAAIA,OAAM,QAAQ,EAAE,EAAG,KAAIA,OAAM,CAAC,CAAC,IAAI;AACvD,SAAO;AACT;AAEA,IAAI,YAAY,MAAM,0RAKyC;AAG/D,IAAI,aAAc;AAClB,IAAI,cAAc;AAGlB,IAAI,cAAiB,IAAI,OAAO,UAAU;AAC1C,IAAI,eAAiB,IAAI,OAAO,WAAW;AAC3C,IAAI,YAAiB,IAAI,OAAO,cAAc,OAAO,WAAW;AAChE,IAAI,iBAAiB,IAAI,OAAO,cAAc,SAAS;AAEvD,SAAS,UAAU,QAAQ,OAAO;AAChC,MAAI;AAGJ,OAAK,OAAO,KAAK;AAGjB,MAAI,OAAO,KAAK;AACd,UAAM,WAAW;AACjB,WAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,EACrC;AAGA,MAAI,OAAO,KAAK;AACd,QAAI,OAAO,IAAI,GAAG,GAAG;AACnB,YAAM,WAAW;AACjB,aAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,IACrC;AACA,QAAI,OAAO,IAAI,GAAG,GAAG;AACnB,aAAO,UAAU;AACjB,aAAO;AAAA,IACT;AAAA,EACF;AAGA,SAAO,OAAO,CAAC;AAGf,MAAI,IAAI,OAAO,MAAM,eAAe,KAAK;AACzC,MAAI,MAAM,QAAQ,UAAU,eAAe,EAAE,CAAC,CAAC;AAC7C,UAAM,OAAO,KAAK,QAAQ;AAE5B,MAAI,QAAQ,aAAa,KAAK;AAE9B,MAAI,UAAU,YAAY,OAAO;AAC/B,UAAM,OAAO,IAAI;AAEnB,MAAI,OAAO,OAAO,OAAO,OAAO,OAAO;AACrC,UAAM,OAAO,KAAK,EAAE;AAEtB,UAAQ,aAAa,KAAK;AAE1B,MAAI,UAAU,OAAO,OAAO,OACxB,UAAU,OAAO,OAAO,OACxB,UAAU,OAAO,OAAO;AAC1B,UAAM,OAAO,IAAI;AAEnB,MAAI,OAAO,KAAK;AACd,WAAO,UAAU,UAAU;AACzB,YAAM,OAAO,IAAI;AACjB,cAAQ,aAAa,KAAK;AAAA,IAC5B;AAAA,EACF;AAGA,MAAI,OAAO,MAAM,UAAU,MAAM,KAAK,GAAG;AACvC,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,MAAM,aAAa,MAAM,KAAK,GAAG;AAC1C,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,MAAM,WAAW,MAAM,KAAK,GAAG;AACxC,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,MAAM,uBAAuB,MAAM,KAAK,GAAG;AACpD,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,MAAM,gBAAgB,MAAM,KAAK,GAAG;AAC7C,WAAO,OAAO,CAAC;AACf,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,MAAM,cAAc,MAAM,KAAK,GAAG;AAC3C,WAAO;AAAA,EACT;AAGA,MAAI,OAAO,MAAM,gEAAgE,MAAM,KAAK,GAAG;AAC7F,WAAO;AAAA,EACT;AAGA,SAAO;AACT;AAEA,SAAS,YAAY,QAAQ,OAAO;AAClC,MAAI,MAAM,MAAM,OAAO,UAAU;AACjC,UAAQ,OAAO,OAAO,KAAK,MAAM,MAAM;AACrC,QAAI,SAAS,OAAO,CAAC,SAAS;AAC5B,YAAM;AACN;AAAA,IACF;AACA,cAAU,CAAC,WAAW,SAAS;AAAA,EACjC;AACA,MAAI,OAAO,CAAC,SAAS;AACnB,UAAM,WAAW;AAAA,EACnB;AACA,SAAO;AACT;AAEA,SAAS,aAAa,QAAQ,OAAO;AACnC,MAAI,MAAM;AACV,UAAO,OAAO,OAAO,KAAK,MAAM,MAAM;AACpC,QAAI,SAAS,OAAO,SAAS,KAAK;AAChC,YAAM,WAAW;AACjB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAEA,SAAS,aAAa,OAAO;AAC3B,MAAI,QAAQ;AACZ,MAAI,MAAM,OAAO,SAAS;AACxB,YAAQ,MAAM,OAAO,MAAM,OAAO,SAAS,CAAC;AAC9C,SAAO;AACT;AAEO,IAAM,QAAQ;AAAA,EACnB,MAAM;AAAA,EACN,YAAY,WAAW;AACrB,WAAO;AAAA,MACL,UAAU;AAAA,MACV,QAAQ,CAAC;AAAA,IACX;AAAA,EACF;AAAA,EACA,OAAO,SAAS,QAAQ,OAAO;AAC7B,QAAI,OAAO,SAAS,EAAG,QAAO;AAC9B,WAAO,MAAM,SAAS,QAAQ,KAAK;AAAA,EACrC;AAAA,EACA,QAAQ,SAAS,OAAO,WAAW,IAAI;AACrC,QAAI,MAAM,aAAa,aAAa,MAAM,aAAa;AACrD,aAAO;AAET,QAAI,QAAQ;AACZ,QAAI,cAAc,OAAO,cAAc,QACnC,cAAc,OAAO,cAAc,QACnC,cAAc;AAChB,cAAQ;AAEV,YAAQ,MAAM,OAAO,SAAS,SAAS,GAAG;AAAA,EAC5C;AAAA,EAEA,cAAc;AAAA,IACZ,eAAe;AAAA,IACf,eAAe,EAAC,MAAM,MAAM,OAAO,EAAC,MAAM,MAAM,OAAO,KAAI,EAAC;AAAA,EAC9D;AACF;", "names": ["words"]}