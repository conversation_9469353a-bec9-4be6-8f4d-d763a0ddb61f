<template>
  <headerAPi :appInfo="appInfo" @tab-change="changeTabkey" />
  <AiPageLayout />
</template>

<script setup lang="ts">
import headerAPi from './components/headerAPi.vue'
import { getAppConfig } from '@/apis'
defineOptions({ name: 'overview' })

const props = defineProps<{
  appInfo?: any
}>()
const emit = defineEmits(['tab-change'])
const changeTabkey = () => {
  emit('tab-change', 'develop')
}
onMounted(() => {})
</script>

<style scoped lang="scss"></style>
