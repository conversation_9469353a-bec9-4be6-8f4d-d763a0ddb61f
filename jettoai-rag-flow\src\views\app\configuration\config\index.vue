<template>
  <div class="overflow-y-auto">
    <div class="relative grow overflow-y-auto px-6 pb-[50px]">
      <!--提示词-->
      <ConfigPrompt :promptTemplate="modelConfig.configs.prompt_template" @updatePrompt="updatePrompt" />

      <!--变量-->
      <ConfigVar
        :promptVariables="modelConfig.configs.prompt_variables"
        @onPromptVariablesChange="onPromptVariablesChange"
      />

      <!--知识库-->
      <DatasetConfig
        :dataSets="modelConfig.dataSets"
        :datasetConfigs="datasetConfigs"
        @addDataset="addDataset"
        @changeDatasetConfig="changeDatasetConfig"
        @changeDataset="changeDataset"
      />
      <SelectDataset
        v-if="isDatasetModalVisible"
        :selectedDatasetList="modelConfig.dataSets"
        @closeDatasetDialog="closeDatasetDialog"
        @changeDataset="changeDataset"
      />

      <!--工具-->
      <AgentTools
        :collectionList="collectionList"
        :selectedTools="selectedTools"
        @changeTool="changeTool"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import ConfigPrompt from '@/views/app/configuration/config-prompt/index.vue'
import ConfigVar from '@/views/app/configuration/config-var/index.vue'
import DatasetConfig from '@/views/app/configuration/dataset-config/DatasetConfig.vue'
import SelectDataset from '@/views/app/configuration/dataset-config/select-dataset/SelectDataset.vue'
import AgentTools from '@/views/app/configuration/agent-tools/AgentTools.vue'

const props = withDefaults(
  defineProps<{
    modelConfig?: any
    datasetConfigs?: any
    promptVariables?: any[]
    collectionList?: any[] // 系统查询的工具函数List。所有的。
  }>(),
  {
    modelConfig: () => ({}) as any,
    datasetConfigs: () => ({}) as any,
    promptVariables: () => [],
    collectionList: () => []
  }
)
const emits = defineEmits(['setModelConfig', 'updateDatasetConfig'])
// 在agent中用户选中的toolList（包括启用和禁用的）
const selectedTools = computed(() => {
  return props.modelConfig.agentConfig.tools
})
const updatePrompt = (val: any) => {
  const modelConfig = {
    ...props.modelConfig
  }
  modelConfig.configs.prompt_template = val
  emits('setModelConfig', modelConfig)
}
/**
 * 变量：更新
 * @param newVariables 变量的最新值
 */
const onPromptVariablesChange = (newVariables: any[]) => {
  console.log('formList更新：', newVariables)
  const modelConfig = {
    ...props.modelConfig
  }
  modelConfig.configs.prompt_variables = newVariables
  // emits('onPromptVariablesChange', newVariables)
  emits('setModelConfig', modelConfig)
}

const isDatasetModalVisible = ref(false)
const addDataset = (type) => {
  isDatasetModalVisible.value = true
}
const closeDatasetDialog = (type: string, newVal) => {
  if(type === 'ok'){
    changeDataset(newVal)
  }
  isDatasetModalVisible.value = false
}

/**
 * 知识库
 */
const changeDatasetConfig = (datasetConfig) => {
  emits('updateDatasetConfig', datasetConfig)
}
const changeDataset = (newVal) => {
  const modelConfig = {
    ...props.modelConfig
  }
  modelConfig.dataSets = newVal
  emits('setModelConfig', modelConfig)
}
/**
 * 工具：
 * @param tools
 */
const changeTool = (tools) => {
  const modelConfig = {
    ...props.modelConfig
  }
  modelConfig.agentConfig.tools = tools
  emits('setModelConfig', modelConfig)
}
</script>
<style scoped lang="scss"></style>
