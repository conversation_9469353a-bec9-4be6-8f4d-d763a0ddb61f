<template>
  <div class="settings-modal">
    <a-drawer
      :width="640"
      :footer="false"
      :visible="true"
      title="知识库设置"
      @cancel="handleOk('cancel')"
      @ok="handleOk('ok')"
    >
      <SettingsContent :datasetId="datasetId" />
    </a-drawer>
  </div>
</template>
<script setup lang="ts">
import SettingsContent from '@/views/datasets/documents/SettingsContent.vue'

const props = defineProps(['datasetId'])
const emits = defineEmits(['closeSettingModal'])

const handleOk = (type = 'cancel') => {
  console.log('type:', type)
  emits('closeSettingModal', type)
}
</script>
<style scoped lang="scss"></style>
<style lang="scss">
// 检索设置容器
.retrieval-settings-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;

  .retrieval-option {
    border: 1px solid var(--color-border-2);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s;

    &.active {
      border-color: var(--color-primary);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .option-header {
      display: flex;
      align-items: center;
      padding: 16px;
      background-color: var(--color-fill-1);
      cursor: pointer;

      .option-icon {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;

        .icon {
          font-size: 18px;
          color: var(--color-text-1);
        }
      }

      .option-info {
        flex: 1;

        .option-title {
          font-weight: 500;
          margin-bottom: 4px;
          display: flex;
          align-items: center;
          gap: 6px;
        }

        .option-desc {
          font-size: 12px;
          color: var(--color-text-3);
        }
      }
    }

    .option-content {
      padding: 16px;
      background-color: #fff;
      border-top: 1px solid var(--color-border-2);

      .content-section {
        .content-detail {
          font-size: 13px;
          color: var(--color-text-2);
          margin-bottom: 16px;
          line-height: 1.6;
        }

        .hybrid-modes {
          display: flex;
          flex-direction: row;
          gap: 12px;
          margin-bottom: 20px;

          .hybrid-mode-option {
            flex: 1;
            display: flex;
            align-items: center;
            padding: 12px;
            border: 1px solid var(--color-border-2);
            border-radius: 6px;
            cursor: pointer;

            &.selected {
              border-color: var(--color-primary);
              background-color: var(--color-primary-light-1);
            }

            .mode-icon {
              font-size: 18px;
              margin-right: 12px;
              color: var(--color-text-2);
            }

            .mode-info {
              flex: 1;

              .mode-name {
                font-weight: 500;
                margin-bottom: 4px;
              }

              .mode-desc {
                font-size: 12px;
                color: var(--color-text-3);
                line-height: 1.5;
              }
            }
          }
        }

        .weight-settings {
          padding: 12px;
          background-color: var(--color-fill-1);
          border-radius: 6px;

          .weight-setting-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            .weight-label {
              width: 120px;
              font-size: 13px;
            }

            .arco-slider {
              flex: 1;
              margin: 0 12px;
            }

            .weight-value {
              width: 50px;
              font-size: 12px;
            }
          }

          .weight-note {
            font-size: 12px;
            color: var(--color-text-3);
            text-align: right;
            padding-right: 30px;
          }
        }

        .rerank-settings {
          .rerank-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;

            .hint-icon {
              font-size: 14px;
              color: var(--color-text-3);
              margin-left: 4px;
              cursor: pointer;
            }
          }

          .select-label {
            font-size: 13px;
            margin-bottom: 8px;
          }

          .arco-select {
            width: 100%;
            margin-bottom: 16px;
          }

          .params-config {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            margin-top: 12px;

            .param-item {
              display: flex;
              align-items: center;
              gap: 8px;

              .param-label {
                font-size: 13px;
                min-width: 80px;
              }

              .param-label1 {
                min-width: 100px;
                display: flex;
                align-items: center;
              }

              .hint-icon {
                font-size: 14px;
                color: var(--color-text-3);
                cursor: pointer;
              }
            }
          }
        }
      }
    }
  }
}
</style>
