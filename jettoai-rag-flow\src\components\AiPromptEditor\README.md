# AiPromptEditor 提示词编辑器

一个用于创建 AI 提示词输入框的 Vue 3 组件，支持 Markdown 语法和关键字触发功能。

## 功能特点

- 基于 Vue 3、CodeMirror 6 和 vue-codemirror6 构建
- Markdown 语法高亮
- 关键字触发功能（例如，'@' 用于提及用户，'/' 用于命令）
- 自定义占位符文本
- 自动高度调整
- 可定制样式
- 不显示行号
- 无当前行高亮效果
- **焦点状态边框高亮**
- **支持模板小部件和元数据**
- **支持删除模板小部件**
- **花括号自动补全功能**
- **变量空间功能**

## 安装

确保你已安装所需依赖：

```bash
npm install vue-codemirror6 @codemirror/view @codemirror/state @codemirror/language @codemirror/lang-markdown @codemirror/commands
```

## 使用方法

### 基本用法

```vue
<template>
  <AiPromptEditor v-model="promptText" placeholder="在此输入提示词..." />
</template>

<script setup>
import { ref } from 'vue'
import AiPromptEditor from '@/components/AiPromptEditor/index.vue'

const promptText = ref('')
</script>
```

### 使用关键字触发功能

```vue
<template>
  <AiPromptEditor
    v-model="promptText"
    placeholder="输入 @ 提及某人或 / 使用命令"
    :trigger-chars="['@', '/']"
    @keyword-trigger="handleKeywordTrigger"
    @trigger-close="handleTriggerClose"
    ref="editorRef"
  >
    <template #keyword-trigger="{ trigger, position }">
      <!-- 自定义下拉菜单 UI -->
      <div v-if="showSuggestions" class="suggestions" :style="{ top: position.y + 'px', left: position.x + 'px' }">
        <!-- 建议列表 -->
      </div>
    </template>
  </AiPromptEditor>
</template>

<script setup>
import { ref } from 'vue'
import AiPromptEditor from '@/components/AiPromptEditor/index.vue'

const promptText = ref('')
const editorRef = ref(null)
const showSuggestions = ref(false)

const handleKeywordTrigger = (event) => {
  showSuggestions.value = true
  // event 包含：{ trigger, position, text }
}

const handleTriggerClose = () => {
  showSuggestions.value = false
}

// 在当前触发位置插入文本
const insertSuggestion = (text) => {
  editorRef.value.replaceTrigger(text)
}
</script>
```

## 模板插入

组件支持插入模板小部件，这些小部件显示为带有特殊样式的文本块，并在内部保存元数据（如标签和属性）。您可以在模板小部件后面继续输入内容。

### 基本用法

```vue
<script setup>
import { ref } from 'vue'
import AiPromptEditor from './AiPromptEditor.vue'

const editorRef = ref(null)

// 插入简单文本
const insertText = () => {
  editorRef.value.insertTemplate('这是一段文本')
}

// 插入模板小部件
const insertTemplateWidget = () => {
  editorRef.value.insertTemplate({
    text: '张三',
    tag: 'span'
  })
}

// 插入带属性的模板小部件
const insertTemplateWithAttrs = () => {
  editorRef.value.insertTemplate({
    text: '李四',
    tag: 'user',
    attributes: {
      id: 'user-123',
      role: 'admin'
    }
  })
}
</script>

<template>
  <AiPromptEditor ref="editorRef" />
  <button @click="insertText">插入文本</button>
  <button @click="insertTemplateWidget">插入模板小部件</button>
  <button @click="insertTemplateWithAttrs">插入带属性的模板</button>
</template>
```

## 花括号自动补全和变量空间功能

组件支持花括号自动补全和变量空间功能，可以通过设置 `enableVariableSpace` 属性来启用或禁用这些功能。

### 花括号自动补全

当启用花括号自动补全功能时：

1. 输入 `{` 将自动补全为 `{}`，并将光标定位在花括号中间
2. 继续输入内容后，光标会保持在正确位置

### 变量空间功能

当启用变量空间功能时：

1. 输入 `{{` 将自动补全为 `{{}}`，并将光标定位在最内层花括号中间
2. 在花括号内输入英文或数字（变量名）将被特殊样式渲染
3. 组件会自动检测和收集所有变量，并通过 `variable-update` 事件通知外部组件

### 使用方法

```vue
<template>
  <div>
    <label>
      <input type="checkbox" v-model="enableVarSpace" />
      启用变量空间功能
    </label>
    
    <AiPromptEditor
      ref="editorRef"
      v-model="content"
      :enable-variable-space="enableVarSpace"
      @variable-update="handleVariableUpdate"
    />
    
    <div v-if="variables.length > 0" class="detected-variables">
      <h3>检测到的变量：</h3>
      <ul>
        <li v-for="(variable, index) in variables" :key="index">{{ variable }}</li>
      </ul>
    </div>
    
    <button @click="insertTemplate">插入模板示例</button>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import AiPromptEditor from './AiPromptEditor.vue'

const content = ref('')
const editorRef = ref(null)
const enableVarSpace = ref(true)
const variables = ref([])

const handleVariableUpdate = (newVariables) => {
  variables.value = newVariables
}

const insertTemplate = () => {
  if (editorRef.value) {
    editorRef.value.insertText('你好，{{name}}！欢迎来到{{company}}。')
  }
}

// 获取变量列表
const getVariables = () => {
  if (editorRef.value) {
    return editorRef.value.getVariables()
  }
  return []
}
</script>
```

## 元数据功能

插入模板小部件时，组件会将标签和属性信息作为元数据保存，并通过`metadata-update`事件通知外部组件。

### 监听元数据更新

```vue
<template>
  <AiPromptEditor 
    ref="editorRef" 
    @metadata-update="handleMetadataUpdate" 
  />
</template>

<script setup>
const handleMetadataUpdate = (metadata) => {
  console.log('元数据已更新:', metadata)
  // 可以根据元数据进行自定义渲染或其他操作
}
</script>
```

### 获取和清除元数据

```js
// 获取当前元数据
const metadata = editorRef.value.getMetadata()

// 清除所有元数据
editorRef.value.clearMetadata()
```

### 获取带HTML标签的内容

编辑器中只会显示模板小部件的文本内容，但你可以使用`getContentWithHtml()`方法获取包含所有HTML标签的完整内容：

```vue
<template>
  <AiPromptEditor ref="editorRef" />
  <div>
    <button @click="getHtmlContent">获取HTML内容</button>
    <div v-html="htmlContent"></div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import AiPromptEditor from './AiPromptEditor.vue'

const editorRef = ref(null)
const htmlContent = ref('')

const getHtmlContent = () => {
  if (editorRef.value) {
    htmlContent.value = editorRef.value.getContentWithHtml()
  }
}

// 插入带HTML标签的内容
const insertTemplateWidget = () => {
  if (editorRef.value) {
    editorRef.value.insertTemplate({
      text: '用户提及',
      tag: 'user',
      attributes: {
        id: 'user-123',
        role: 'admin'
      }
    })
  }
}
</script>
```

## 焦点状态高亮

编辑器支持在获取焦点时高亮边框，可以通过`focusBorderColor`属性自定义高亮颜色。

```vue
<template>
  <AiPromptEditor 
    ref="editorRef" 
    focus-border-color="#4f46e5"
    @focus="handleFocus"
    @blur="handleBlur"
  />
</template>

<script setup>
const handleFocus = () => {
  console.log('编辑器获得焦点')
}

const handleBlur = () => {
  console.log('编辑器失去焦点')
}
</script>
```

## 属性

| 属性 | 类型 | 默认值 | 描述 |
|------|------|---------|-------------|
| `modelValue` | String | `''` | 编辑器内容（与 v-model 一起使用） |
| `placeholder` | String | `'在此输入提示词...'` | 编辑器为空时的占位符文本 |
| `height` | String | `'200px'` | 编辑器高度（当 autoHeight 为 false 时） |
| `minHeight` | String | `'100px'` | 编辑器最小高度 |
| `maxHeight` | String | `'500px'` | 编辑器最大高度 |
| `autoHeight` | Boolean | `true` | 编辑器是否根据内容自动调整高度 |
| `triggerChars` | Array | `['@', '/']` | 触发关键字建议的字符 |
| `tabSize` | Number | `2` | 制表符大小 |
| `focusBorderColor` | String | `'#3b82f6'` | 编辑器获取焦点时的边框颜色 |
| `enableVariableSpace` | Boolean | `false` | 是否启用花括号自动补全和变量空间功能 |

## 事件

| 事件 | 载荷 | 描述 |
|-------|---------|-------------|
| `update:modelValue` | `String` | 编辑器内容变化时触发 |
| `change` | `String` | 编辑器内容变化时触发 |
| `ready` | `{ view }` | 编辑器准备就绪时触发 |
| `focus` | - | 编辑器获得焦点时触发 |
| `blur` | - | 编辑器失去焦点时触发 |
| `keyword-trigger` | `{ trigger, position, text }` | 检测到关键字触发时触发 |
| `trigger-close` | - | 关键字触发关闭时触发 |
| `metadata-update` | `{ metadata }` | 元数据更新时触发 |
| `variable-update` | `string[]` | 检测到的变量列表更新时触发 |

## 插槽

| 插槽 | 属性 | 描述 |
|------|-------|-------------|
| `keyword-trigger` | `{ trigger, position }` | 用于渲染自定义关键字触发 UI 的插槽 |

## 方法

以下方法通过模板引用（template refs）暴露：

| 方法 | 参数 | 描述 |
|--------|------------|-------------|
| `focus()` | - | 聚焦编辑器 |
| `blur()` | - | 取消编辑器焦点 |
| `insertText(text)` | `text: string` | 在当前光标位置插入文本 |
| `insertNewline()` | - | 在当前光标位置插入换行符 |
| `replaceTrigger(text)` | `text: string` | 用提供的文本替换当前触发文本 |
| `closeTrigger()` | - | 关闭当前触发 |
| `getEditor()` | - | 获取底层 CodeMirror 编辑器实例 |
| `insertTemplate(options)` | `options: TemplateOptions` | 插入模板 |
| `getMetadata()` | - | 获取当前元数据 |
| `clearMetadata()` | - | 清除所有元数据 |
| `getContentWithHtml()` | - | 获取包含HTML标签的完整内容 |
| `getVariables()` | - | 获取检测到的变量列表 |

## 样式定制

组件提供了基本样式，可以使用 CSS 进行自定义。主容器的类名为 `.ai-prompt-editor`，你可以使用深度选择器（deep selectors）来定位内部元素：

```scss
.ai-prompt-editor {
  // 自定义样式
  
  &.focused {
    // 聚焦状态样式
    border-color: #4f46e5;
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
  }
  
  :deep(.cm-editor) {
    // CodeMirror 编辑器样式
  }
  
  :deep(.cm-content) {
    // 内容区域样式
  }
  
  // 模板小部件样式
  :deep(.template-widget) {
    display: inline-block;
    padding: 0 2px;
    border-radius: 2px;
    background-color: rgba(59, 130, 246, 0.1);
    border-bottom: 1px dashed #3b82f6;
  }
  
  // 变量空间样式
  :deep(.jinja-expression) {
    display: inline;
    color: #0f766e; // 深绿色
    
    .valid-Fuk9lu {
      color: #be185d; // 深粉色
      font-weight: 500;
    }
  }
  
  // Markdown 元素样式
  :deep(.cm-header) { /* ... */ }
  :deep(.cm-strong) { /* ... */ }
  :deep(.cm-emphasis) { /* ... */ }
  :deep(.cm-link) { /* ... */ }
}
```

## 示例

查看 `demo.vue` 文件获取如何使用 AiPromptEditor 组件及其所有功能的完整示例。

### 模板小部件显示

当插入模板时，编辑器会创建一个特殊的模板小部件来显示文本。这些小部件有以下特点：

1. 显示为带有特殊样式的文本块（浅蓝色背景和虚线下划线）
2. 可以通过一次退格键或删除键完全删除
3. 可以在其后面继续输入内容
4. 在导出HTML内容时会自动转换为相应的HTML标签

```vue
<template>
  <div>
    <AiPromptEditor ref="editorRef" />
    <div class="tips">
      <p>模板小部件特点：</p>
      <ul>
        <li>特殊样式显示</li>
        <li>一键删除</li>
        <li>可在后面继续输入内容</li>
      </ul>
    </div>
  </div>
</template>
```

### 变量空间功能

当启用变量空间功能时，您可以使用双花括号 `{{}}` 定义变量，这些变量将以特殊样式显示，并被自动检测和收集：

```vue
<template>
  <div>
    <div class="toggle">
      <label>
        <input type="checkbox" v-model="enableVarSpace" />
        启用变量空间功能
      </label>
    </div>
    
    <AiPromptEditor 
      ref="editorRef" 
      :enable-variable-space="enableVarSpace"
      @variable-update="onVariableUpdate" 
    />
    
    <div v-if="variables.length > 0" class="variable-list">
      <h3>检测到的变量：</h3>
      <div v-for="(variable, index) in variables" :key="index" class="variable-item">
        {{ variable }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const editorRef = ref(null)
const enableVarSpace = ref(true)
const variables = ref([])

const onVariableUpdate = (newVars) => {
  variables.value = newVars
}

const insertVariableTemplate = () => {
  if (editorRef.value) {
    editorRef.value.insertText('你好，{{name}}！欢迎来到{{company}}。')
  }
}
</script>
```

### 模板选项

插入模板时可以使用以下选项：

```typescript
interface TemplateOptions {
  text: string;           // 要插入的文本
  tag: string;            // 标签名称
  attributes?: Record<string, string>; // 可选的标签属性
  cursorOffset?: number;  // 插入后光标的偏移量
}
```

- `text`: 要插入的文本内容
- `tag`: 标签名称
- `attributes`: 可选，标签属性对象
- `cursorOffset`: 可选，插入后光标的偏移量，默认为插入文本的长度

### 删除模板小部件

模板小部件可以通过以下方式一键删除：

1. 将光标放在模板小部件前面，然后按删除键（Delete）
2. 将光标放在模板小部件后面，然后按退格键（Backspace）
3. 将光标放在模板小部件内部，然后按退格键或删除键

当删除操作发生时，组件会自动清除相关的元数据并更新编辑器内容。

#### 模板小部件与普通文本的交互

当在模板小部件后面输入普通文本时，退格键的行为如下：

1. 首先删除普通文本中的字符
2. 当普通文本全部删除后，再次按退格键才会删除模板小部件

这种行为与常规文本编辑器的行为一致，确保用户在编辑时不会意外删除重要内容。 