<template>
  <div ref="ifbox" class="ifbox">
    <!-- v-for="item in nodeInfo.cases" -->
    <div v-for="(item, index) in nodeInfo.cases" :key="item.id" class="case-1">
      <div class="case-title" :style="{ 'padding-top': index > 0 ? '15px' : '0px' }">
        <div class="first-title">{{ index == 0 ? 'IF' : 'ELSE' }}</div>
        <div class="seconed-title">case{{ index + 1 }}</div>
      </div>
      <div class="right-content">
        <a-button
          v-if="item.conditions.length > 1"
          size="mini"
          class="mt-4 all-btn"
          @click="changelogicalOperator(item)"
        >
          {{ item.logical_operator }}
        </a-button>
        <div v-for="(item1, index1) in item.conditions" :key="item1.id" class="ifcase">
          <div class="left">
            <div class="case-select">
              <VariableSelector
                v-model:value-selector="item1.variable_selector"
                :style="{ width: '70%' }"
                :node-id="nodeId"
                @change="(a, b) => changeoptionlist(a, b, item1)"
              />
              <!-- <selectbox :defaultList="defaultList" :nodeitem="item1" @changeoptionlist="changeoptionlist"></selectbox> -->
              <!-- <a-select
             :style="{width:'70%'}"  @change="handleChangeContext(item1.variable_selector)" :default-value="item1.variable_selector.join('.')" placeholder="Please select ...">
              <a-option v-for="item in defaultList" :key="item.value_selector.join('.')" :value="item.value_selector.join('.')" :label="item.variable">
            </a-option>
            </a-select> -->
              <a-select
                v-model="item1.comparison_operator"
                size="mini"
                :style="{ width: '28%' }"
                placeholder="Please select ..."
              >
                <a-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
              </a-select>
            </div>
            <div class="case-input">
              <template v-if="item1.variable_selector[1] !== 'files'">
                <a-input
                  v-model="item1.value"
                  :style="{ width: '100%', height: '100%' }"
                  placeholder="输入值"
                  allow-clear
                />
              </template>
              <template v-else>
                <!-- <a-button  size="mini" v-if="item1.sub_variable_condition.conditions.length>1" @click="changefilelogicalOperator(item1)">{{ item1.sub_variable_condition .logical_operator }}</a-button> -->
                <div style="width: 18%">
                  <a-tag
                    v-if="item1.sub_variable_condition.conditions.length > 1"
                    checkable
                    color="arcoblue"
                    :default-checked="true"
                    @click="changefilelogicalOperator(item1)"
                  >
                    {{ item1.sub_variable_condition.logical_operator }}
                  </a-tag>
                </div>

                <div v-if="item1.sub_variable_condition" style="flex: 1">
                  <div
                    v-for="(item2, index2) in item1.sub_variable_condition.conditions"
                    :key="item2.id"
                    class="file-selector"
                    style="margin: 5px 0"
                  >
                    <div
                      class="flle-select-type"
                      style="display: flex; justify-content: space-between; align-items: center"
                    >
                      <a-select
                        v-model="item2.key"
                        :style="{ width: '50%' }"
                        placeholder="Please select ..."
                        @change="changefileKey(item2)"
                      >
                        <a-option v-for="item in SUB_VARIABLES" :key="item" :value="item">{{ item }}</a-option>
                      </a-select>
                      <a-select
                        v-model="item2.comparison_operator"
                        :style="{ width: '28%' }"
                        :disabled="item2.key == 'related_id'"
                        placeholder="Please select ..."
                      >
                        <a-option
                          v-for="item in changeKey(item2)"
                          :key="item.value"
                          :value="item.value"
                          :label="item.label"
                        />
                      </a-select>
                      <!-- <a-button type="text" @click="deletefileItem(item1, index2)">
                        <template #icon>
                          <icon-delete style="color: #676f83" size="16" />
                        </template>
                      </a-button> -->
                    </div>
                    <div class="flle-select-value" style="margin: 5px 0">
                      <fileValue :nodeitem="item2" :defaultList="defaultList" />
                      <!-- <a-select :style="{width:'100%'}" placeholder="Please select ...">
                      <a-option v-for="item in  list"  :key="item.value" :value="item.value" :label="item.label"></a-option>
                    </a-select> -->
                    </div>
                  </div>
                  <a-button size="mini" style="margin-top: 5px" @click="addchildfile(item1)">
                    <icon-plus />
                    添加子变量
                  </a-button>
                </div>
              </template>
            </div>
          </div>
          <a-button type="text" @click="deleteItem(item, index1)">
            <template #icon>
              <icon-delete style="color: #000" size="18" />
            </template>
          </a-button>
        </div>

        <div class="btns">
          <a-button class="mt-4" @click="addTemplateItem(item)">
            <icon-plus />
            添加条件
          </a-button>
          <a-button v-if="nodeInfo.cases.length > 1" class="mt-4" @click="removeItem(item, index)">
            <icon-delete />
            移除
          </a-button>
        </div>
      </div>
    </div>
    <a-divider />
    <a-button long class="mt-4" @click="addcaseItem">
      <icon-plus />
      ELSE
    </a-button>
    <a-divider />
  </div>
</template>
<script setup lang="ts">
import { uniqueId } from 'lodash-es'
import { comparisonOperator } from '@/views/app/workflow/types/workflow'
import { remove } from 'xe-utils'
import selectbox from './selectbox.vue'
import fileValue from './fileValue.vue'
import VariableSelector from '@/views/app/workflow/components/variable-selector/VariableSelector.vue'
const defaultList = [
  {
    label: '',
    required: false,
    readonly: true,
    type: 'string',
    variable: 'sys.user_id',
    value_selector: ['sys', 'user_id']
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'array[file]',
    variable: 'sys.files',
    value_selector: ['sys', 'files']
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.app_id',
    value_selector: ['sys', 'app_id']
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.workflow_id',
    value_selector: ['sys', 'workflow_id']
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.workflow_run_id',
    value_selector: ['sys', 'workflow_run_id']
  }
]
const SUB_VARIABLES = ['type', 'size', 'name', 'url', 'extension', 'mime_type', 'transfer_method', 'related_id']
const comparison_operatorlist = [
  {
    label: '是',
    value: 'in'
  },
  {
    label: '不是',
    value: 'not in'
  }
]
const extensionlist = [
  {
    label: '是',
    value: 'is'
  },
  {
    label: '不是',
    value: 'is not'
  },
  {
    label: '包含',
    value: 'contains'
  },
  {
    label: '不包含',
    value: 'not contains'
  }
]
const sizelist = [
  {
    label: '>',
    value: '>'
  },
  {
    label: '≥',
    value: '≥'
  },
  {
    label: '<',
    value: '<'
  },
  {
    label: '≤',
    value: '≤'
  }
]
const options = Object.keys(comparisonOperator).map((key) => ({
  label: comparisonOperator[key as keyof typeof comparisonOperator],
  value: key
}))
const list = ref<any[]>([]) // 初始为空数组
const operatorMap = {
  type: comparison_operatorlist,
  transfer_method: comparison_operatorlist,
  size: sizelist,
  name: options,
  url: options,
  mime_type: options,
  extension: extensionlist
}

const changeKey = (item) => {
  const targetList = operatorMap[item.key] || []
  // list.value = Array.isArray(targetList) ? [...targetList] : [];
  // console.log(list.value);
  return Array.isArray(targetList) ? [...targetList] : []
}

const changefileKey = (item) => {
  item.comparison_operator = ''
}
const changeoptionlist = (item, value, item1) => {
  console.log(item, value, item1)

  if (value.type == 'array[file]') {
    item1.sub_variable_condition
      ? item1.sub_variable_condition
      : (item1.sub_variable_condition = {
          case_id: uniqueId(UNIQUE_ID_PREFIX),
          conditions: [],
          logical_operator: 'and'
        })
  }
}
let valid = ref(false)
const props = defineProps<{
  nodeInfo?: any
  nodeId: string
}>()
const UNIQUE_ID_PREFIX = 'key-value-'
const emit = defineEmits(['update:nodeInfo'])
const addTemplateItem = (item) => {
  console.log(props.nodeInfo)
  item.conditions.push({
    comparison_operator: comparisonOperator[item.comparison_operator],
    id: uniqueId(UNIQUE_ID_PREFIX),
    value: '',
    variable_selector: [],
    varType: 'string'
  })

  valid.value = true
}
const handleChangeContext = ($event) => {
  console.log($event)
}
const addchildfile = (item) => {
  console.log(item)
  if (Object.keys(item.sub_variable_condition).length == 0) {
    item.sub_variable_condition = {
      case_id: uniqueId(UNIQUE_ID_PREFIX),
      conditions: [],
      logical_operator: 'and'
    }
  }
  item.sub_variable_condition.conditions.push({
    comparison_operator: '',
    id: uniqueId(UNIQUE_ID_PREFIX),
    key: '',
    value: '',
    varType: 'string'
  })
}
const addcaseItem = () => {
  // const updatedCases = [...props.nodeInfo.cases]
  // updatedCases.push({
  //   case_id: uniqueId(UNIQUE_ID_PREFIX),
  //   conditions: [],
  //   logical_operator: 'and'
  // })
  // console.log(updatedCases)

  // emit('update:nodeInfo', { ...props.nodeInfo, cases: updatedCases })
  // console.log(props.nodeInfo)
  props.nodeInfo.cases.push({
    case_id: uniqueId(UNIQUE_ID_PREFIX),
    conditions: [],
    logical_operator: 'and'
  })
}
const deletefileItem = (item, index) => {
  console.log(item)

  item.sub_variable_condition.conditions.splice(index, 1)
}
const deleteItem = (item, index) => {
  item.conditions.splice(index, 1)
}
const removeItem = (item, index) => {
  props.nodeInfo.cases.splice(index, 1)
  // const updatedCases = [...props.nodeInfo.cases]
  // updatedCases.splice(index, 1)
  // emit('update:nodeInfo', { ...props.nodeInfo, cases: updatedCases })
}
const changelogicalOperator = (item) => {
  console.log(item)

  item.logical_operator = item.logical_operator == 'AND' ? 'OR' : 'AND'
}
const changefilelogicalOperator = (item) => {
  item.sub_variable_condition.logical_operator = item.sub_variable_condition.logical_operator == 'AND' ? 'OR' : 'AND'
}
</script>
<style scoped lang="scss">
.ifbox {
  .case-1 {
    display: flex;
    // align-items: center;
  }

  .case-title {
    width: 40px;

    .first-title {
      font-weight: bold;
    }

    .seconed-title {
      font-size: 12px;
    }
  }

  .right-content {
    width: calc(100% - 40px);
    position: relative;

    .all-btn {
      position: absolute;
      top: 30%;
      transform: translateY(-50%);
      left: -55px;
    }

    .ifcase {
      width: 100%;
      // height: 80px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 10px 0;

      .left {
        width: 90%;
        height: 100%;
        // background: #c8ceda40 ;
      }

      .case-select {
        width: 100%;
        // height: 40px;
        display: flex;
        justify-content: space-between;
      }

      .case-input {
        width: 100%;
        display: flex;
        align-items: center;
        margin-top: 10px;
        // height: 40px;
      }
    }

    .btns {
      width: 100%;
      display: flex;
      justify-content: space-between;
    }
  }
}
</style>
