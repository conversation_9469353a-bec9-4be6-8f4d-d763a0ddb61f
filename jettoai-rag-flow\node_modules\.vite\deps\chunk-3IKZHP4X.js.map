{"version": 3, "sources": ["../../.pnpm/@lezer+common@1.2.3/node_modules/@lezer/common/dist/index.js", "../../.pnpm/@lezer+highlight@1.2.1/node_modules/@lezer/highlight/dist/index.js", "../../.pnpm/@codemirror+language@6.11.0/node_modules/@codemirror/language/dist/index.js"], "sourcesContent": ["/**\nThe default maximum length of a `TreeBuffer` node.\n*/\nconst DefaultBufferLength = 1024;\nlet nextPropID = 0;\nclass Range {\n    constructor(from, to) {\n        this.from = from;\n        this.to = to;\n    }\n}\n/**\nEach [node type](#common.NodeType) or [individual tree](#common.Tree)\ncan have metadata associated with it in props. Instances of this\nclass represent prop names.\n*/\nclass NodeProp {\n    /**\n    Create a new node prop type.\n    */\n    constructor(config = {}) {\n        this.id = nextPropID++;\n        this.perNode = !!config.perNode;\n        this.deserialize = config.deserialize || (() => {\n            throw new Error(\"This node type doesn't define a deserialize function\");\n        });\n    }\n    /**\n    This is meant to be used with\n    [`NodeSet.extend`](#common.NodeSet.extend) or\n    [`LRParser.configure`](#lr.ParserConfig.props) to compute\n    prop values for each node type in the set. Takes a [match\n    object](#common.NodeType^match) or function that returns undefined\n    if the node type doesn't get this prop, and the prop's value if\n    it does.\n    */\n    add(match) {\n        if (this.perNode)\n            throw new RangeError(\"Can't add per-node props to node types\");\n        if (typeof match != \"function\")\n            match = NodeType.match(match);\n        return (type) => {\n            let result = match(type);\n            return result === undefined ? null : [this, result];\n        };\n    }\n}\n/**\nProp that is used to describe matching delimiters. For opening\ndelimiters, this holds an array of node names (written as a\nspace-separated string when declaring this prop in a grammar)\nfor the node types of closing delimiters that match it.\n*/\nNodeProp.closedBy = new NodeProp({ deserialize: str => str.split(\" \") });\n/**\nThe inverse of [`closedBy`](#common.NodeProp^closedBy). This is\nattached to closing delimiters, holding an array of node names\nof types of matching opening delimiters.\n*/\nNodeProp.openedBy = new NodeProp({ deserialize: str => str.split(\" \") });\n/**\nUsed to assign node types to groups (for example, all node\ntypes that represent an expression could be tagged with an\n`\"Expression\"` group).\n*/\nNodeProp.group = new NodeProp({ deserialize: str => str.split(\" \") });\n/**\nAttached to nodes to indicate these should be\n[displayed](https://codemirror.net/docs/ref/#language.syntaxTree)\nin a bidirectional text isolate, so that direction-neutral\ncharacters on their sides don't incorrectly get associated with\nsurrounding text. You'll generally want to set this for nodes\nthat contain arbitrary text, like strings and comments, and for\nnodes that appear _inside_ arbitrary text, like HTML tags. When\nnot given a value, in a grammar declaration, defaults to\n`\"auto\"`.\n*/\nNodeProp.isolate = new NodeProp({ deserialize: value => {\n        if (value && value != \"rtl\" && value != \"ltr\" && value != \"auto\")\n            throw new RangeError(\"Invalid value for isolate: \" + value);\n        return value || \"auto\";\n    } });\n/**\nThe hash of the [context](#lr.ContextTracker.constructor)\nthat the node was parsed in, if any. Used to limit reuse of\ncontextual nodes.\n*/\nNodeProp.contextHash = new NodeProp({ perNode: true });\n/**\nThe distance beyond the end of the node that the tokenizer\nlooked ahead for any of the tokens inside the node. (The LR\nparser only stores this when it is larger than 25, for\nefficiency reasons.)\n*/\nNodeProp.lookAhead = new NodeProp({ perNode: true });\n/**\nThis per-node prop is used to replace a given node, or part of a\nnode, with another tree. This is useful to include trees from\ndifferent languages in mixed-language parsers.\n*/\nNodeProp.mounted = new NodeProp({ perNode: true });\n/**\nA mounted tree, which can be [stored](#common.NodeProp^mounted) on\na tree node to indicate that parts of its content are\nrepresented by another tree.\n*/\nclass MountedTree {\n    constructor(\n    /**\n    The inner tree.\n    */\n    tree, \n    /**\n    If this is null, this tree replaces the entire node (it will\n    be included in the regular iteration instead of its host\n    node). If not, only the given ranges are considered to be\n    covered by this tree. This is used for trees that are mixed in\n    a way that isn't strictly hierarchical. Such mounted trees are\n    only entered by [`resolveInner`](#common.Tree.resolveInner)\n    and [`enter`](#common.SyntaxNode.enter).\n    */\n    overlay, \n    /**\n    The parser used to create this subtree.\n    */\n    parser) {\n        this.tree = tree;\n        this.overlay = overlay;\n        this.parser = parser;\n    }\n    /**\n    @internal\n    */\n    static get(tree) {\n        return tree && tree.props && tree.props[NodeProp.mounted.id];\n    }\n}\nconst noProps = Object.create(null);\n/**\nEach node in a syntax tree has a node type associated with it.\n*/\nclass NodeType {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The name of the node type. Not necessarily unique, but if the\n    grammar was written properly, different node types with the\n    same name within a node set should play the same semantic\n    role.\n    */\n    name, \n    /**\n    @internal\n    */\n    props, \n    /**\n    The id of this node in its set. Corresponds to the term ids\n    used in the parser.\n    */\n    id, \n    /**\n    @internal\n    */\n    flags = 0) {\n        this.name = name;\n        this.props = props;\n        this.id = id;\n        this.flags = flags;\n    }\n    /**\n    Define a node type.\n    */\n    static define(spec) {\n        let props = spec.props && spec.props.length ? Object.create(null) : noProps;\n        let flags = (spec.top ? 1 /* NodeFlag.Top */ : 0) | (spec.skipped ? 2 /* NodeFlag.Skipped */ : 0) |\n            (spec.error ? 4 /* NodeFlag.Error */ : 0) | (spec.name == null ? 8 /* NodeFlag.Anonymous */ : 0);\n        let type = new NodeType(spec.name || \"\", props, spec.id, flags);\n        if (spec.props)\n            for (let src of spec.props) {\n                if (!Array.isArray(src))\n                    src = src(type);\n                if (src) {\n                    if (src[0].perNode)\n                        throw new RangeError(\"Can't store a per-node prop on a node type\");\n                    props[src[0].id] = src[1];\n                }\n            }\n        return type;\n    }\n    /**\n    Retrieves a node prop for this type. Will return `undefined` if\n    the prop isn't present on this node.\n    */\n    prop(prop) { return this.props[prop.id]; }\n    /**\n    True when this is the top node of a grammar.\n    */\n    get isTop() { return (this.flags & 1 /* NodeFlag.Top */) > 0; }\n    /**\n    True when this node is produced by a skip rule.\n    */\n    get isSkipped() { return (this.flags & 2 /* NodeFlag.Skipped */) > 0; }\n    /**\n    Indicates whether this is an error node.\n    */\n    get isError() { return (this.flags & 4 /* NodeFlag.Error */) > 0; }\n    /**\n    When true, this node type doesn't correspond to a user-declared\n    named node, for example because it is used to cache repetition.\n    */\n    get isAnonymous() { return (this.flags & 8 /* NodeFlag.Anonymous */) > 0; }\n    /**\n    Returns true when this node's name or one of its\n    [groups](#common.NodeProp^group) matches the given string.\n    */\n    is(name) {\n        if (typeof name == 'string') {\n            if (this.name == name)\n                return true;\n            let group = this.prop(NodeProp.group);\n            return group ? group.indexOf(name) > -1 : false;\n        }\n        return this.id == name;\n    }\n    /**\n    Create a function from node types to arbitrary values by\n    specifying an object whose property names are node or\n    [group](#common.NodeProp^group) names. Often useful with\n    [`NodeProp.add`](#common.NodeProp.add). You can put multiple\n    names, separated by spaces, in a single property name to map\n    multiple node names to a single value.\n    */\n    static match(map) {\n        let direct = Object.create(null);\n        for (let prop in map)\n            for (let name of prop.split(\" \"))\n                direct[name] = map[prop];\n        return (node) => {\n            for (let groups = node.prop(NodeProp.group), i = -1; i < (groups ? groups.length : 0); i++) {\n                let found = direct[i < 0 ? node.name : groups[i]];\n                if (found)\n                    return found;\n            }\n        };\n    }\n}\n/**\nAn empty dummy node type to use when no actual type is available.\n*/\nNodeType.none = new NodeType(\"\", Object.create(null), 0, 8 /* NodeFlag.Anonymous */);\n/**\nA node set holds a collection of node types. It is used to\ncompactly represent trees by storing their type ids, rather than a\nfull pointer to the type object, in a numeric array. Each parser\n[has](#lr.LRParser.nodeSet) a node set, and [tree\nbuffers](#common.TreeBuffer) can only store collections of nodes\nfrom the same set. A set can have a maximum of 2**16 (65536) node\ntypes in it, so that the ids fit into 16-bit typed array slots.\n*/\nclass NodeSet {\n    /**\n    Create a set with the given types. The `id` property of each\n    type should correspond to its position within the array.\n    */\n    constructor(\n    /**\n    The node types in this set, by id.\n    */\n    types) {\n        this.types = types;\n        for (let i = 0; i < types.length; i++)\n            if (types[i].id != i)\n                throw new RangeError(\"Node type ids should correspond to array positions when creating a node set\");\n    }\n    /**\n    Create a copy of this set with some node properties added. The\n    arguments to this method can be created with\n    [`NodeProp.add`](#common.NodeProp.add).\n    */\n    extend(...props) {\n        let newTypes = [];\n        for (let type of this.types) {\n            let newProps = null;\n            for (let source of props) {\n                let add = source(type);\n                if (add) {\n                    if (!newProps)\n                        newProps = Object.assign({}, type.props);\n                    newProps[add[0].id] = add[1];\n                }\n            }\n            newTypes.push(newProps ? new NodeType(type.name, newProps, type.id, type.flags) : type);\n        }\n        return new NodeSet(newTypes);\n    }\n}\nconst CachedNode = new WeakMap(), CachedInnerNode = new WeakMap();\n/**\nOptions that control iteration. Can be combined with the `|`\noperator to enable multiple ones.\n*/\nvar IterMode;\n(function (IterMode) {\n    /**\n    When enabled, iteration will only visit [`Tree`](#common.Tree)\n    objects, not nodes packed into\n    [`TreeBuffer`](#common.TreeBuffer)s.\n    */\n    IterMode[IterMode[\"ExcludeBuffers\"] = 1] = \"ExcludeBuffers\";\n    /**\n    Enable this to make iteration include anonymous nodes (such as\n    the nodes that wrap repeated grammar constructs into a balanced\n    tree).\n    */\n    IterMode[IterMode[\"IncludeAnonymous\"] = 2] = \"IncludeAnonymous\";\n    /**\n    By default, regular [mounted](#common.NodeProp^mounted) nodes\n    replace their base node in iteration. Enable this to ignore them\n    instead.\n    */\n    IterMode[IterMode[\"IgnoreMounts\"] = 4] = \"IgnoreMounts\";\n    /**\n    This option only applies in\n    [`enter`](#common.SyntaxNode.enter)-style methods. It tells the\n    library to not enter mounted overlays if one covers the given\n    position.\n    */\n    IterMode[IterMode[\"IgnoreOverlays\"] = 8] = \"IgnoreOverlays\";\n})(IterMode || (IterMode = {}));\n/**\nA piece of syntax tree. There are two ways to approach these\ntrees: the way they are actually stored in memory, and the\nconvenient way.\n\nSyntax trees are stored as a tree of `Tree` and `TreeBuffer`\nobjects. By packing detail information into `TreeBuffer` leaf\nnodes, the representation is made a lot more memory-efficient.\n\nHowever, when you want to actually work with tree nodes, this\nrepresentation is very awkward, so most client code will want to\nuse the [`TreeCursor`](#common.TreeCursor) or\n[`SyntaxNode`](#common.SyntaxNode) interface instead, which provides\na view on some part of this data structure, and can be used to\nmove around to adjacent nodes.\n*/\nclass Tree {\n    /**\n    Construct a new tree. See also [`Tree.build`](#common.Tree^build).\n    */\n    constructor(\n    /**\n    The type of the top node.\n    */\n    type, \n    /**\n    This node's child nodes.\n    */\n    children, \n    /**\n    The positions (offsets relative to the start of this tree) of\n    the children.\n    */\n    positions, \n    /**\n    The total length of this tree\n    */\n    length, \n    /**\n    Per-node [node props](#common.NodeProp) to associate with this node.\n    */\n    props) {\n        this.type = type;\n        this.children = children;\n        this.positions = positions;\n        this.length = length;\n        /**\n        @internal\n        */\n        this.props = null;\n        if (props && props.length) {\n            this.props = Object.create(null);\n            for (let [prop, value] of props)\n                this.props[typeof prop == \"number\" ? prop : prop.id] = value;\n        }\n    }\n    /**\n    @internal\n    */\n    toString() {\n        let mounted = MountedTree.get(this);\n        if (mounted && !mounted.overlay)\n            return mounted.tree.toString();\n        let children = \"\";\n        for (let ch of this.children) {\n            let str = ch.toString();\n            if (str) {\n                if (children)\n                    children += \",\";\n                children += str;\n            }\n        }\n        return !this.type.name ? children :\n            (/\\W/.test(this.type.name) && !this.type.isError ? JSON.stringify(this.type.name) : this.type.name) +\n                (children.length ? \"(\" + children + \")\" : \"\");\n    }\n    /**\n    Get a [tree cursor](#common.TreeCursor) positioned at the top of\n    the tree. Mode can be used to [control](#common.IterMode) which\n    nodes the cursor visits.\n    */\n    cursor(mode = 0) {\n        return new TreeCursor(this.topNode, mode);\n    }\n    /**\n    Get a [tree cursor](#common.TreeCursor) pointing into this tree\n    at the given position and side (see\n    [`moveTo`](#common.TreeCursor.moveTo).\n    */\n    cursorAt(pos, side = 0, mode = 0) {\n        let scope = CachedNode.get(this) || this.topNode;\n        let cursor = new TreeCursor(scope);\n        cursor.moveTo(pos, side);\n        CachedNode.set(this, cursor._tree);\n        return cursor;\n    }\n    /**\n    Get a [syntax node](#common.SyntaxNode) object for the top of the\n    tree.\n    */\n    get topNode() {\n        return new TreeNode(this, 0, 0, null);\n    }\n    /**\n    Get the [syntax node](#common.SyntaxNode) at the given position.\n    If `side` is -1, this will move into nodes that end at the\n    position. If 1, it'll move into nodes that start at the\n    position. With 0, it'll only enter nodes that cover the position\n    from both sides.\n    \n    Note that this will not enter\n    [overlays](#common.MountedTree.overlay), and you often want\n    [`resolveInner`](#common.Tree.resolveInner) instead.\n    */\n    resolve(pos, side = 0) {\n        let node = resolveNode(CachedNode.get(this) || this.topNode, pos, side, false);\n        CachedNode.set(this, node);\n        return node;\n    }\n    /**\n    Like [`resolve`](#common.Tree.resolve), but will enter\n    [overlaid](#common.MountedTree.overlay) nodes, producing a syntax node\n    pointing into the innermost overlaid tree at the given position\n    (with parent links going through all parent structure, including\n    the host trees).\n    */\n    resolveInner(pos, side = 0) {\n        let node = resolveNode(CachedInnerNode.get(this) || this.topNode, pos, side, true);\n        CachedInnerNode.set(this, node);\n        return node;\n    }\n    /**\n    In some situations, it can be useful to iterate through all\n    nodes around a position, including those in overlays that don't\n    directly cover the position. This method gives you an iterator\n    that will produce all nodes, from small to big, around the given\n    position.\n    */\n    resolveStack(pos, side = 0) {\n        return stackIterator(this, pos, side);\n    }\n    /**\n    Iterate over the tree and its children, calling `enter` for any\n    node that touches the `from`/`to` region (if given) before\n    running over such a node's children, and `leave` (if given) when\n    leaving the node. When `enter` returns `false`, that node will\n    not have its children iterated over (or `leave` called).\n    */\n    iterate(spec) {\n        let { enter, leave, from = 0, to = this.length } = spec;\n        let mode = spec.mode || 0, anon = (mode & IterMode.IncludeAnonymous) > 0;\n        for (let c = this.cursor(mode | IterMode.IncludeAnonymous);;) {\n            let entered = false;\n            if (c.from <= to && c.to >= from && (!anon && c.type.isAnonymous || enter(c) !== false)) {\n                if (c.firstChild())\n                    continue;\n                entered = true;\n            }\n            for (;;) {\n                if (entered && leave && (anon || !c.type.isAnonymous))\n                    leave(c);\n                if (c.nextSibling())\n                    break;\n                if (!c.parent())\n                    return;\n                entered = true;\n            }\n        }\n    }\n    /**\n    Get the value of the given [node prop](#common.NodeProp) for this\n    node. Works with both per-node and per-type props.\n    */\n    prop(prop) {\n        return !prop.perNode ? this.type.prop(prop) : this.props ? this.props[prop.id] : undefined;\n    }\n    /**\n    Returns the node's [per-node props](#common.NodeProp.perNode) in a\n    format that can be passed to the [`Tree`](#common.Tree)\n    constructor.\n    */\n    get propValues() {\n        let result = [];\n        if (this.props)\n            for (let id in this.props)\n                result.push([+id, this.props[id]]);\n        return result;\n    }\n    /**\n    Balance the direct children of this tree, producing a copy of\n    which may have children grouped into subtrees with type\n    [`NodeType.none`](#common.NodeType^none).\n    */\n    balance(config = {}) {\n        return this.children.length <= 8 /* Balance.BranchFactor */ ? this :\n            balanceRange(NodeType.none, this.children, this.positions, 0, this.children.length, 0, this.length, (children, positions, length) => new Tree(this.type, children, positions, length, this.propValues), config.makeTree || ((children, positions, length) => new Tree(NodeType.none, children, positions, length)));\n    }\n    /**\n    Build a tree from a postfix-ordered buffer of node information,\n    or a cursor over such a buffer.\n    */\n    static build(data) { return buildTree(data); }\n}\n/**\nThe empty tree\n*/\nTree.empty = new Tree(NodeType.none, [], [], 0);\nclass FlatBufferCursor {\n    constructor(buffer, index) {\n        this.buffer = buffer;\n        this.index = index;\n    }\n    get id() { return this.buffer[this.index - 4]; }\n    get start() { return this.buffer[this.index - 3]; }\n    get end() { return this.buffer[this.index - 2]; }\n    get size() { return this.buffer[this.index - 1]; }\n    get pos() { return this.index; }\n    next() { this.index -= 4; }\n    fork() { return new FlatBufferCursor(this.buffer, this.index); }\n}\n/**\nTree buffers contain (type, start, end, endIndex) quads for each\nnode. In such a buffer, nodes are stored in prefix order (parents\nbefore children, with the endIndex of the parent indicating which\nchildren belong to it).\n*/\nclass TreeBuffer {\n    /**\n    Create a tree buffer.\n    */\n    constructor(\n    /**\n    The buffer's content.\n    */\n    buffer, \n    /**\n    The total length of the group of nodes in the buffer.\n    */\n    length, \n    /**\n    The node set used in this buffer.\n    */\n    set) {\n        this.buffer = buffer;\n        this.length = length;\n        this.set = set;\n    }\n    /**\n    @internal\n    */\n    get type() { return NodeType.none; }\n    /**\n    @internal\n    */\n    toString() {\n        let result = [];\n        for (let index = 0; index < this.buffer.length;) {\n            result.push(this.childString(index));\n            index = this.buffer[index + 3];\n        }\n        return result.join(\",\");\n    }\n    /**\n    @internal\n    */\n    childString(index) {\n        let id = this.buffer[index], endIndex = this.buffer[index + 3];\n        let type = this.set.types[id], result = type.name;\n        if (/\\W/.test(result) && !type.isError)\n            result = JSON.stringify(result);\n        index += 4;\n        if (endIndex == index)\n            return result;\n        let children = [];\n        while (index < endIndex) {\n            children.push(this.childString(index));\n            index = this.buffer[index + 3];\n        }\n        return result + \"(\" + children.join(\",\") + \")\";\n    }\n    /**\n    @internal\n    */\n    findChild(startIndex, endIndex, dir, pos, side) {\n        let { buffer } = this, pick = -1;\n        for (let i = startIndex; i != endIndex; i = buffer[i + 3]) {\n            if (checkSide(side, pos, buffer[i + 1], buffer[i + 2])) {\n                pick = i;\n                if (dir > 0)\n                    break;\n            }\n        }\n        return pick;\n    }\n    /**\n    @internal\n    */\n    slice(startI, endI, from) {\n        let b = this.buffer;\n        let copy = new Uint16Array(endI - startI), len = 0;\n        for (let i = startI, j = 0; i < endI;) {\n            copy[j++] = b[i++];\n            copy[j++] = b[i++] - from;\n            let to = copy[j++] = b[i++] - from;\n            copy[j++] = b[i++] - startI;\n            len = Math.max(len, to);\n        }\n        return new TreeBuffer(copy, len, this.set);\n    }\n}\nfunction checkSide(side, pos, from, to) {\n    switch (side) {\n        case -2 /* Side.Before */: return from < pos;\n        case -1 /* Side.AtOrBefore */: return to >= pos && from < pos;\n        case 0 /* Side.Around */: return from < pos && to > pos;\n        case 1 /* Side.AtOrAfter */: return from <= pos && to > pos;\n        case 2 /* Side.After */: return to > pos;\n        case 4 /* Side.DontCare */: return true;\n    }\n}\nfunction resolveNode(node, pos, side, overlays) {\n    var _a;\n    // Move up to a node that actually holds the position, if possible\n    while (node.from == node.to ||\n        (side < 1 ? node.from >= pos : node.from > pos) ||\n        (side > -1 ? node.to <= pos : node.to < pos)) {\n        let parent = !overlays && node instanceof TreeNode && node.index < 0 ? null : node.parent;\n        if (!parent)\n            return node;\n        node = parent;\n    }\n    let mode = overlays ? 0 : IterMode.IgnoreOverlays;\n    // Must go up out of overlays when those do not overlap with pos\n    if (overlays)\n        for (let scan = node, parent = scan.parent; parent; scan = parent, parent = scan.parent) {\n            if (scan instanceof TreeNode && scan.index < 0 && ((_a = parent.enter(pos, side, mode)) === null || _a === void 0 ? void 0 : _a.from) != scan.from)\n                node = parent;\n        }\n    for (;;) {\n        let inner = node.enter(pos, side, mode);\n        if (!inner)\n            return node;\n        node = inner;\n    }\n}\nclass BaseNode {\n    cursor(mode = 0) { return new TreeCursor(this, mode); }\n    getChild(type, before = null, after = null) {\n        let r = getChildren(this, type, before, after);\n        return r.length ? r[0] : null;\n    }\n    getChildren(type, before = null, after = null) {\n        return getChildren(this, type, before, after);\n    }\n    resolve(pos, side = 0) {\n        return resolveNode(this, pos, side, false);\n    }\n    resolveInner(pos, side = 0) {\n        return resolveNode(this, pos, side, true);\n    }\n    matchContext(context) {\n        return matchNodeContext(this.parent, context);\n    }\n    enterUnfinishedNodesBefore(pos) {\n        let scan = this.childBefore(pos), node = this;\n        while (scan) {\n            let last = scan.lastChild;\n            if (!last || last.to != scan.to)\n                break;\n            if (last.type.isError && last.from == last.to) {\n                node = scan;\n                scan = last.prevSibling;\n            }\n            else {\n                scan = last;\n            }\n        }\n        return node;\n    }\n    get node() { return this; }\n    get next() { return this.parent; }\n}\nclass TreeNode extends BaseNode {\n    constructor(_tree, from, \n    // Index in parent node, set to -1 if the node is not a direct child of _parent.node (overlay)\n    index, _parent) {\n        super();\n        this._tree = _tree;\n        this.from = from;\n        this.index = index;\n        this._parent = _parent;\n    }\n    get type() { return this._tree.type; }\n    get name() { return this._tree.type.name; }\n    get to() { return this.from + this._tree.length; }\n    nextChild(i, dir, pos, side, mode = 0) {\n        for (let parent = this;;) {\n            for (let { children, positions } = parent._tree, e = dir > 0 ? children.length : -1; i != e; i += dir) {\n                let next = children[i], start = positions[i] + parent.from;\n                if (!checkSide(side, pos, start, start + next.length))\n                    continue;\n                if (next instanceof TreeBuffer) {\n                    if (mode & IterMode.ExcludeBuffers)\n                        continue;\n                    let index = next.findChild(0, next.buffer.length, dir, pos - start, side);\n                    if (index > -1)\n                        return new BufferNode(new BufferContext(parent, next, i, start), null, index);\n                }\n                else if ((mode & IterMode.IncludeAnonymous) || (!next.type.isAnonymous || hasChild(next))) {\n                    let mounted;\n                    if (!(mode & IterMode.IgnoreMounts) && (mounted = MountedTree.get(next)) && !mounted.overlay)\n                        return new TreeNode(mounted.tree, start, i, parent);\n                    let inner = new TreeNode(next, start, i, parent);\n                    return (mode & IterMode.IncludeAnonymous) || !inner.type.isAnonymous ? inner\n                        : inner.nextChild(dir < 0 ? next.children.length - 1 : 0, dir, pos, side);\n                }\n            }\n            if ((mode & IterMode.IncludeAnonymous) || !parent.type.isAnonymous)\n                return null;\n            if (parent.index >= 0)\n                i = parent.index + dir;\n            else\n                i = dir < 0 ? -1 : parent._parent._tree.children.length;\n            parent = parent._parent;\n            if (!parent)\n                return null;\n        }\n    }\n    get firstChild() { return this.nextChild(0, 1, 0, 4 /* Side.DontCare */); }\n    get lastChild() { return this.nextChild(this._tree.children.length - 1, -1, 0, 4 /* Side.DontCare */); }\n    childAfter(pos) { return this.nextChild(0, 1, pos, 2 /* Side.After */); }\n    childBefore(pos) { return this.nextChild(this._tree.children.length - 1, -1, pos, -2 /* Side.Before */); }\n    enter(pos, side, mode = 0) {\n        let mounted;\n        if (!(mode & IterMode.IgnoreOverlays) && (mounted = MountedTree.get(this._tree)) && mounted.overlay) {\n            let rPos = pos - this.from;\n            for (let { from, to } of mounted.overlay) {\n                if ((side > 0 ? from <= rPos : from < rPos) &&\n                    (side < 0 ? to >= rPos : to > rPos))\n                    return new TreeNode(mounted.tree, mounted.overlay[0].from + this.from, -1, this);\n            }\n        }\n        return this.nextChild(0, 1, pos, side, mode);\n    }\n    nextSignificantParent() {\n        let val = this;\n        while (val.type.isAnonymous && val._parent)\n            val = val._parent;\n        return val;\n    }\n    get parent() {\n        return this._parent ? this._parent.nextSignificantParent() : null;\n    }\n    get nextSibling() {\n        return this._parent && this.index >= 0 ? this._parent.nextChild(this.index + 1, 1, 0, 4 /* Side.DontCare */) : null;\n    }\n    get prevSibling() {\n        return this._parent && this.index >= 0 ? this._parent.nextChild(this.index - 1, -1, 0, 4 /* Side.DontCare */) : null;\n    }\n    get tree() { return this._tree; }\n    toTree() { return this._tree; }\n    /**\n    @internal\n    */\n    toString() { return this._tree.toString(); }\n}\nfunction getChildren(node, type, before, after) {\n    let cur = node.cursor(), result = [];\n    if (!cur.firstChild())\n        return result;\n    if (before != null)\n        for (let found = false; !found;) {\n            found = cur.type.is(before);\n            if (!cur.nextSibling())\n                return result;\n        }\n    for (;;) {\n        if (after != null && cur.type.is(after))\n            return result;\n        if (cur.type.is(type))\n            result.push(cur.node);\n        if (!cur.nextSibling())\n            return after == null ? result : [];\n    }\n}\nfunction matchNodeContext(node, context, i = context.length - 1) {\n    for (let p = node; i >= 0; p = p.parent) {\n        if (!p)\n            return false;\n        if (!p.type.isAnonymous) {\n            if (context[i] && context[i] != p.name)\n                return false;\n            i--;\n        }\n    }\n    return true;\n}\nclass BufferContext {\n    constructor(parent, buffer, index, start) {\n        this.parent = parent;\n        this.buffer = buffer;\n        this.index = index;\n        this.start = start;\n    }\n}\nclass BufferNode extends BaseNode {\n    get name() { return this.type.name; }\n    get from() { return this.context.start + this.context.buffer.buffer[this.index + 1]; }\n    get to() { return this.context.start + this.context.buffer.buffer[this.index + 2]; }\n    constructor(context, _parent, index) {\n        super();\n        this.context = context;\n        this._parent = _parent;\n        this.index = index;\n        this.type = context.buffer.set.types[context.buffer.buffer[index]];\n    }\n    child(dir, pos, side) {\n        let { buffer } = this.context;\n        let index = buffer.findChild(this.index + 4, buffer.buffer[this.index + 3], dir, pos - this.context.start, side);\n        return index < 0 ? null : new BufferNode(this.context, this, index);\n    }\n    get firstChild() { return this.child(1, 0, 4 /* Side.DontCare */); }\n    get lastChild() { return this.child(-1, 0, 4 /* Side.DontCare */); }\n    childAfter(pos) { return this.child(1, pos, 2 /* Side.After */); }\n    childBefore(pos) { return this.child(-1, pos, -2 /* Side.Before */); }\n    enter(pos, side, mode = 0) {\n        if (mode & IterMode.ExcludeBuffers)\n            return null;\n        let { buffer } = this.context;\n        let index = buffer.findChild(this.index + 4, buffer.buffer[this.index + 3], side > 0 ? 1 : -1, pos - this.context.start, side);\n        return index < 0 ? null : new BufferNode(this.context, this, index);\n    }\n    get parent() {\n        return this._parent || this.context.parent.nextSignificantParent();\n    }\n    externalSibling(dir) {\n        return this._parent ? null : this.context.parent.nextChild(this.context.index + dir, dir, 0, 4 /* Side.DontCare */);\n    }\n    get nextSibling() {\n        let { buffer } = this.context;\n        let after = buffer.buffer[this.index + 3];\n        if (after < (this._parent ? buffer.buffer[this._parent.index + 3] : buffer.buffer.length))\n            return new BufferNode(this.context, this._parent, after);\n        return this.externalSibling(1);\n    }\n    get prevSibling() {\n        let { buffer } = this.context;\n        let parentStart = this._parent ? this._parent.index + 4 : 0;\n        if (this.index == parentStart)\n            return this.externalSibling(-1);\n        return new BufferNode(this.context, this._parent, buffer.findChild(parentStart, this.index, -1, 0, 4 /* Side.DontCare */));\n    }\n    get tree() { return null; }\n    toTree() {\n        let children = [], positions = [];\n        let { buffer } = this.context;\n        let startI = this.index + 4, endI = buffer.buffer[this.index + 3];\n        if (endI > startI) {\n            let from = buffer.buffer[this.index + 1];\n            children.push(buffer.slice(startI, endI, from));\n            positions.push(0);\n        }\n        return new Tree(this.type, children, positions, this.to - this.from);\n    }\n    /**\n    @internal\n    */\n    toString() { return this.context.buffer.childString(this.index); }\n}\nfunction iterStack(heads) {\n    if (!heads.length)\n        return null;\n    let pick = 0, picked = heads[0];\n    for (let i = 1; i < heads.length; i++) {\n        let node = heads[i];\n        if (node.from > picked.from || node.to < picked.to) {\n            picked = node;\n            pick = i;\n        }\n    }\n    let next = picked instanceof TreeNode && picked.index < 0 ? null : picked.parent;\n    let newHeads = heads.slice();\n    if (next)\n        newHeads[pick] = next;\n    else\n        newHeads.splice(pick, 1);\n    return new StackIterator(newHeads, picked);\n}\nclass StackIterator {\n    constructor(heads, node) {\n        this.heads = heads;\n        this.node = node;\n    }\n    get next() { return iterStack(this.heads); }\n}\nfunction stackIterator(tree, pos, side) {\n    let inner = tree.resolveInner(pos, side), layers = null;\n    for (let scan = inner instanceof TreeNode ? inner : inner.context.parent; scan; scan = scan.parent) {\n        if (scan.index < 0) { // This is an overlay root\n            let parent = scan.parent;\n            (layers || (layers = [inner])).push(parent.resolve(pos, side));\n            scan = parent;\n        }\n        else {\n            let mount = MountedTree.get(scan.tree);\n            // Relevant overlay branching off\n            if (mount && mount.overlay && mount.overlay[0].from <= pos && mount.overlay[mount.overlay.length - 1].to >= pos) {\n                let root = new TreeNode(mount.tree, mount.overlay[0].from + scan.from, -1, scan);\n                (layers || (layers = [inner])).push(resolveNode(root, pos, side, false));\n            }\n        }\n    }\n    return layers ? iterStack(layers) : inner;\n}\n/**\nA tree cursor object focuses on a given node in a syntax tree, and\nallows you to move to adjacent nodes.\n*/\nclass TreeCursor {\n    /**\n    Shorthand for `.type.name`.\n    */\n    get name() { return this.type.name; }\n    /**\n    @internal\n    */\n    constructor(node, \n    /**\n    @internal\n    */\n    mode = 0) {\n        this.mode = mode;\n        /**\n        @internal\n        */\n        this.buffer = null;\n        this.stack = [];\n        /**\n        @internal\n        */\n        this.index = 0;\n        this.bufferNode = null;\n        if (node instanceof TreeNode) {\n            this.yieldNode(node);\n        }\n        else {\n            this._tree = node.context.parent;\n            this.buffer = node.context;\n            for (let n = node._parent; n; n = n._parent)\n                this.stack.unshift(n.index);\n            this.bufferNode = node;\n            this.yieldBuf(node.index);\n        }\n    }\n    yieldNode(node) {\n        if (!node)\n            return false;\n        this._tree = node;\n        this.type = node.type;\n        this.from = node.from;\n        this.to = node.to;\n        return true;\n    }\n    yieldBuf(index, type) {\n        this.index = index;\n        let { start, buffer } = this.buffer;\n        this.type = type || buffer.set.types[buffer.buffer[index]];\n        this.from = start + buffer.buffer[index + 1];\n        this.to = start + buffer.buffer[index + 2];\n        return true;\n    }\n    /**\n    @internal\n    */\n    yield(node) {\n        if (!node)\n            return false;\n        if (node instanceof TreeNode) {\n            this.buffer = null;\n            return this.yieldNode(node);\n        }\n        this.buffer = node.context;\n        return this.yieldBuf(node.index, node.type);\n    }\n    /**\n    @internal\n    */\n    toString() {\n        return this.buffer ? this.buffer.buffer.childString(this.index) : this._tree.toString();\n    }\n    /**\n    @internal\n    */\n    enterChild(dir, pos, side) {\n        if (!this.buffer)\n            return this.yield(this._tree.nextChild(dir < 0 ? this._tree._tree.children.length - 1 : 0, dir, pos, side, this.mode));\n        let { buffer } = this.buffer;\n        let index = buffer.findChild(this.index + 4, buffer.buffer[this.index + 3], dir, pos - this.buffer.start, side);\n        if (index < 0)\n            return false;\n        this.stack.push(this.index);\n        return this.yieldBuf(index);\n    }\n    /**\n    Move the cursor to this node's first child. When this returns\n    false, the node has no child, and the cursor has not been moved.\n    */\n    firstChild() { return this.enterChild(1, 0, 4 /* Side.DontCare */); }\n    /**\n    Move the cursor to this node's last child.\n    */\n    lastChild() { return this.enterChild(-1, 0, 4 /* Side.DontCare */); }\n    /**\n    Move the cursor to the first child that ends after `pos`.\n    */\n    childAfter(pos) { return this.enterChild(1, pos, 2 /* Side.After */); }\n    /**\n    Move to the last child that starts before `pos`.\n    */\n    childBefore(pos) { return this.enterChild(-1, pos, -2 /* Side.Before */); }\n    /**\n    Move the cursor to the child around `pos`. If side is -1 the\n    child may end at that position, when 1 it may start there. This\n    will also enter [overlaid](#common.MountedTree.overlay)\n    [mounted](#common.NodeProp^mounted) trees unless `overlays` is\n    set to false.\n    */\n    enter(pos, side, mode = this.mode) {\n        if (!this.buffer)\n            return this.yield(this._tree.enter(pos, side, mode));\n        return mode & IterMode.ExcludeBuffers ? false : this.enterChild(1, pos, side);\n    }\n    /**\n    Move to the node's parent node, if this isn't the top node.\n    */\n    parent() {\n        if (!this.buffer)\n            return this.yieldNode((this.mode & IterMode.IncludeAnonymous) ? this._tree._parent : this._tree.parent);\n        if (this.stack.length)\n            return this.yieldBuf(this.stack.pop());\n        let parent = (this.mode & IterMode.IncludeAnonymous) ? this.buffer.parent : this.buffer.parent.nextSignificantParent();\n        this.buffer = null;\n        return this.yieldNode(parent);\n    }\n    /**\n    @internal\n    */\n    sibling(dir) {\n        if (!this.buffer)\n            return !this._tree._parent ? false\n                : this.yield(this._tree.index < 0 ? null\n                    : this._tree._parent.nextChild(this._tree.index + dir, dir, 0, 4 /* Side.DontCare */, this.mode));\n        let { buffer } = this.buffer, d = this.stack.length - 1;\n        if (dir < 0) {\n            let parentStart = d < 0 ? 0 : this.stack[d] + 4;\n            if (this.index != parentStart)\n                return this.yieldBuf(buffer.findChild(parentStart, this.index, -1, 0, 4 /* Side.DontCare */));\n        }\n        else {\n            let after = buffer.buffer[this.index + 3];\n            if (after < (d < 0 ? buffer.buffer.length : buffer.buffer[this.stack[d] + 3]))\n                return this.yieldBuf(after);\n        }\n        return d < 0 ? this.yield(this.buffer.parent.nextChild(this.buffer.index + dir, dir, 0, 4 /* Side.DontCare */, this.mode)) : false;\n    }\n    /**\n    Move to this node's next sibling, if any.\n    */\n    nextSibling() { return this.sibling(1); }\n    /**\n    Move to this node's previous sibling, if any.\n    */\n    prevSibling() { return this.sibling(-1); }\n    atLastNode(dir) {\n        let index, parent, { buffer } = this;\n        if (buffer) {\n            if (dir > 0) {\n                if (this.index < buffer.buffer.buffer.length)\n                    return false;\n            }\n            else {\n                for (let i = 0; i < this.index; i++)\n                    if (buffer.buffer.buffer[i + 3] < this.index)\n                        return false;\n            }\n            ({ index, parent } = buffer);\n        }\n        else {\n            ({ index, _parent: parent } = this._tree);\n        }\n        for (; parent; { index, _parent: parent } = parent) {\n            if (index > -1)\n                for (let i = index + dir, e = dir < 0 ? -1 : parent._tree.children.length; i != e; i += dir) {\n                    let child = parent._tree.children[i];\n                    if ((this.mode & IterMode.IncludeAnonymous) ||\n                        child instanceof TreeBuffer ||\n                        !child.type.isAnonymous ||\n                        hasChild(child))\n                        return false;\n                }\n        }\n        return true;\n    }\n    move(dir, enter) {\n        if (enter && this.enterChild(dir, 0, 4 /* Side.DontCare */))\n            return true;\n        for (;;) {\n            if (this.sibling(dir))\n                return true;\n            if (this.atLastNode(dir) || !this.parent())\n                return false;\n        }\n    }\n    /**\n    Move to the next node in a\n    [pre-order](https://en.wikipedia.org/wiki/Tree_traversal#Pre-order,_NLR)\n    traversal, going from a node to its first child or, if the\n    current node is empty or `enter` is false, its next sibling or\n    the next sibling of the first parent node that has one.\n    */\n    next(enter = true) { return this.move(1, enter); }\n    /**\n    Move to the next node in a last-to-first pre-order traversal. A\n    node is followed by its last child or, if it has none, its\n    previous sibling or the previous sibling of the first parent\n    node that has one.\n    */\n    prev(enter = true) { return this.move(-1, enter); }\n    /**\n    Move the cursor to the innermost node that covers `pos`. If\n    `side` is -1, it will enter nodes that end at `pos`. If it is 1,\n    it will enter nodes that start at `pos`.\n    */\n    moveTo(pos, side = 0) {\n        // Move up to a node that actually holds the position, if possible\n        while (this.from == this.to ||\n            (side < 1 ? this.from >= pos : this.from > pos) ||\n            (side > -1 ? this.to <= pos : this.to < pos))\n            if (!this.parent())\n                break;\n        // Then scan down into child nodes as far as possible\n        while (this.enterChild(1, pos, side)) { }\n        return this;\n    }\n    /**\n    Get a [syntax node](#common.SyntaxNode) at the cursor's current\n    position.\n    */\n    get node() {\n        if (!this.buffer)\n            return this._tree;\n        let cache = this.bufferNode, result = null, depth = 0;\n        if (cache && cache.context == this.buffer) {\n            scan: for (let index = this.index, d = this.stack.length; d >= 0;) {\n                for (let c = cache; c; c = c._parent)\n                    if (c.index == index) {\n                        if (index == this.index)\n                            return c;\n                        result = c;\n                        depth = d + 1;\n                        break scan;\n                    }\n                index = this.stack[--d];\n            }\n        }\n        for (let i = depth; i < this.stack.length; i++)\n            result = new BufferNode(this.buffer, result, this.stack[i]);\n        return this.bufferNode = new BufferNode(this.buffer, result, this.index);\n    }\n    /**\n    Get the [tree](#common.Tree) that represents the current node, if\n    any. Will return null when the node is in a [tree\n    buffer](#common.TreeBuffer).\n    */\n    get tree() {\n        return this.buffer ? null : this._tree._tree;\n    }\n    /**\n    Iterate over the current node and all its descendants, calling\n    `enter` when entering a node and `leave`, if given, when leaving\n    one. When `enter` returns `false`, any children of that node are\n    skipped, and `leave` isn't called for it.\n    */\n    iterate(enter, leave) {\n        for (let depth = 0;;) {\n            let mustLeave = false;\n            if (this.type.isAnonymous || enter(this) !== false) {\n                if (this.firstChild()) {\n                    depth++;\n                    continue;\n                }\n                if (!this.type.isAnonymous)\n                    mustLeave = true;\n            }\n            for (;;) {\n                if (mustLeave && leave)\n                    leave(this);\n                mustLeave = this.type.isAnonymous;\n                if (!depth)\n                    return;\n                if (this.nextSibling())\n                    break;\n                this.parent();\n                depth--;\n                mustLeave = true;\n            }\n        }\n    }\n    /**\n    Test whether the current node matches a given context—a sequence\n    of direct parent node names. Empty strings in the context array\n    are treated as wildcards.\n    */\n    matchContext(context) {\n        if (!this.buffer)\n            return matchNodeContext(this.node.parent, context);\n        let { buffer } = this.buffer, { types } = buffer.set;\n        for (let i = context.length - 1, d = this.stack.length - 1; i >= 0; d--) {\n            if (d < 0)\n                return matchNodeContext(this._tree, context, i);\n            let type = types[buffer.buffer[this.stack[d]]];\n            if (!type.isAnonymous) {\n                if (context[i] && context[i] != type.name)\n                    return false;\n                i--;\n            }\n        }\n        return true;\n    }\n}\nfunction hasChild(tree) {\n    return tree.children.some(ch => ch instanceof TreeBuffer || !ch.type.isAnonymous || hasChild(ch));\n}\nfunction buildTree(data) {\n    var _a;\n    let { buffer, nodeSet, maxBufferLength = DefaultBufferLength, reused = [], minRepeatType = nodeSet.types.length } = data;\n    let cursor = Array.isArray(buffer) ? new FlatBufferCursor(buffer, buffer.length) : buffer;\n    let types = nodeSet.types;\n    let contextHash = 0, lookAhead = 0;\n    function takeNode(parentStart, minPos, children, positions, inRepeat, depth) {\n        let { id, start, end, size } = cursor;\n        let lookAheadAtStart = lookAhead, contextAtStart = contextHash;\n        while (size < 0) {\n            cursor.next();\n            if (size == -1 /* SpecialRecord.Reuse */) {\n                let node = reused[id];\n                children.push(node);\n                positions.push(start - parentStart);\n                return;\n            }\n            else if (size == -3 /* SpecialRecord.ContextChange */) { // Context change\n                contextHash = id;\n                return;\n            }\n            else if (size == -4 /* SpecialRecord.LookAhead */) {\n                lookAhead = id;\n                return;\n            }\n            else {\n                throw new RangeError(`Unrecognized record size: ${size}`);\n            }\n        }\n        let type = types[id], node, buffer;\n        let startPos = start - parentStart;\n        if (end - start <= maxBufferLength && (buffer = findBufferSize(cursor.pos - minPos, inRepeat))) {\n            // Small enough for a buffer, and no reused nodes inside\n            let data = new Uint16Array(buffer.size - buffer.skip);\n            let endPos = cursor.pos - buffer.size, index = data.length;\n            while (cursor.pos > endPos)\n                index = copyToBuffer(buffer.start, data, index);\n            node = new TreeBuffer(data, end - buffer.start, nodeSet);\n            startPos = buffer.start - parentStart;\n        }\n        else { // Make it a node\n            let endPos = cursor.pos - size;\n            cursor.next();\n            let localChildren = [], localPositions = [];\n            let localInRepeat = id >= minRepeatType ? id : -1;\n            let lastGroup = 0, lastEnd = end;\n            while (cursor.pos > endPos) {\n                if (localInRepeat >= 0 && cursor.id == localInRepeat && cursor.size >= 0) {\n                    if (cursor.end <= lastEnd - maxBufferLength) {\n                        makeRepeatLeaf(localChildren, localPositions, start, lastGroup, cursor.end, lastEnd, localInRepeat, lookAheadAtStart, contextAtStart);\n                        lastGroup = localChildren.length;\n                        lastEnd = cursor.end;\n                    }\n                    cursor.next();\n                }\n                else if (depth > 2500 /* CutOff.Depth */) {\n                    takeFlatNode(start, endPos, localChildren, localPositions);\n                }\n                else {\n                    takeNode(start, endPos, localChildren, localPositions, localInRepeat, depth + 1);\n                }\n            }\n            if (localInRepeat >= 0 && lastGroup > 0 && lastGroup < localChildren.length)\n                makeRepeatLeaf(localChildren, localPositions, start, lastGroup, start, lastEnd, localInRepeat, lookAheadAtStart, contextAtStart);\n            localChildren.reverse();\n            localPositions.reverse();\n            if (localInRepeat > -1 && lastGroup > 0) {\n                let make = makeBalanced(type, contextAtStart);\n                node = balanceRange(type, localChildren, localPositions, 0, localChildren.length, 0, end - start, make, make);\n            }\n            else {\n                node = makeTree(type, localChildren, localPositions, end - start, lookAheadAtStart - end, contextAtStart);\n            }\n        }\n        children.push(node);\n        positions.push(startPos);\n    }\n    function takeFlatNode(parentStart, minPos, children, positions) {\n        let nodes = []; // Temporary, inverted array of leaf nodes found, with absolute positions\n        let nodeCount = 0, stopAt = -1;\n        while (cursor.pos > minPos) {\n            let { id, start, end, size } = cursor;\n            if (size > 4) { // Not a leaf\n                cursor.next();\n            }\n            else if (stopAt > -1 && start < stopAt) {\n                break;\n            }\n            else {\n                if (stopAt < 0)\n                    stopAt = end - maxBufferLength;\n                nodes.push(id, start, end);\n                nodeCount++;\n                cursor.next();\n            }\n        }\n        if (nodeCount) {\n            let buffer = new Uint16Array(nodeCount * 4);\n            let start = nodes[nodes.length - 2];\n            for (let i = nodes.length - 3, j = 0; i >= 0; i -= 3) {\n                buffer[j++] = nodes[i];\n                buffer[j++] = nodes[i + 1] - start;\n                buffer[j++] = nodes[i + 2] - start;\n                buffer[j++] = j;\n            }\n            children.push(new TreeBuffer(buffer, nodes[2] - start, nodeSet));\n            positions.push(start - parentStart);\n        }\n    }\n    function makeBalanced(type, contextHash) {\n        return (children, positions, length) => {\n            let lookAhead = 0, lastI = children.length - 1, last, lookAheadProp;\n            if (lastI >= 0 && (last = children[lastI]) instanceof Tree) {\n                if (!lastI && last.type == type && last.length == length)\n                    return last;\n                if (lookAheadProp = last.prop(NodeProp.lookAhead))\n                    lookAhead = positions[lastI] + last.length + lookAheadProp;\n            }\n            return makeTree(type, children, positions, length, lookAhead, contextHash);\n        };\n    }\n    function makeRepeatLeaf(children, positions, base, i, from, to, type, lookAhead, contextHash) {\n        let localChildren = [], localPositions = [];\n        while (children.length > i) {\n            localChildren.push(children.pop());\n            localPositions.push(positions.pop() + base - from);\n        }\n        children.push(makeTree(nodeSet.types[type], localChildren, localPositions, to - from, lookAhead - to, contextHash));\n        positions.push(from - base);\n    }\n    function makeTree(type, children, positions, length, lookAhead, contextHash, props) {\n        if (contextHash) {\n            let pair = [NodeProp.contextHash, contextHash];\n            props = props ? [pair].concat(props) : [pair];\n        }\n        if (lookAhead > 25) {\n            let pair = [NodeProp.lookAhead, lookAhead];\n            props = props ? [pair].concat(props) : [pair];\n        }\n        return new Tree(type, children, positions, length, props);\n    }\n    function findBufferSize(maxSize, inRepeat) {\n        // Scan through the buffer to find previous siblings that fit\n        // together in a TreeBuffer, and don't contain any reused nodes\n        // (which can't be stored in a buffer).\n        // If `inRepeat` is > -1, ignore node boundaries of that type for\n        // nesting, but make sure the end falls either at the start\n        // (`maxSize`) or before such a node.\n        let fork = cursor.fork();\n        let size = 0, start = 0, skip = 0, minStart = fork.end - maxBufferLength;\n        let result = { size: 0, start: 0, skip: 0 };\n        scan: for (let minPos = fork.pos - maxSize; fork.pos > minPos;) {\n            let nodeSize = fork.size;\n            // Pretend nested repeat nodes of the same type don't exist\n            if (fork.id == inRepeat && nodeSize >= 0) {\n                // Except that we store the current state as a valid return\n                // value.\n                result.size = size;\n                result.start = start;\n                result.skip = skip;\n                skip += 4;\n                size += 4;\n                fork.next();\n                continue;\n            }\n            let startPos = fork.pos - nodeSize;\n            if (nodeSize < 0 || startPos < minPos || fork.start < minStart)\n                break;\n            let localSkipped = fork.id >= minRepeatType ? 4 : 0;\n            let nodeStart = fork.start;\n            fork.next();\n            while (fork.pos > startPos) {\n                if (fork.size < 0) {\n                    if (fork.size == -3 /* SpecialRecord.ContextChange */)\n                        localSkipped += 4;\n                    else\n                        break scan;\n                }\n                else if (fork.id >= minRepeatType) {\n                    localSkipped += 4;\n                }\n                fork.next();\n            }\n            start = nodeStart;\n            size += nodeSize;\n            skip += localSkipped;\n        }\n        if (inRepeat < 0 || size == maxSize) {\n            result.size = size;\n            result.start = start;\n            result.skip = skip;\n        }\n        return result.size > 4 ? result : undefined;\n    }\n    function copyToBuffer(bufferStart, buffer, index) {\n        let { id, start, end, size } = cursor;\n        cursor.next();\n        if (size >= 0 && id < minRepeatType) {\n            let startIndex = index;\n            if (size > 4) {\n                let endPos = cursor.pos - (size - 4);\n                while (cursor.pos > endPos)\n                    index = copyToBuffer(bufferStart, buffer, index);\n            }\n            buffer[--index] = startIndex;\n            buffer[--index] = end - bufferStart;\n            buffer[--index] = start - bufferStart;\n            buffer[--index] = id;\n        }\n        else if (size == -3 /* SpecialRecord.ContextChange */) {\n            contextHash = id;\n        }\n        else if (size == -4 /* SpecialRecord.LookAhead */) {\n            lookAhead = id;\n        }\n        return index;\n    }\n    let children = [], positions = [];\n    while (cursor.pos > 0)\n        takeNode(data.start || 0, data.bufferStart || 0, children, positions, -1, 0);\n    let length = (_a = data.length) !== null && _a !== void 0 ? _a : (children.length ? positions[0] + children[0].length : 0);\n    return new Tree(types[data.topID], children.reverse(), positions.reverse(), length);\n}\nconst nodeSizeCache = new WeakMap;\nfunction nodeSize(balanceType, node) {\n    if (!balanceType.isAnonymous || node instanceof TreeBuffer || node.type != balanceType)\n        return 1;\n    let size = nodeSizeCache.get(node);\n    if (size == null) {\n        size = 1;\n        for (let child of node.children) {\n            if (child.type != balanceType || !(child instanceof Tree)) {\n                size = 1;\n                break;\n            }\n            size += nodeSize(balanceType, child);\n        }\n        nodeSizeCache.set(node, size);\n    }\n    return size;\n}\nfunction balanceRange(\n// The type the balanced tree's inner nodes.\nbalanceType, \n// The direct children and their positions\nchildren, positions, \n// The index range in children/positions to use\nfrom, to, \n// The start position of the nodes, relative to their parent.\nstart, \n// Length of the outer node\nlength, \n// Function to build the top node of the balanced tree\nmkTop, \n// Function to build internal nodes for the balanced tree\nmkTree) {\n    let total = 0;\n    for (let i = from; i < to; i++)\n        total += nodeSize(balanceType, children[i]);\n    let maxChild = Math.ceil((total * 1.5) / 8 /* Balance.BranchFactor */);\n    let localChildren = [], localPositions = [];\n    function divide(children, positions, from, to, offset) {\n        for (let i = from; i < to;) {\n            let groupFrom = i, groupStart = positions[i], groupSize = nodeSize(balanceType, children[i]);\n            i++;\n            for (; i < to; i++) {\n                let nextSize = nodeSize(balanceType, children[i]);\n                if (groupSize + nextSize >= maxChild)\n                    break;\n                groupSize += nextSize;\n            }\n            if (i == groupFrom + 1) {\n                if (groupSize > maxChild) {\n                    let only = children[groupFrom]; // Only trees can have a size > 1\n                    divide(only.children, only.positions, 0, only.children.length, positions[groupFrom] + offset);\n                    continue;\n                }\n                localChildren.push(children[groupFrom]);\n            }\n            else {\n                let length = positions[i - 1] + children[i - 1].length - groupStart;\n                localChildren.push(balanceRange(balanceType, children, positions, groupFrom, i, groupStart, length, null, mkTree));\n            }\n            localPositions.push(groupStart + offset - start);\n        }\n    }\n    divide(children, positions, from, to, 0);\n    return (mkTop || mkTree)(localChildren, localPositions, length);\n}\n/**\nProvides a way to associate values with pieces of trees. As long\nas that part of the tree is reused, the associated values can be\nretrieved from an updated tree.\n*/\nclass NodeWeakMap {\n    constructor() {\n        this.map = new WeakMap();\n    }\n    setBuffer(buffer, index, value) {\n        let inner = this.map.get(buffer);\n        if (!inner)\n            this.map.set(buffer, inner = new Map);\n        inner.set(index, value);\n    }\n    getBuffer(buffer, index) {\n        let inner = this.map.get(buffer);\n        return inner && inner.get(index);\n    }\n    /**\n    Set the value for this syntax node.\n    */\n    set(node, value) {\n        if (node instanceof BufferNode)\n            this.setBuffer(node.context.buffer, node.index, value);\n        else if (node instanceof TreeNode)\n            this.map.set(node.tree, value);\n    }\n    /**\n    Retrieve value for this syntax node, if it exists in the map.\n    */\n    get(node) {\n        return node instanceof BufferNode ? this.getBuffer(node.context.buffer, node.index)\n            : node instanceof TreeNode ? this.map.get(node.tree) : undefined;\n    }\n    /**\n    Set the value for the node that a cursor currently points to.\n    */\n    cursorSet(cursor, value) {\n        if (cursor.buffer)\n            this.setBuffer(cursor.buffer.buffer, cursor.index, value);\n        else\n            this.map.set(cursor.tree, value);\n    }\n    /**\n    Retrieve the value for the node that a cursor currently points\n    to.\n    */\n    cursorGet(cursor) {\n        return cursor.buffer ? this.getBuffer(cursor.buffer.buffer, cursor.index) : this.map.get(cursor.tree);\n    }\n}\n\n/**\nTree fragments are used during [incremental\nparsing](#common.Parser.startParse) to track parts of old trees\nthat can be reused in a new parse. An array of fragments is used\nto track regions of an old tree whose nodes might be reused in new\nparses. Use the static\n[`applyChanges`](#common.TreeFragment^applyChanges) method to\nupdate fragments for document changes.\n*/\nclass TreeFragment {\n    /**\n    Construct a tree fragment. You'll usually want to use\n    [`addTree`](#common.TreeFragment^addTree) and\n    [`applyChanges`](#common.TreeFragment^applyChanges) instead of\n    calling this directly.\n    */\n    constructor(\n    /**\n    The start of the unchanged range pointed to by this fragment.\n    This refers to an offset in the _updated_ document (as opposed\n    to the original tree).\n    */\n    from, \n    /**\n    The end of the unchanged range.\n    */\n    to, \n    /**\n    The tree that this fragment is based on.\n    */\n    tree, \n    /**\n    The offset between the fragment's tree and the document that\n    this fragment can be used against. Add this when going from\n    document to tree positions, subtract it to go from tree to\n    document positions.\n    */\n    offset, openStart = false, openEnd = false) {\n        this.from = from;\n        this.to = to;\n        this.tree = tree;\n        this.offset = offset;\n        this.open = (openStart ? 1 /* Open.Start */ : 0) | (openEnd ? 2 /* Open.End */ : 0);\n    }\n    /**\n    Whether the start of the fragment represents the start of a\n    parse, or the end of a change. (In the second case, it may not\n    be safe to reuse some nodes at the start, depending on the\n    parsing algorithm.)\n    */\n    get openStart() { return (this.open & 1 /* Open.Start */) > 0; }\n    /**\n    Whether the end of the fragment represents the end of a\n    full-document parse, or the start of a change.\n    */\n    get openEnd() { return (this.open & 2 /* Open.End */) > 0; }\n    /**\n    Create a set of fragments from a freshly parsed tree, or update\n    an existing set of fragments by replacing the ones that overlap\n    with a tree with content from the new tree. When `partial` is\n    true, the parse is treated as incomplete, and the resulting\n    fragment has [`openEnd`](#common.TreeFragment.openEnd) set to\n    true.\n    */\n    static addTree(tree, fragments = [], partial = false) {\n        let result = [new TreeFragment(0, tree.length, tree, 0, false, partial)];\n        for (let f of fragments)\n            if (f.to > tree.length)\n                result.push(f);\n        return result;\n    }\n    /**\n    Apply a set of edits to an array of fragments, removing or\n    splitting fragments as necessary to remove edited ranges, and\n    adjusting offsets for fragments that moved.\n    */\n    static applyChanges(fragments, changes, minGap = 128) {\n        if (!changes.length)\n            return fragments;\n        let result = [];\n        let fI = 1, nextF = fragments.length ? fragments[0] : null;\n        for (let cI = 0, pos = 0, off = 0;; cI++) {\n            let nextC = cI < changes.length ? changes[cI] : null;\n            let nextPos = nextC ? nextC.fromA : 1e9;\n            if (nextPos - pos >= minGap)\n                while (nextF && nextF.from < nextPos) {\n                    let cut = nextF;\n                    if (pos >= cut.from || nextPos <= cut.to || off) {\n                        let fFrom = Math.max(cut.from, pos) - off, fTo = Math.min(cut.to, nextPos) - off;\n                        cut = fFrom >= fTo ? null : new TreeFragment(fFrom, fTo, cut.tree, cut.offset + off, cI > 0, !!nextC);\n                    }\n                    if (cut)\n                        result.push(cut);\n                    if (nextF.to > nextPos)\n                        break;\n                    nextF = fI < fragments.length ? fragments[fI++] : null;\n                }\n            if (!nextC)\n                break;\n            pos = nextC.toA;\n            off = nextC.toA - nextC.toB;\n        }\n        return result;\n    }\n}\n/**\nA superclass that parsers should extend.\n*/\nclass Parser {\n    /**\n    Start a parse, returning a [partial parse](#common.PartialParse)\n    object. [`fragments`](#common.TreeFragment) can be passed in to\n    make the parse incremental.\n    \n    By default, the entire input is parsed. You can pass `ranges`,\n    which should be a sorted array of non-empty, non-overlapping\n    ranges, to parse only those ranges. The tree returned in that\n    case will start at `ranges[0].from`.\n    */\n    startParse(input, fragments, ranges) {\n        if (typeof input == \"string\")\n            input = new StringInput(input);\n        ranges = !ranges ? [new Range(0, input.length)] : ranges.length ? ranges.map(r => new Range(r.from, r.to)) : [new Range(0, 0)];\n        return this.createParse(input, fragments || [], ranges);\n    }\n    /**\n    Run a full parse, returning the resulting tree.\n    */\n    parse(input, fragments, ranges) {\n        let parse = this.startParse(input, fragments, ranges);\n        for (;;) {\n            let done = parse.advance();\n            if (done)\n                return done;\n        }\n    }\n}\nclass StringInput {\n    constructor(string) {\n        this.string = string;\n    }\n    get length() { return this.string.length; }\n    chunk(from) { return this.string.slice(from); }\n    get lineChunks() { return false; }\n    read(from, to) { return this.string.slice(from, to); }\n}\n\n/**\nCreate a parse wrapper that, after the inner parse completes,\nscans its tree for mixed language regions with the `nest`\nfunction, runs the resulting [inner parses](#common.NestedParse),\nand then [mounts](#common.NodeProp^mounted) their results onto the\ntree.\n*/\nfunction parseMixed(nest) {\n    return (parse, input, fragments, ranges) => new MixedParse(parse, nest, input, fragments, ranges);\n}\nclass InnerParse {\n    constructor(parser, parse, overlay, target, from) {\n        this.parser = parser;\n        this.parse = parse;\n        this.overlay = overlay;\n        this.target = target;\n        this.from = from;\n    }\n}\nfunction checkRanges(ranges) {\n    if (!ranges.length || ranges.some(r => r.from >= r.to))\n        throw new RangeError(\"Invalid inner parse ranges given: \" + JSON.stringify(ranges));\n}\nclass ActiveOverlay {\n    constructor(parser, predicate, mounts, index, start, target, prev) {\n        this.parser = parser;\n        this.predicate = predicate;\n        this.mounts = mounts;\n        this.index = index;\n        this.start = start;\n        this.target = target;\n        this.prev = prev;\n        this.depth = 0;\n        this.ranges = [];\n    }\n}\nconst stoppedInner = new NodeProp({ perNode: true });\nclass MixedParse {\n    constructor(base, nest, input, fragments, ranges) {\n        this.nest = nest;\n        this.input = input;\n        this.fragments = fragments;\n        this.ranges = ranges;\n        this.inner = [];\n        this.innerDone = 0;\n        this.baseTree = null;\n        this.stoppedAt = null;\n        this.baseParse = base;\n    }\n    advance() {\n        if (this.baseParse) {\n            let done = this.baseParse.advance();\n            if (!done)\n                return null;\n            this.baseParse = null;\n            this.baseTree = done;\n            this.startInner();\n            if (this.stoppedAt != null)\n                for (let inner of this.inner)\n                    inner.parse.stopAt(this.stoppedAt);\n        }\n        if (this.innerDone == this.inner.length) {\n            let result = this.baseTree;\n            if (this.stoppedAt != null)\n                result = new Tree(result.type, result.children, result.positions, result.length, result.propValues.concat([[stoppedInner, this.stoppedAt]]));\n            return result;\n        }\n        let inner = this.inner[this.innerDone], done = inner.parse.advance();\n        if (done) {\n            this.innerDone++;\n            // This is a somewhat dodgy but super helpful hack where we\n            // patch up nodes created by the inner parse (and thus\n            // presumably not aliased anywhere else) to hold the information\n            // about the inner parse.\n            let props = Object.assign(Object.create(null), inner.target.props);\n            props[NodeProp.mounted.id] = new MountedTree(done, inner.overlay, inner.parser);\n            inner.target.props = props;\n        }\n        return null;\n    }\n    get parsedPos() {\n        if (this.baseParse)\n            return 0;\n        let pos = this.input.length;\n        for (let i = this.innerDone; i < this.inner.length; i++) {\n            if (this.inner[i].from < pos)\n                pos = Math.min(pos, this.inner[i].parse.parsedPos);\n        }\n        return pos;\n    }\n    stopAt(pos) {\n        this.stoppedAt = pos;\n        if (this.baseParse)\n            this.baseParse.stopAt(pos);\n        else\n            for (let i = this.innerDone; i < this.inner.length; i++)\n                this.inner[i].parse.stopAt(pos);\n    }\n    startInner() {\n        let fragmentCursor = new FragmentCursor(this.fragments);\n        let overlay = null;\n        let covered = null;\n        let cursor = new TreeCursor(new TreeNode(this.baseTree, this.ranges[0].from, 0, null), IterMode.IncludeAnonymous | IterMode.IgnoreMounts);\n        scan: for (let nest, isCovered;;) {\n            let enter = true, range;\n            if (this.stoppedAt != null && cursor.from >= this.stoppedAt) {\n                enter = false;\n            }\n            else if (fragmentCursor.hasNode(cursor)) {\n                if (overlay) {\n                    let match = overlay.mounts.find(m => m.frag.from <= cursor.from && m.frag.to >= cursor.to && m.mount.overlay);\n                    if (match)\n                        for (let r of match.mount.overlay) {\n                            let from = r.from + match.pos, to = r.to + match.pos;\n                            if (from >= cursor.from && to <= cursor.to && !overlay.ranges.some(r => r.from < to && r.to > from))\n                                overlay.ranges.push({ from, to });\n                        }\n                }\n                enter = false;\n            }\n            else if (covered && (isCovered = checkCover(covered.ranges, cursor.from, cursor.to))) {\n                enter = isCovered != 2 /* Cover.Full */;\n            }\n            else if (!cursor.type.isAnonymous && (nest = this.nest(cursor, this.input)) &&\n                (cursor.from < cursor.to || !nest.overlay)) {\n                if (!cursor.tree)\n                    materialize(cursor);\n                let oldMounts = fragmentCursor.findMounts(cursor.from, nest.parser);\n                if (typeof nest.overlay == \"function\") {\n                    overlay = new ActiveOverlay(nest.parser, nest.overlay, oldMounts, this.inner.length, cursor.from, cursor.tree, overlay);\n                }\n                else {\n                    let ranges = punchRanges(this.ranges, nest.overlay ||\n                        (cursor.from < cursor.to ? [new Range(cursor.from, cursor.to)] : []));\n                    if (ranges.length)\n                        checkRanges(ranges);\n                    if (ranges.length || !nest.overlay)\n                        this.inner.push(new InnerParse(nest.parser, ranges.length ? nest.parser.startParse(this.input, enterFragments(oldMounts, ranges), ranges)\n                            : nest.parser.startParse(\"\"), nest.overlay ? nest.overlay.map(r => new Range(r.from - cursor.from, r.to - cursor.from)) : null, cursor.tree, ranges.length ? ranges[0].from : cursor.from));\n                    if (!nest.overlay)\n                        enter = false;\n                    else if (ranges.length)\n                        covered = { ranges, depth: 0, prev: covered };\n                }\n            }\n            else if (overlay && (range = overlay.predicate(cursor))) {\n                if (range === true)\n                    range = new Range(cursor.from, cursor.to);\n                if (range.from < range.to) {\n                    let last = overlay.ranges.length - 1;\n                    if (last >= 0 && overlay.ranges[last].to == range.from)\n                        overlay.ranges[last] = { from: overlay.ranges[last].from, to: range.to };\n                    else\n                        overlay.ranges.push(range);\n                }\n            }\n            if (enter && cursor.firstChild()) {\n                if (overlay)\n                    overlay.depth++;\n                if (covered)\n                    covered.depth++;\n            }\n            else {\n                for (;;) {\n                    if (cursor.nextSibling())\n                        break;\n                    if (!cursor.parent())\n                        break scan;\n                    if (overlay && !--overlay.depth) {\n                        let ranges = punchRanges(this.ranges, overlay.ranges);\n                        if (ranges.length) {\n                            checkRanges(ranges);\n                            this.inner.splice(overlay.index, 0, new InnerParse(overlay.parser, overlay.parser.startParse(this.input, enterFragments(overlay.mounts, ranges), ranges), overlay.ranges.map(r => new Range(r.from - overlay.start, r.to - overlay.start)), overlay.target, ranges[0].from));\n                        }\n                        overlay = overlay.prev;\n                    }\n                    if (covered && !--covered.depth)\n                        covered = covered.prev;\n                }\n            }\n        }\n    }\n}\nfunction checkCover(covered, from, to) {\n    for (let range of covered) {\n        if (range.from >= to)\n            break;\n        if (range.to > from)\n            return range.from <= from && range.to >= to ? 2 /* Cover.Full */ : 1 /* Cover.Partial */;\n    }\n    return 0 /* Cover.None */;\n}\n// Take a piece of buffer and convert it into a stand-alone\n// TreeBuffer.\nfunction sliceBuf(buf, startI, endI, nodes, positions, off) {\n    if (startI < endI) {\n        let from = buf.buffer[startI + 1];\n        nodes.push(buf.slice(startI, endI, from));\n        positions.push(from - off);\n    }\n}\n// This function takes a node that's in a buffer, and converts it, and\n// its parent buffer nodes, into a Tree. This is again acting on the\n// assumption that the trees and buffers have been constructed by the\n// parse that was ran via the mix parser, and thus aren't shared with\n// any other code, making violations of the immutability safe.\nfunction materialize(cursor) {\n    let { node } = cursor, stack = [];\n    let buffer = node.context.buffer;\n    // Scan up to the nearest tree\n    do {\n        stack.push(cursor.index);\n        cursor.parent();\n    } while (!cursor.tree);\n    // Find the index of the buffer in that tree\n    let base = cursor.tree, i = base.children.indexOf(buffer);\n    let buf = base.children[i], b = buf.buffer, newStack = [i];\n    // Split a level in the buffer, putting the nodes before and after\n    // the child that contains `node` into new buffers.\n    function split(startI, endI, type, innerOffset, length, stackPos) {\n        let targetI = stack[stackPos];\n        let children = [], positions = [];\n        sliceBuf(buf, startI, targetI, children, positions, innerOffset);\n        let from = b[targetI + 1], to = b[targetI + 2];\n        newStack.push(children.length);\n        let child = stackPos\n            ? split(targetI + 4, b[targetI + 3], buf.set.types[b[targetI]], from, to - from, stackPos - 1)\n            : node.toTree();\n        children.push(child);\n        positions.push(from - innerOffset);\n        sliceBuf(buf, b[targetI + 3], endI, children, positions, innerOffset);\n        return new Tree(type, children, positions, length);\n    }\n    base.children[i] = split(0, b.length, NodeType.none, 0, buf.length, stack.length - 1);\n    // Move the cursor back to the target node\n    for (let index of newStack) {\n        let tree = cursor.tree.children[index], pos = cursor.tree.positions[index];\n        cursor.yield(new TreeNode(tree, pos + cursor.from, index, cursor._tree));\n    }\n}\nclass StructureCursor {\n    constructor(root, offset) {\n        this.offset = offset;\n        this.done = false;\n        this.cursor = root.cursor(IterMode.IncludeAnonymous | IterMode.IgnoreMounts);\n    }\n    // Move to the first node (in pre-order) that starts at or after `pos`.\n    moveTo(pos) {\n        let { cursor } = this, p = pos - this.offset;\n        while (!this.done && cursor.from < p) {\n            if (cursor.to >= pos && cursor.enter(p, 1, IterMode.IgnoreOverlays | IterMode.ExcludeBuffers)) ;\n            else if (!cursor.next(false))\n                this.done = true;\n        }\n    }\n    hasNode(cursor) {\n        this.moveTo(cursor.from);\n        if (!this.done && this.cursor.from + this.offset == cursor.from && this.cursor.tree) {\n            for (let tree = this.cursor.tree;;) {\n                if (tree == cursor.tree)\n                    return true;\n                if (tree.children.length && tree.positions[0] == 0 && tree.children[0] instanceof Tree)\n                    tree = tree.children[0];\n                else\n                    break;\n            }\n        }\n        return false;\n    }\n}\nclass FragmentCursor {\n    constructor(fragments) {\n        var _a;\n        this.fragments = fragments;\n        this.curTo = 0;\n        this.fragI = 0;\n        if (fragments.length) {\n            let first = this.curFrag = fragments[0];\n            this.curTo = (_a = first.tree.prop(stoppedInner)) !== null && _a !== void 0 ? _a : first.to;\n            this.inner = new StructureCursor(first.tree, -first.offset);\n        }\n        else {\n            this.curFrag = this.inner = null;\n        }\n    }\n    hasNode(node) {\n        while (this.curFrag && node.from >= this.curTo)\n            this.nextFrag();\n        return this.curFrag && this.curFrag.from <= node.from && this.curTo >= node.to && this.inner.hasNode(node);\n    }\n    nextFrag() {\n        var _a;\n        this.fragI++;\n        if (this.fragI == this.fragments.length) {\n            this.curFrag = this.inner = null;\n        }\n        else {\n            let frag = this.curFrag = this.fragments[this.fragI];\n            this.curTo = (_a = frag.tree.prop(stoppedInner)) !== null && _a !== void 0 ? _a : frag.to;\n            this.inner = new StructureCursor(frag.tree, -frag.offset);\n        }\n    }\n    findMounts(pos, parser) {\n        var _a;\n        let result = [];\n        if (this.inner) {\n            this.inner.cursor.moveTo(pos, 1);\n            for (let pos = this.inner.cursor.node; pos; pos = pos.parent) {\n                let mount = (_a = pos.tree) === null || _a === void 0 ? void 0 : _a.prop(NodeProp.mounted);\n                if (mount && mount.parser == parser) {\n                    for (let i = this.fragI; i < this.fragments.length; i++) {\n                        let frag = this.fragments[i];\n                        if (frag.from >= pos.to)\n                            break;\n                        if (frag.tree == this.curFrag.tree)\n                            result.push({\n                                frag,\n                                pos: pos.from - frag.offset,\n                                mount\n                            });\n                    }\n                }\n            }\n        }\n        return result;\n    }\n}\nfunction punchRanges(outer, ranges) {\n    let copy = null, current = ranges;\n    for (let i = 1, j = 0; i < outer.length; i++) {\n        let gapFrom = outer[i - 1].to, gapTo = outer[i].from;\n        for (; j < current.length; j++) {\n            let r = current[j];\n            if (r.from >= gapTo)\n                break;\n            if (r.to <= gapFrom)\n                continue;\n            if (!copy)\n                current = copy = ranges.slice();\n            if (r.from < gapFrom) {\n                copy[j] = new Range(r.from, gapFrom);\n                if (r.to > gapTo)\n                    copy.splice(j + 1, 0, new Range(gapTo, r.to));\n            }\n            else if (r.to > gapTo) {\n                copy[j--] = new Range(gapTo, r.to);\n            }\n            else {\n                copy.splice(j--, 1);\n            }\n        }\n    }\n    return current;\n}\nfunction findCoverChanges(a, b, from, to) {\n    let iA = 0, iB = 0, inA = false, inB = false, pos = -1e9;\n    let result = [];\n    for (;;) {\n        let nextA = iA == a.length ? 1e9 : inA ? a[iA].to : a[iA].from;\n        let nextB = iB == b.length ? 1e9 : inB ? b[iB].to : b[iB].from;\n        if (inA != inB) {\n            let start = Math.max(pos, from), end = Math.min(nextA, nextB, to);\n            if (start < end)\n                result.push(new Range(start, end));\n        }\n        pos = Math.min(nextA, nextB);\n        if (pos == 1e9)\n            break;\n        if (nextA == pos) {\n            if (!inA)\n                inA = true;\n            else {\n                inA = false;\n                iA++;\n            }\n        }\n        if (nextB == pos) {\n            if (!inB)\n                inB = true;\n            else {\n                inB = false;\n                iB++;\n            }\n        }\n    }\n    return result;\n}\n// Given a number of fragments for the outer tree, and a set of ranges\n// to parse, find fragments for inner trees mounted around those\n// ranges, if any.\nfunction enterFragments(mounts, ranges) {\n    let result = [];\n    for (let { pos, mount, frag } of mounts) {\n        let startPos = pos + (mount.overlay ? mount.overlay[0].from : 0), endPos = startPos + mount.tree.length;\n        let from = Math.max(frag.from, startPos), to = Math.min(frag.to, endPos);\n        if (mount.overlay) {\n            let overlay = mount.overlay.map(r => new Range(r.from + pos, r.to + pos));\n            let changes = findCoverChanges(ranges, overlay, from, to);\n            for (let i = 0, pos = from;; i++) {\n                let last = i == changes.length, end = last ? to : changes[i].from;\n                if (end > pos)\n                    result.push(new TreeFragment(pos, end, mount.tree, -startPos, frag.from >= pos || frag.openStart, frag.to <= end || frag.openEnd));\n                if (last)\n                    break;\n                pos = changes[i].to;\n            }\n        }\n        else {\n            result.push(new TreeFragment(from, to, mount.tree, -startPos, frag.from >= startPos || frag.openStart, frag.to <= endPos || frag.openEnd));\n        }\n    }\n    return result;\n}\n\nexport { DefaultBufferLength, IterMode, MountedTree, NodeProp, NodeSet, NodeType, NodeWeakMap, Parser, Tree, TreeBuffer, TreeCursor, TreeFragment, parseMixed };\n", "import { NodeProp } from '@lezer/common';\n\nlet nextTagID = 0;\n/**\nHighlighting tags are markers that denote a highlighting category.\nThey are [associated](#highlight.styleTags) with parts of a syntax\ntree by a language mode, and then mapped to an actual CSS style by\na [highlighter](#highlight.Highlighter).\n\nBecause syntax tree node types and highlight styles have to be\nable to talk the same language, CodeMirror uses a mostly _closed_\n[vocabulary](#highlight.tags) of syntax tags (as opposed to\ntraditional open string-based systems, which make it hard for\nhighlighting themes to cover all the tokens produced by the\nvarious languages).\n\nIt _is_ possible to [define](#highlight.Tag^define) your own\nhighlighting tags for system-internal use (where you control both\nthe language package and the highlighter), but such tags will not\nbe picked up by regular highlighters (though you can derive them\nfrom standard tags to allow highlighters to fall back to those).\n*/\nclass Tag {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The optional name of the base tag @internal\n    */\n    name, \n    /**\n    The set of this tag and all its parent tags, starting with\n    this one itself and sorted in order of decreasing specificity.\n    */\n    set, \n    /**\n    The base unmodified tag that this one is based on, if it's\n    modified @internal\n    */\n    base, \n    /**\n    The modifiers applied to this.base @internal\n    */\n    modified) {\n        this.name = name;\n        this.set = set;\n        this.base = base;\n        this.modified = modified;\n        /**\n        @internal\n        */\n        this.id = nextTagID++;\n    }\n    toString() {\n        let { name } = this;\n        for (let mod of this.modified)\n            if (mod.name)\n                name = `${mod.name}(${name})`;\n        return name;\n    }\n    static define(nameOrParent, parent) {\n        let name = typeof nameOrParent == \"string\" ? nameOrParent : \"?\";\n        if (nameOrParent instanceof Tag)\n            parent = nameOrParent;\n        if (parent === null || parent === void 0 ? void 0 : parent.base)\n            throw new Error(\"Can not derive from a modified tag\");\n        let tag = new Tag(name, [], null, []);\n        tag.set.push(tag);\n        if (parent)\n            for (let t of parent.set)\n                tag.set.push(t);\n        return tag;\n    }\n    /**\n    Define a tag _modifier_, which is a function that, given a tag,\n    will return a tag that is a subtag of the original. Applying the\n    same modifier to a twice tag will return the same value (`m1(t1)\n    == m1(t1)`) and applying multiple modifiers will, regardless or\n    order, produce the same tag (`m1(m2(t1)) == m2(m1(t1))`).\n    \n    When multiple modifiers are applied to a given base tag, each\n    smaller set of modifiers is registered as a parent, so that for\n    example `m1(m2(m3(t1)))` is a subtype of `m1(m2(t1))`,\n    `m1(m3(t1)`, and so on.\n    */\n    static defineModifier(name) {\n        let mod = new Modifier(name);\n        return (tag) => {\n            if (tag.modified.indexOf(mod) > -1)\n                return tag;\n            return Modifier.get(tag.base || tag, tag.modified.concat(mod).sort((a, b) => a.id - b.id));\n        };\n    }\n}\nlet nextModifierID = 0;\nclass Modifier {\n    constructor(name) {\n        this.name = name;\n        this.instances = [];\n        this.id = nextModifierID++;\n    }\n    static get(base, mods) {\n        if (!mods.length)\n            return base;\n        let exists = mods[0].instances.find(t => t.base == base && sameArray(mods, t.modified));\n        if (exists)\n            return exists;\n        let set = [], tag = new Tag(base.name, set, base, mods);\n        for (let m of mods)\n            m.instances.push(tag);\n        let configs = powerSet(mods);\n        for (let parent of base.set)\n            if (!parent.modified.length)\n                for (let config of configs)\n                    set.push(Modifier.get(parent, config));\n        return tag;\n    }\n}\nfunction sameArray(a, b) {\n    return a.length == b.length && a.every((x, i) => x == b[i]);\n}\nfunction powerSet(array) {\n    let sets = [[]];\n    for (let i = 0; i < array.length; i++) {\n        for (let j = 0, e = sets.length; j < e; j++) {\n            sets.push(sets[j].concat(array[i]));\n        }\n    }\n    return sets.sort((a, b) => b.length - a.length);\n}\n/**\nThis function is used to add a set of tags to a language syntax\nvia [`NodeSet.extend`](#common.NodeSet.extend) or\n[`LRParser.configure`](#lr.LRParser.configure).\n\nThe argument object maps node selectors to [highlighting\ntags](#highlight.Tag) or arrays of tags.\n\nNode selectors may hold one or more (space-separated) node paths.\nSuch a path can be a [node name](#common.NodeType.name), or\nmultiple node names (or `*` wildcards) separated by slash\ncharacters, as in `\"Block/Declaration/VariableName\"`. Such a path\nmatches the final node but only if its direct parent nodes are the\nother nodes mentioned. A `*` in such a path matches any parent,\nbut only a single level—wildcards that match multiple parents\naren't supported, both for efficiency reasons and because Lezer\ntrees make it rather hard to reason about what they would match.)\n\nA path can be ended with `/...` to indicate that the tag assigned\nto the node should also apply to all child nodes, even if they\nmatch their own style (by default, only the innermost style is\nused).\n\nWhen a path ends in `!`, as in `Attribute!`, no further matching\nhappens for the node's child nodes, and the entire node gets the\ngiven style.\n\nIn this notation, node names that contain `/`, `!`, `*`, or `...`\nmust be quoted as JSON strings.\n\nFor example:\n\n```javascript\nparser.withProps(\n  styleTags({\n    // Style Number and BigNumber nodes\n    \"Number BigNumber\": tags.number,\n    // Style Escape nodes whose parent is String\n    \"String/Escape\": tags.escape,\n    // Style anything inside Attributes nodes\n    \"Attributes!\": tags.meta,\n    // Add a style to all content inside Italic nodes\n    \"Italic/...\": tags.emphasis,\n    // Style InvalidString nodes as both `string` and `invalid`\n    \"InvalidString\": [tags.string, tags.invalid],\n    // Style the node named \"/\" as punctuation\n    '\"/\"': tags.punctuation\n  })\n)\n```\n*/\nfunction styleTags(spec) {\n    let byName = Object.create(null);\n    for (let prop in spec) {\n        let tags = spec[prop];\n        if (!Array.isArray(tags))\n            tags = [tags];\n        for (let part of prop.split(\" \"))\n            if (part) {\n                let pieces = [], mode = 2 /* Mode.Normal */, rest = part;\n                for (let pos = 0;;) {\n                    if (rest == \"...\" && pos > 0 && pos + 3 == part.length) {\n                        mode = 1 /* Mode.Inherit */;\n                        break;\n                    }\n                    let m = /^\"(?:[^\"\\\\]|\\\\.)*?\"|[^\\/!]+/.exec(rest);\n                    if (!m)\n                        throw new RangeError(\"Invalid path: \" + part);\n                    pieces.push(m[0] == \"*\" ? \"\" : m[0][0] == '\"' ? JSON.parse(m[0]) : m[0]);\n                    pos += m[0].length;\n                    if (pos == part.length)\n                        break;\n                    let next = part[pos++];\n                    if (pos == part.length && next == \"!\") {\n                        mode = 0 /* Mode.Opaque */;\n                        break;\n                    }\n                    if (next != \"/\")\n                        throw new RangeError(\"Invalid path: \" + part);\n                    rest = part.slice(pos);\n                }\n                let last = pieces.length - 1, inner = pieces[last];\n                if (!inner)\n                    throw new RangeError(\"Invalid path: \" + part);\n                let rule = new Rule(tags, mode, last > 0 ? pieces.slice(0, last) : null);\n                byName[inner] = rule.sort(byName[inner]);\n            }\n    }\n    return ruleNodeProp.add(byName);\n}\nconst ruleNodeProp = new NodeProp();\nclass Rule {\n    constructor(tags, mode, context, next) {\n        this.tags = tags;\n        this.mode = mode;\n        this.context = context;\n        this.next = next;\n    }\n    get opaque() { return this.mode == 0 /* Mode.Opaque */; }\n    get inherit() { return this.mode == 1 /* Mode.Inherit */; }\n    sort(other) {\n        if (!other || other.depth < this.depth) {\n            this.next = other;\n            return this;\n        }\n        other.next = this.sort(other.next);\n        return other;\n    }\n    get depth() { return this.context ? this.context.length : 0; }\n}\nRule.empty = new Rule([], 2 /* Mode.Normal */, null);\n/**\nDefine a [highlighter](#highlight.Highlighter) from an array of\ntag/class pairs. Classes associated with more specific tags will\ntake precedence.\n*/\nfunction tagHighlighter(tags, options) {\n    let map = Object.create(null);\n    for (let style of tags) {\n        if (!Array.isArray(style.tag))\n            map[style.tag.id] = style.class;\n        else\n            for (let tag of style.tag)\n                map[tag.id] = style.class;\n    }\n    let { scope, all = null } = options || {};\n    return {\n        style: (tags) => {\n            let cls = all;\n            for (let tag of tags) {\n                for (let sub of tag.set) {\n                    let tagClass = map[sub.id];\n                    if (tagClass) {\n                        cls = cls ? cls + \" \" + tagClass : tagClass;\n                        break;\n                    }\n                }\n            }\n            return cls;\n        },\n        scope\n    };\n}\nfunction highlightTags(highlighters, tags) {\n    let result = null;\n    for (let highlighter of highlighters) {\n        let value = highlighter.style(tags);\n        if (value)\n            result = result ? result + \" \" + value : value;\n    }\n    return result;\n}\n/**\nHighlight the given [tree](#common.Tree) with the given\n[highlighter](#highlight.Highlighter). Often, the higher-level\n[`highlightCode`](#highlight.highlightCode) function is easier to\nuse.\n*/\nfunction highlightTree(tree, highlighter, \n/**\nAssign styling to a region of the text. Will be called, in order\nof position, for any ranges where more than zero classes apply.\n`classes` is a space separated string of CSS classes.\n*/\nputStyle, \n/**\nThe start of the range to highlight.\n*/\nfrom = 0, \n/**\nThe end of the range.\n*/\nto = tree.length) {\n    let builder = new HighlightBuilder(from, Array.isArray(highlighter) ? highlighter : [highlighter], putStyle);\n    builder.highlightRange(tree.cursor(), from, to, \"\", builder.highlighters);\n    builder.flush(to);\n}\n/**\nHighlight the given tree with the given highlighter, calling\n`putText` for every piece of text, either with a set of classes or\nwith the empty string when unstyled, and `putBreak` for every line\nbreak.\n*/\nfunction highlightCode(code, tree, highlighter, putText, putBreak, from = 0, to = code.length) {\n    let pos = from;\n    function writeTo(p, classes) {\n        if (p <= pos)\n            return;\n        for (let text = code.slice(pos, p), i = 0;;) {\n            let nextBreak = text.indexOf(\"\\n\", i);\n            let upto = nextBreak < 0 ? text.length : nextBreak;\n            if (upto > i)\n                putText(text.slice(i, upto), classes);\n            if (nextBreak < 0)\n                break;\n            putBreak();\n            i = nextBreak + 1;\n        }\n        pos = p;\n    }\n    highlightTree(tree, highlighter, (from, to, classes) => {\n        writeTo(from, \"\");\n        writeTo(to, classes);\n    }, from, to);\n    writeTo(to, \"\");\n}\nclass HighlightBuilder {\n    constructor(at, highlighters, span) {\n        this.at = at;\n        this.highlighters = highlighters;\n        this.span = span;\n        this.class = \"\";\n    }\n    startSpan(at, cls) {\n        if (cls != this.class) {\n            this.flush(at);\n            if (at > this.at)\n                this.at = at;\n            this.class = cls;\n        }\n    }\n    flush(to) {\n        if (to > this.at && this.class)\n            this.span(this.at, to, this.class);\n    }\n    highlightRange(cursor, from, to, inheritedClass, highlighters) {\n        let { type, from: start, to: end } = cursor;\n        if (start >= to || end <= from)\n            return;\n        if (type.isTop)\n            highlighters = this.highlighters.filter(h => !h.scope || h.scope(type));\n        let cls = inheritedClass;\n        let rule = getStyleTags(cursor) || Rule.empty;\n        let tagCls = highlightTags(highlighters, rule.tags);\n        if (tagCls) {\n            if (cls)\n                cls += \" \";\n            cls += tagCls;\n            if (rule.mode == 1 /* Mode.Inherit */)\n                inheritedClass += (inheritedClass ? \" \" : \"\") + tagCls;\n        }\n        this.startSpan(Math.max(from, start), cls);\n        if (rule.opaque)\n            return;\n        let mounted = cursor.tree && cursor.tree.prop(NodeProp.mounted);\n        if (mounted && mounted.overlay) {\n            let inner = cursor.node.enter(mounted.overlay[0].from + start, 1);\n            let innerHighlighters = this.highlighters.filter(h => !h.scope || h.scope(mounted.tree.type));\n            let hasChild = cursor.firstChild();\n            for (let i = 0, pos = start;; i++) {\n                let next = i < mounted.overlay.length ? mounted.overlay[i] : null;\n                let nextPos = next ? next.from + start : end;\n                let rangeFrom = Math.max(from, pos), rangeTo = Math.min(to, nextPos);\n                if (rangeFrom < rangeTo && hasChild) {\n                    while (cursor.from < rangeTo) {\n                        this.highlightRange(cursor, rangeFrom, rangeTo, inheritedClass, highlighters);\n                        this.startSpan(Math.min(rangeTo, cursor.to), cls);\n                        if (cursor.to >= nextPos || !cursor.nextSibling())\n                            break;\n                    }\n                }\n                if (!next || nextPos > to)\n                    break;\n                pos = next.to + start;\n                if (pos > from) {\n                    this.highlightRange(inner.cursor(), Math.max(from, next.from + start), Math.min(to, pos), \"\", innerHighlighters);\n                    this.startSpan(Math.min(to, pos), cls);\n                }\n            }\n            if (hasChild)\n                cursor.parent();\n        }\n        else if (cursor.firstChild()) {\n            if (mounted)\n                inheritedClass = \"\";\n            do {\n                if (cursor.to <= from)\n                    continue;\n                if (cursor.from >= to)\n                    break;\n                this.highlightRange(cursor, from, to, inheritedClass, highlighters);\n                this.startSpan(Math.min(to, cursor.to), cls);\n            } while (cursor.nextSibling());\n            cursor.parent();\n        }\n    }\n}\n/**\nMatch a syntax node's [highlight rules](#highlight.styleTags). If\nthere's a match, return its set of tags, and whether it is\nopaque (uses a `!`) or applies to all child nodes (`/...`).\n*/\nfunction getStyleTags(node) {\n    let rule = node.type.prop(ruleNodeProp);\n    while (rule && rule.context && !node.matchContext(rule.context))\n        rule = rule.next;\n    return rule || null;\n}\nconst t = Tag.define;\nconst comment = t(), name = t(), typeName = t(name), propertyName = t(name), literal = t(), string = t(literal), number = t(literal), content = t(), heading = t(content), keyword = t(), operator = t(), punctuation = t(), bracket = t(punctuation), meta = t();\n/**\nThe default set of highlighting [tags](#highlight.Tag).\n\nThis collection is heavily biased towards programming languages,\nand necessarily incomplete. A full ontology of syntactic\nconstructs would fill a stack of books, and be impractical to\nwrite themes for. So try to make do with this set. If all else\nfails, [open an\nissue](https://github.com/codemirror/codemirror.next) to propose a\nnew tag, or [define](#highlight.Tag^define) a local custom tag for\nyour use case.\n\nNote that it is not obligatory to always attach the most specific\ntag possible to an element—if your grammar can't easily\ndistinguish a certain type of element (such as a local variable),\nit is okay to style it as its more general variant (a variable).\n\nFor tags that extend some parent tag, the documentation links to\nthe parent.\n*/\nconst tags = {\n    /**\n    A comment.\n    */\n    comment,\n    /**\n    A line [comment](#highlight.tags.comment).\n    */\n    lineComment: t(comment),\n    /**\n    A block [comment](#highlight.tags.comment).\n    */\n    blockComment: t(comment),\n    /**\n    A documentation [comment](#highlight.tags.comment).\n    */\n    docComment: t(comment),\n    /**\n    Any kind of identifier.\n    */\n    name,\n    /**\n    The [name](#highlight.tags.name) of a variable.\n    */\n    variableName: t(name),\n    /**\n    A type [name](#highlight.tags.name).\n    */\n    typeName: typeName,\n    /**\n    A tag name (subtag of [`typeName`](#highlight.tags.typeName)).\n    */\n    tagName: t(typeName),\n    /**\n    A property or field [name](#highlight.tags.name).\n    */\n    propertyName: propertyName,\n    /**\n    An attribute name (subtag of [`propertyName`](#highlight.tags.propertyName)).\n    */\n    attributeName: t(propertyName),\n    /**\n    The [name](#highlight.tags.name) of a class.\n    */\n    className: t(name),\n    /**\n    A label [name](#highlight.tags.name).\n    */\n    labelName: t(name),\n    /**\n    A namespace [name](#highlight.tags.name).\n    */\n    namespace: t(name),\n    /**\n    The [name](#highlight.tags.name) of a macro.\n    */\n    macroName: t(name),\n    /**\n    A literal value.\n    */\n    literal,\n    /**\n    A string [literal](#highlight.tags.literal).\n    */\n    string,\n    /**\n    A documentation [string](#highlight.tags.string).\n    */\n    docString: t(string),\n    /**\n    A character literal (subtag of [string](#highlight.tags.string)).\n    */\n    character: t(string),\n    /**\n    An attribute value (subtag of [string](#highlight.tags.string)).\n    */\n    attributeValue: t(string),\n    /**\n    A number [literal](#highlight.tags.literal).\n    */\n    number,\n    /**\n    An integer [number](#highlight.tags.number) literal.\n    */\n    integer: t(number),\n    /**\n    A floating-point [number](#highlight.tags.number) literal.\n    */\n    float: t(number),\n    /**\n    A boolean [literal](#highlight.tags.literal).\n    */\n    bool: t(literal),\n    /**\n    Regular expression [literal](#highlight.tags.literal).\n    */\n    regexp: t(literal),\n    /**\n    An escape [literal](#highlight.tags.literal), for example a\n    backslash escape in a string.\n    */\n    escape: t(literal),\n    /**\n    A color [literal](#highlight.tags.literal).\n    */\n    color: t(literal),\n    /**\n    A URL [literal](#highlight.tags.literal).\n    */\n    url: t(literal),\n    /**\n    A language keyword.\n    */\n    keyword,\n    /**\n    The [keyword](#highlight.tags.keyword) for the self or this\n    object.\n    */\n    self: t(keyword),\n    /**\n    The [keyword](#highlight.tags.keyword) for null.\n    */\n    null: t(keyword),\n    /**\n    A [keyword](#highlight.tags.keyword) denoting some atomic value.\n    */\n    atom: t(keyword),\n    /**\n    A [keyword](#highlight.tags.keyword) that represents a unit.\n    */\n    unit: t(keyword),\n    /**\n    A modifier [keyword](#highlight.tags.keyword).\n    */\n    modifier: t(keyword),\n    /**\n    A [keyword](#highlight.tags.keyword) that acts as an operator.\n    */\n    operatorKeyword: t(keyword),\n    /**\n    A control-flow related [keyword](#highlight.tags.keyword).\n    */\n    controlKeyword: t(keyword),\n    /**\n    A [keyword](#highlight.tags.keyword) that defines something.\n    */\n    definitionKeyword: t(keyword),\n    /**\n    A [keyword](#highlight.tags.keyword) related to defining or\n    interfacing with modules.\n    */\n    moduleKeyword: t(keyword),\n    /**\n    An operator.\n    */\n    operator,\n    /**\n    An [operator](#highlight.tags.operator) that dereferences something.\n    */\n    derefOperator: t(operator),\n    /**\n    Arithmetic-related [operator](#highlight.tags.operator).\n    */\n    arithmeticOperator: t(operator),\n    /**\n    Logical [operator](#highlight.tags.operator).\n    */\n    logicOperator: t(operator),\n    /**\n    Bit [operator](#highlight.tags.operator).\n    */\n    bitwiseOperator: t(operator),\n    /**\n    Comparison [operator](#highlight.tags.operator).\n    */\n    compareOperator: t(operator),\n    /**\n    [Operator](#highlight.tags.operator) that updates its operand.\n    */\n    updateOperator: t(operator),\n    /**\n    [Operator](#highlight.tags.operator) that defines something.\n    */\n    definitionOperator: t(operator),\n    /**\n    Type-related [operator](#highlight.tags.operator).\n    */\n    typeOperator: t(operator),\n    /**\n    Control-flow [operator](#highlight.tags.operator).\n    */\n    controlOperator: t(operator),\n    /**\n    Program or markup punctuation.\n    */\n    punctuation,\n    /**\n    [Punctuation](#highlight.tags.punctuation) that separates\n    things.\n    */\n    separator: t(punctuation),\n    /**\n    Bracket-style [punctuation](#highlight.tags.punctuation).\n    */\n    bracket,\n    /**\n    Angle [brackets](#highlight.tags.bracket) (usually `<` and `>`\n    tokens).\n    */\n    angleBracket: t(bracket),\n    /**\n    Square [brackets](#highlight.tags.bracket) (usually `[` and `]`\n    tokens).\n    */\n    squareBracket: t(bracket),\n    /**\n    Parentheses (usually `(` and `)` tokens). Subtag of\n    [bracket](#highlight.tags.bracket).\n    */\n    paren: t(bracket),\n    /**\n    Braces (usually `{` and `}` tokens). Subtag of\n    [bracket](#highlight.tags.bracket).\n    */\n    brace: t(bracket),\n    /**\n    Content, for example plain text in XML or markup documents.\n    */\n    content,\n    /**\n    [Content](#highlight.tags.content) that represents a heading.\n    */\n    heading,\n    /**\n    A level 1 [heading](#highlight.tags.heading).\n    */\n    heading1: t(heading),\n    /**\n    A level 2 [heading](#highlight.tags.heading).\n    */\n    heading2: t(heading),\n    /**\n    A level 3 [heading](#highlight.tags.heading).\n    */\n    heading3: t(heading),\n    /**\n    A level 4 [heading](#highlight.tags.heading).\n    */\n    heading4: t(heading),\n    /**\n    A level 5 [heading](#highlight.tags.heading).\n    */\n    heading5: t(heading),\n    /**\n    A level 6 [heading](#highlight.tags.heading).\n    */\n    heading6: t(heading),\n    /**\n    A prose [content](#highlight.tags.content) separator (such as a horizontal rule).\n    */\n    contentSeparator: t(content),\n    /**\n    [Content](#highlight.tags.content) that represents a list.\n    */\n    list: t(content),\n    /**\n    [Content](#highlight.tags.content) that represents a quote.\n    */\n    quote: t(content),\n    /**\n    [Content](#highlight.tags.content) that is emphasized.\n    */\n    emphasis: t(content),\n    /**\n    [Content](#highlight.tags.content) that is styled strong.\n    */\n    strong: t(content),\n    /**\n    [Content](#highlight.tags.content) that is part of a link.\n    */\n    link: t(content),\n    /**\n    [Content](#highlight.tags.content) that is styled as code or\n    monospace.\n    */\n    monospace: t(content),\n    /**\n    [Content](#highlight.tags.content) that has a strike-through\n    style.\n    */\n    strikethrough: t(content),\n    /**\n    Inserted text in a change-tracking format.\n    */\n    inserted: t(),\n    /**\n    Deleted text.\n    */\n    deleted: t(),\n    /**\n    Changed text.\n    */\n    changed: t(),\n    /**\n    An invalid or unsyntactic element.\n    */\n    invalid: t(),\n    /**\n    Metadata or meta-instruction.\n    */\n    meta,\n    /**\n    [Metadata](#highlight.tags.meta) that applies to the entire\n    document.\n    */\n    documentMeta: t(meta),\n    /**\n    [Metadata](#highlight.tags.meta) that annotates or adds\n    attributes to a given syntactic element.\n    */\n    annotation: t(meta),\n    /**\n    Processing instruction or preprocessor directive. Subtag of\n    [meta](#highlight.tags.meta).\n    */\n    processingInstruction: t(meta),\n    /**\n    [Modifier](#highlight.Tag^defineModifier) that indicates that a\n    given element is being defined. Expected to be used with the\n    various [name](#highlight.tags.name) tags.\n    */\n    definition: Tag.defineModifier(\"definition\"),\n    /**\n    [Modifier](#highlight.Tag^defineModifier) that indicates that\n    something is constant. Mostly expected to be used with\n    [variable names](#highlight.tags.variableName).\n    */\n    constant: Tag.defineModifier(\"constant\"),\n    /**\n    [Modifier](#highlight.Tag^defineModifier) used to indicate that\n    a [variable](#highlight.tags.variableName) or [property\n    name](#highlight.tags.propertyName) is being called or defined\n    as a function.\n    */\n    function: Tag.defineModifier(\"function\"),\n    /**\n    [Modifier](#highlight.Tag^defineModifier) that can be applied to\n    [names](#highlight.tags.name) to indicate that they belong to\n    the language's standard environment.\n    */\n    standard: Tag.defineModifier(\"standard\"),\n    /**\n    [Modifier](#highlight.Tag^defineModifier) that indicates a given\n    [names](#highlight.tags.name) is local to some scope.\n    */\n    local: Tag.defineModifier(\"local\"),\n    /**\n    A generic variant [modifier](#highlight.Tag^defineModifier) that\n    can be used to tag language-specific alternative variants of\n    some common tag. It is recommended for themes to define special\n    forms of at least the [string](#highlight.tags.string) and\n    [variable name](#highlight.tags.variableName) tags, since those\n    come up a lot.\n    */\n    special: Tag.defineModifier(\"special\")\n};\nfor (let name in tags) {\n    let val = tags[name];\n    if (val instanceof Tag)\n        val.name = name;\n}\n/**\nThis is a highlighter that adds stable, predictable classes to\ntokens, for styling with external CSS.\n\nThe following tags are mapped to their name prefixed with `\"tok-\"`\n(for example `\"tok-comment\"`):\n\n* [`link`](#highlight.tags.link)\n* [`heading`](#highlight.tags.heading)\n* [`emphasis`](#highlight.tags.emphasis)\n* [`strong`](#highlight.tags.strong)\n* [`keyword`](#highlight.tags.keyword)\n* [`atom`](#highlight.tags.atom)\n* [`bool`](#highlight.tags.bool)\n* [`url`](#highlight.tags.url)\n* [`labelName`](#highlight.tags.labelName)\n* [`inserted`](#highlight.tags.inserted)\n* [`deleted`](#highlight.tags.deleted)\n* [`literal`](#highlight.tags.literal)\n* [`string`](#highlight.tags.string)\n* [`number`](#highlight.tags.number)\n* [`variableName`](#highlight.tags.variableName)\n* [`typeName`](#highlight.tags.typeName)\n* [`namespace`](#highlight.tags.namespace)\n* [`className`](#highlight.tags.className)\n* [`macroName`](#highlight.tags.macroName)\n* [`propertyName`](#highlight.tags.propertyName)\n* [`operator`](#highlight.tags.operator)\n* [`comment`](#highlight.tags.comment)\n* [`meta`](#highlight.tags.meta)\n* [`punctuation`](#highlight.tags.punctuation)\n* [`invalid`](#highlight.tags.invalid)\n\nIn addition, these mappings are provided:\n\n* [`regexp`](#highlight.tags.regexp),\n  [`escape`](#highlight.tags.escape), and\n  [`special`](#highlight.tags.special)[`(string)`](#highlight.tags.string)\n  are mapped to `\"tok-string2\"`\n* [`special`](#highlight.tags.special)[`(variableName)`](#highlight.tags.variableName)\n  to `\"tok-variableName2\"`\n* [`local`](#highlight.tags.local)[`(variableName)`](#highlight.tags.variableName)\n  to `\"tok-variableName tok-local\"`\n* [`definition`](#highlight.tags.definition)[`(variableName)`](#highlight.tags.variableName)\n  to `\"tok-variableName tok-definition\"`\n* [`definition`](#highlight.tags.definition)[`(propertyName)`](#highlight.tags.propertyName)\n  to `\"tok-propertyName tok-definition\"`\n*/\nconst classHighlighter = tagHighlighter([\n    { tag: tags.link, class: \"tok-link\" },\n    { tag: tags.heading, class: \"tok-heading\" },\n    { tag: tags.emphasis, class: \"tok-emphasis\" },\n    { tag: tags.strong, class: \"tok-strong\" },\n    { tag: tags.keyword, class: \"tok-keyword\" },\n    { tag: tags.atom, class: \"tok-atom\" },\n    { tag: tags.bool, class: \"tok-bool\" },\n    { tag: tags.url, class: \"tok-url\" },\n    { tag: tags.labelName, class: \"tok-labelName\" },\n    { tag: tags.inserted, class: \"tok-inserted\" },\n    { tag: tags.deleted, class: \"tok-deleted\" },\n    { tag: tags.literal, class: \"tok-literal\" },\n    { tag: tags.string, class: \"tok-string\" },\n    { tag: tags.number, class: \"tok-number\" },\n    { tag: [tags.regexp, tags.escape, tags.special(tags.string)], class: \"tok-string2\" },\n    { tag: tags.variableName, class: \"tok-variableName\" },\n    { tag: tags.local(tags.variableName), class: \"tok-variableName tok-local\" },\n    { tag: tags.definition(tags.variableName), class: \"tok-variableName tok-definition\" },\n    { tag: tags.special(tags.variableName), class: \"tok-variableName2\" },\n    { tag: tags.definition(tags.propertyName), class: \"tok-propertyName tok-definition\" },\n    { tag: tags.typeName, class: \"tok-typeName\" },\n    { tag: tags.namespace, class: \"tok-namespace\" },\n    { tag: tags.className, class: \"tok-className\" },\n    { tag: tags.macroName, class: \"tok-macroName\" },\n    { tag: tags.propertyName, class: \"tok-propertyName\" },\n    { tag: tags.operator, class: \"tok-operator\" },\n    { tag: tags.comment, class: \"tok-comment\" },\n    { tag: tags.meta, class: \"tok-meta\" },\n    { tag: tags.invalid, class: \"tok-invalid\" },\n    { tag: tags.punctuation, class: \"tok-punctuation\" }\n]);\n\nexport { Tag, classHighlighter, getStyleTags, highlightCode, highlightTree, styleTags, tagHighlighter, tags };\n", "import { NodeProp, IterMode, Tree, TreeFragment, Parser, NodeType, NodeSet } from '@lezer/common';\nimport { StateEffect, StateField, Facet, EditorState, countColumn, combineConfig, RangeSet, RangeSetBuilder, Prec } from '@codemirror/state';\nimport { ViewPlugin, logException, EditorView, Decoration, WidgetType, gutter, GutterMarker, Direction } from '@codemirror/view';\nimport { tags, tagHighlighter, highlightTree, styleTags } from '@lezer/highlight';\nimport { StyleModule } from 'style-mod';\n\nvar _a;\n/**\nNode prop stored in a parser's top syntax node to provide the\nfacet that stores language-specific data for that language.\n*/\nconst languageDataProp = /*@__PURE__*/new NodeProp();\n/**\nHelper function to define a facet (to be added to the top syntax\nnode(s) for a language via\n[`languageDataProp`](https://codemirror.net/6/docs/ref/#language.languageDataProp)), that will be\nused to associate language data with the language. You\nprobably only need this when subclassing\n[`Language`](https://codemirror.net/6/docs/ref/#language.Language).\n*/\nfunction defineLanguageFacet(baseData) {\n    return Facet.define({\n        combine: baseData ? values => values.concat(baseData) : undefined\n    });\n}\n/**\nSyntax node prop used to register sublanguages. Should be added to\nthe top level node type for the language.\n*/\nconst sublanguageProp = /*@__PURE__*/new NodeProp();\n/**\nA language object manages parsing and per-language\n[metadata](https://codemirror.net/6/docs/ref/#state.EditorState.languageDataAt). Parse data is\nmanaged as a [Lezer](https://lezer.codemirror.net) tree. The class\ncan be used directly, via the [`LRLanguage`](https://codemirror.net/6/docs/ref/#language.LRLanguage)\nsubclass for [Lezer](https://lezer.codemirror.net/) LR parsers, or\nvia the [`StreamLanguage`](https://codemirror.net/6/docs/ref/#language.StreamLanguage) subclass\nfor stream parsers.\n*/\nclass Language {\n    /**\n    Construct a language object. If you need to invoke this\n    directly, first define a data facet with\n    [`defineLanguageFacet`](https://codemirror.net/6/docs/ref/#language.defineLanguageFacet), and then\n    configure your parser to [attach](https://codemirror.net/6/docs/ref/#language.languageDataProp) it\n    to the language's outer syntax node.\n    */\n    constructor(\n    /**\n    The [language data](https://codemirror.net/6/docs/ref/#state.EditorState.languageDataAt) facet\n    used for this language.\n    */\n    data, parser, extraExtensions = [], \n    /**\n    A language name.\n    */\n    name = \"\") {\n        this.data = data;\n        this.name = name;\n        // Kludge to define EditorState.tree as a debugging helper,\n        // without the EditorState package actually knowing about\n        // languages and lezer trees.\n        if (!EditorState.prototype.hasOwnProperty(\"tree\"))\n            Object.defineProperty(EditorState.prototype, \"tree\", { get() { return syntaxTree(this); } });\n        this.parser = parser;\n        this.extension = [\n            language.of(this),\n            EditorState.languageData.of((state, pos, side) => {\n                let top = topNodeAt(state, pos, side), data = top.type.prop(languageDataProp);\n                if (!data)\n                    return [];\n                let base = state.facet(data), sub = top.type.prop(sublanguageProp);\n                if (sub) {\n                    let innerNode = top.resolve(pos - top.from, side);\n                    for (let sublang of sub)\n                        if (sublang.test(innerNode, state)) {\n                            let data = state.facet(sublang.facet);\n                            return sublang.type == \"replace\" ? data : data.concat(base);\n                        }\n                }\n                return base;\n            })\n        ].concat(extraExtensions);\n    }\n    /**\n    Query whether this language is active at the given position.\n    */\n    isActiveAt(state, pos, side = -1) {\n        return topNodeAt(state, pos, side).type.prop(languageDataProp) == this.data;\n    }\n    /**\n    Find the document regions that were parsed using this language.\n    The returned regions will _include_ any nested languages rooted\n    in this language, when those exist.\n    */\n    findRegions(state) {\n        let lang = state.facet(language);\n        if ((lang === null || lang === void 0 ? void 0 : lang.data) == this.data)\n            return [{ from: 0, to: state.doc.length }];\n        if (!lang || !lang.allowsNesting)\n            return [];\n        let result = [];\n        let explore = (tree, from) => {\n            if (tree.prop(languageDataProp) == this.data) {\n                result.push({ from, to: from + tree.length });\n                return;\n            }\n            let mount = tree.prop(NodeProp.mounted);\n            if (mount) {\n                if (mount.tree.prop(languageDataProp) == this.data) {\n                    if (mount.overlay)\n                        for (let r of mount.overlay)\n                            result.push({ from: r.from + from, to: r.to + from });\n                    else\n                        result.push({ from: from, to: from + tree.length });\n                    return;\n                }\n                else if (mount.overlay) {\n                    let size = result.length;\n                    explore(mount.tree, mount.overlay[0].from + from);\n                    if (result.length > size)\n                        return;\n                }\n            }\n            for (let i = 0; i < tree.children.length; i++) {\n                let ch = tree.children[i];\n                if (ch instanceof Tree)\n                    explore(ch, tree.positions[i] + from);\n            }\n        };\n        explore(syntaxTree(state), 0);\n        return result;\n    }\n    /**\n    Indicates whether this language allows nested languages. The\n    default implementation returns true.\n    */\n    get allowsNesting() { return true; }\n}\n/**\n@internal\n*/\nLanguage.setState = /*@__PURE__*/StateEffect.define();\nfunction topNodeAt(state, pos, side) {\n    let topLang = state.facet(language), tree = syntaxTree(state).topNode;\n    if (!topLang || topLang.allowsNesting) {\n        for (let node = tree; node; node = node.enter(pos, side, IterMode.ExcludeBuffers))\n            if (node.type.isTop)\n                tree = node;\n    }\n    return tree;\n}\n/**\nA subclass of [`Language`](https://codemirror.net/6/docs/ref/#language.Language) for use with Lezer\n[LR parsers](https://lezer.codemirror.net/docs/ref#lr.LRParser)\nparsers.\n*/\nclass LRLanguage extends Language {\n    constructor(data, parser, name) {\n        super(data, parser, [], name);\n        this.parser = parser;\n    }\n    /**\n    Define a language from a parser.\n    */\n    static define(spec) {\n        let data = defineLanguageFacet(spec.languageData);\n        return new LRLanguage(data, spec.parser.configure({\n            props: [languageDataProp.add(type => type.isTop ? data : undefined)]\n        }), spec.name);\n    }\n    /**\n    Create a new instance of this language with a reconfigured\n    version of its parser and optionally a new name.\n    */\n    configure(options, name) {\n        return new LRLanguage(this.data, this.parser.configure(options), name || this.name);\n    }\n    get allowsNesting() { return this.parser.hasWrappers(); }\n}\n/**\nGet the syntax tree for a state, which is the current (possibly\nincomplete) parse tree of the active\n[language](https://codemirror.net/6/docs/ref/#language.Language), or the empty tree if there is no\nlanguage available.\n*/\nfunction syntaxTree(state) {\n    let field = state.field(Language.state, false);\n    return field ? field.tree : Tree.empty;\n}\n/**\nTry to get a parse tree that spans at least up to `upto`. The\nmethod will do at most `timeout` milliseconds of work to parse\nup to that point if the tree isn't already available.\n*/\nfunction ensureSyntaxTree(state, upto, timeout = 50) {\n    var _a;\n    let parse = (_a = state.field(Language.state, false)) === null || _a === void 0 ? void 0 : _a.context;\n    if (!parse)\n        return null;\n    let oldVieport = parse.viewport;\n    parse.updateViewport({ from: 0, to: upto });\n    let result = parse.isDone(upto) || parse.work(timeout, upto) ? parse.tree : null;\n    parse.updateViewport(oldVieport);\n    return result;\n}\n/**\nQueries whether there is a full syntax tree available up to the\ngiven document position. If there isn't, the background parse\nprocess _might_ still be working and update the tree further, but\nthere is no guarantee of that—the parser will [stop\nworking](https://codemirror.net/6/docs/ref/#language.syntaxParserRunning) when it has spent a\ncertain amount of time or has moved beyond the visible viewport.\nAlways returns false if no language has been enabled.\n*/\nfunction syntaxTreeAvailable(state, upto = state.doc.length) {\n    var _a;\n    return ((_a = state.field(Language.state, false)) === null || _a === void 0 ? void 0 : _a.context.isDone(upto)) || false;\n}\n/**\nMove parsing forward, and update the editor state afterwards to\nreflect the new tree. Will work for at most `timeout`\nmilliseconds. Returns true if the parser managed get to the given\nposition in that time.\n*/\nfunction forceParsing(view, upto = view.viewport.to, timeout = 100) {\n    let success = ensureSyntaxTree(view.state, upto, timeout);\n    if (success != syntaxTree(view.state))\n        view.dispatch({});\n    return !!success;\n}\n/**\nTells you whether the language parser is planning to do more\nparsing work (in a `requestIdleCallback` pseudo-thread) or has\nstopped running, either because it parsed the entire document,\nbecause it spent too much time and was cut off, or because there\nis no language parser enabled.\n*/\nfunction syntaxParserRunning(view) {\n    var _a;\n    return ((_a = view.plugin(parseWorker)) === null || _a === void 0 ? void 0 : _a.isWorking()) || false;\n}\n/**\nLezer-style\n[`Input`](https://lezer.codemirror.net/docs/ref#common.Input)\nobject for a [`Text`](https://codemirror.net/6/docs/ref/#state.Text) object.\n*/\nclass DocInput {\n    /**\n    Create an input object for the given document.\n    */\n    constructor(doc) {\n        this.doc = doc;\n        this.cursorPos = 0;\n        this.string = \"\";\n        this.cursor = doc.iter();\n    }\n    get length() { return this.doc.length; }\n    syncTo(pos) {\n        this.string = this.cursor.next(pos - this.cursorPos).value;\n        this.cursorPos = pos + this.string.length;\n        return this.cursorPos - this.string.length;\n    }\n    chunk(pos) {\n        this.syncTo(pos);\n        return this.string;\n    }\n    get lineChunks() { return true; }\n    read(from, to) {\n        let stringStart = this.cursorPos - this.string.length;\n        if (from < stringStart || to >= this.cursorPos)\n            return this.doc.sliceString(from, to);\n        else\n            return this.string.slice(from - stringStart, to - stringStart);\n    }\n}\nlet currentContext = null;\n/**\nA parse context provided to parsers working on the editor content.\n*/\nclass ParseContext {\n    constructor(parser, \n    /**\n    The current editor state.\n    */\n    state, \n    /**\n    Tree fragments that can be reused by incremental re-parses.\n    */\n    fragments = [], \n    /**\n    @internal\n    */\n    tree, \n    /**\n    @internal\n    */\n    treeLen, \n    /**\n    The current editor viewport (or some overapproximation\n    thereof). Intended to be used for opportunistically avoiding\n    work (in which case\n    [`skipUntilInView`](https://codemirror.net/6/docs/ref/#language.ParseContext.skipUntilInView)\n    should be called to make sure the parser is restarted when the\n    skipped region becomes visible).\n    */\n    viewport, \n    /**\n    @internal\n    */\n    skipped, \n    /**\n    This is where skipping parsers can register a promise that,\n    when resolved, will schedule a new parse. It is cleared when\n    the parse worker picks up the promise. @internal\n    */\n    scheduleOn) {\n        this.parser = parser;\n        this.state = state;\n        this.fragments = fragments;\n        this.tree = tree;\n        this.treeLen = treeLen;\n        this.viewport = viewport;\n        this.skipped = skipped;\n        this.scheduleOn = scheduleOn;\n        this.parse = null;\n        /**\n        @internal\n        */\n        this.tempSkipped = [];\n    }\n    /**\n    @internal\n    */\n    static create(parser, state, viewport) {\n        return new ParseContext(parser, state, [], Tree.empty, 0, viewport, [], null);\n    }\n    startParse() {\n        return this.parser.startParse(new DocInput(this.state.doc), this.fragments);\n    }\n    /**\n    @internal\n    */\n    work(until, upto) {\n        if (upto != null && upto >= this.state.doc.length)\n            upto = undefined;\n        if (this.tree != Tree.empty && this.isDone(upto !== null && upto !== void 0 ? upto : this.state.doc.length)) {\n            this.takeTree();\n            return true;\n        }\n        return this.withContext(() => {\n            var _a;\n            if (typeof until == \"number\") {\n                let endTime = Date.now() + until;\n                until = () => Date.now() > endTime;\n            }\n            if (!this.parse)\n                this.parse = this.startParse();\n            if (upto != null && (this.parse.stoppedAt == null || this.parse.stoppedAt > upto) &&\n                upto < this.state.doc.length)\n                this.parse.stopAt(upto);\n            for (;;) {\n                let done = this.parse.advance();\n                if (done) {\n                    this.fragments = this.withoutTempSkipped(TreeFragment.addTree(done, this.fragments, this.parse.stoppedAt != null));\n                    this.treeLen = (_a = this.parse.stoppedAt) !== null && _a !== void 0 ? _a : this.state.doc.length;\n                    this.tree = done;\n                    this.parse = null;\n                    if (this.treeLen < (upto !== null && upto !== void 0 ? upto : this.state.doc.length))\n                        this.parse = this.startParse();\n                    else\n                        return true;\n                }\n                if (until())\n                    return false;\n            }\n        });\n    }\n    /**\n    @internal\n    */\n    takeTree() {\n        let pos, tree;\n        if (this.parse && (pos = this.parse.parsedPos) >= this.treeLen) {\n            if (this.parse.stoppedAt == null || this.parse.stoppedAt > pos)\n                this.parse.stopAt(pos);\n            this.withContext(() => { while (!(tree = this.parse.advance())) { } });\n            this.treeLen = pos;\n            this.tree = tree;\n            this.fragments = this.withoutTempSkipped(TreeFragment.addTree(this.tree, this.fragments, true));\n            this.parse = null;\n        }\n    }\n    withContext(f) {\n        let prev = currentContext;\n        currentContext = this;\n        try {\n            return f();\n        }\n        finally {\n            currentContext = prev;\n        }\n    }\n    withoutTempSkipped(fragments) {\n        for (let r; r = this.tempSkipped.pop();)\n            fragments = cutFragments(fragments, r.from, r.to);\n        return fragments;\n    }\n    /**\n    @internal\n    */\n    changes(changes, newState) {\n        let { fragments, tree, treeLen, viewport, skipped } = this;\n        this.takeTree();\n        if (!changes.empty) {\n            let ranges = [];\n            changes.iterChangedRanges((fromA, toA, fromB, toB) => ranges.push({ fromA, toA, fromB, toB }));\n            fragments = TreeFragment.applyChanges(fragments, ranges);\n            tree = Tree.empty;\n            treeLen = 0;\n            viewport = { from: changes.mapPos(viewport.from, -1), to: changes.mapPos(viewport.to, 1) };\n            if (this.skipped.length) {\n                skipped = [];\n                for (let r of this.skipped) {\n                    let from = changes.mapPos(r.from, 1), to = changes.mapPos(r.to, -1);\n                    if (from < to)\n                        skipped.push({ from, to });\n                }\n            }\n        }\n        return new ParseContext(this.parser, newState, fragments, tree, treeLen, viewport, skipped, this.scheduleOn);\n    }\n    /**\n    @internal\n    */\n    updateViewport(viewport) {\n        if (this.viewport.from == viewport.from && this.viewport.to == viewport.to)\n            return false;\n        this.viewport = viewport;\n        let startLen = this.skipped.length;\n        for (let i = 0; i < this.skipped.length; i++) {\n            let { from, to } = this.skipped[i];\n            if (from < viewport.to && to > viewport.from) {\n                this.fragments = cutFragments(this.fragments, from, to);\n                this.skipped.splice(i--, 1);\n            }\n        }\n        if (this.skipped.length >= startLen)\n            return false;\n        this.reset();\n        return true;\n    }\n    /**\n    @internal\n    */\n    reset() {\n        if (this.parse) {\n            this.takeTree();\n            this.parse = null;\n        }\n    }\n    /**\n    Notify the parse scheduler that the given region was skipped\n    because it wasn't in view, and the parse should be restarted\n    when it comes into view.\n    */\n    skipUntilInView(from, to) {\n        this.skipped.push({ from, to });\n    }\n    /**\n    Returns a parser intended to be used as placeholder when\n    asynchronously loading a nested parser. It'll skip its input and\n    mark it as not-really-parsed, so that the next update will parse\n    it again.\n    \n    When `until` is given, a reparse will be scheduled when that\n    promise resolves.\n    */\n    static getSkippingParser(until) {\n        return new class extends Parser {\n            createParse(input, fragments, ranges) {\n                let from = ranges[0].from, to = ranges[ranges.length - 1].to;\n                let parser = {\n                    parsedPos: from,\n                    advance() {\n                        let cx = currentContext;\n                        if (cx) {\n                            for (let r of ranges)\n                                cx.tempSkipped.push(r);\n                            if (until)\n                                cx.scheduleOn = cx.scheduleOn ? Promise.all([cx.scheduleOn, until]) : until;\n                        }\n                        this.parsedPos = to;\n                        return new Tree(NodeType.none, [], [], to - from);\n                    },\n                    stoppedAt: null,\n                    stopAt() { }\n                };\n                return parser;\n            }\n        };\n    }\n    /**\n    @internal\n    */\n    isDone(upto) {\n        upto = Math.min(upto, this.state.doc.length);\n        let frags = this.fragments;\n        return this.treeLen >= upto && frags.length && frags[0].from == 0 && frags[0].to >= upto;\n    }\n    /**\n    Get the context for the current parse, or `null` if no editor\n    parse is in progress.\n    */\n    static get() { return currentContext; }\n}\nfunction cutFragments(fragments, from, to) {\n    return TreeFragment.applyChanges(fragments, [{ fromA: from, toA: to, fromB: from, toB: to }]);\n}\nclass LanguageState {\n    constructor(\n    // A mutable parse state that is used to preserve work done during\n    // the lifetime of a state when moving to the next state.\n    context) {\n        this.context = context;\n        this.tree = context.tree;\n    }\n    apply(tr) {\n        if (!tr.docChanged && this.tree == this.context.tree)\n            return this;\n        let newCx = this.context.changes(tr.changes, tr.state);\n        // If the previous parse wasn't done, go forward only up to its\n        // end position or the end of the viewport, to avoid slowing down\n        // state updates with parse work beyond the viewport.\n        let upto = this.context.treeLen == tr.startState.doc.length ? undefined\n            : Math.max(tr.changes.mapPos(this.context.treeLen), newCx.viewport.to);\n        if (!newCx.work(20 /* Work.Apply */, upto))\n            newCx.takeTree();\n        return new LanguageState(newCx);\n    }\n    static init(state) {\n        let vpTo = Math.min(3000 /* Work.InitViewport */, state.doc.length);\n        let parseState = ParseContext.create(state.facet(language).parser, state, { from: 0, to: vpTo });\n        if (!parseState.work(20 /* Work.Apply */, vpTo))\n            parseState.takeTree();\n        return new LanguageState(parseState);\n    }\n}\nLanguage.state = /*@__PURE__*/StateField.define({\n    create: LanguageState.init,\n    update(value, tr) {\n        for (let e of tr.effects)\n            if (e.is(Language.setState))\n                return e.value;\n        if (tr.startState.facet(language) != tr.state.facet(language))\n            return LanguageState.init(tr.state);\n        return value.apply(tr);\n    }\n});\nlet requestIdle = (callback) => {\n    let timeout = setTimeout(() => callback(), 500 /* Work.MaxPause */);\n    return () => clearTimeout(timeout);\n};\nif (typeof requestIdleCallback != \"undefined\")\n    requestIdle = (callback) => {\n        let idle = -1, timeout = setTimeout(() => {\n            idle = requestIdleCallback(callback, { timeout: 500 /* Work.MaxPause */ - 100 /* Work.MinPause */ });\n        }, 100 /* Work.MinPause */);\n        return () => idle < 0 ? clearTimeout(timeout) : cancelIdleCallback(idle);\n    };\nconst isInputPending = typeof navigator != \"undefined\" && ((_a = navigator.scheduling) === null || _a === void 0 ? void 0 : _a.isInputPending)\n    ? () => navigator.scheduling.isInputPending() : null;\nconst parseWorker = /*@__PURE__*/ViewPlugin.fromClass(class ParseWorker {\n    constructor(view) {\n        this.view = view;\n        this.working = null;\n        this.workScheduled = 0;\n        // End of the current time chunk\n        this.chunkEnd = -1;\n        // Milliseconds of budget left for this chunk\n        this.chunkBudget = -1;\n        this.work = this.work.bind(this);\n        this.scheduleWork();\n    }\n    update(update) {\n        let cx = this.view.state.field(Language.state).context;\n        if (cx.updateViewport(update.view.viewport) || this.view.viewport.to > cx.treeLen)\n            this.scheduleWork();\n        if (update.docChanged || update.selectionSet) {\n            if (this.view.hasFocus)\n                this.chunkBudget += 50 /* Work.ChangeBonus */;\n            this.scheduleWork();\n        }\n        this.checkAsyncSchedule(cx);\n    }\n    scheduleWork() {\n        if (this.working)\n            return;\n        let { state } = this.view, field = state.field(Language.state);\n        if (field.tree != field.context.tree || !field.context.isDone(state.doc.length))\n            this.working = requestIdle(this.work);\n    }\n    work(deadline) {\n        this.working = null;\n        let now = Date.now();\n        if (this.chunkEnd < now && (this.chunkEnd < 0 || this.view.hasFocus)) { // Start a new chunk\n            this.chunkEnd = now + 30000 /* Work.ChunkTime */;\n            this.chunkBudget = 3000 /* Work.ChunkBudget */;\n        }\n        if (this.chunkBudget <= 0)\n            return; // No more budget\n        let { state, viewport: { to: vpTo } } = this.view, field = state.field(Language.state);\n        if (field.tree == field.context.tree && field.context.isDone(vpTo + 100000 /* Work.MaxParseAhead */))\n            return;\n        let endTime = Date.now() + Math.min(this.chunkBudget, 100 /* Work.Slice */, deadline && !isInputPending ? Math.max(25 /* Work.MinSlice */, deadline.timeRemaining() - 5) : 1e9);\n        let viewportFirst = field.context.treeLen < vpTo && state.doc.length > vpTo + 1000;\n        let done = field.context.work(() => {\n            return isInputPending && isInputPending() || Date.now() > endTime;\n        }, vpTo + (viewportFirst ? 0 : 100000 /* Work.MaxParseAhead */));\n        this.chunkBudget -= Date.now() - now;\n        if (done || this.chunkBudget <= 0) {\n            field.context.takeTree();\n            this.view.dispatch({ effects: Language.setState.of(new LanguageState(field.context)) });\n        }\n        if (this.chunkBudget > 0 && !(done && !viewportFirst))\n            this.scheduleWork();\n        this.checkAsyncSchedule(field.context);\n    }\n    checkAsyncSchedule(cx) {\n        if (cx.scheduleOn) {\n            this.workScheduled++;\n            cx.scheduleOn\n                .then(() => this.scheduleWork())\n                .catch(err => logException(this.view.state, err))\n                .then(() => this.workScheduled--);\n            cx.scheduleOn = null;\n        }\n    }\n    destroy() {\n        if (this.working)\n            this.working();\n    }\n    isWorking() {\n        return !!(this.working || this.workScheduled > 0);\n    }\n}, {\n    eventHandlers: { focus() { this.scheduleWork(); } }\n});\n/**\nThe facet used to associate a language with an editor state. Used\nby `Language` object's `extension` property (so you don't need to\nmanually wrap your languages in this). Can be used to access the\ncurrent language on a state.\n*/\nconst language = /*@__PURE__*/Facet.define({\n    combine(languages) { return languages.length ? languages[0] : null; },\n    enables: language => [\n        Language.state,\n        parseWorker,\n        EditorView.contentAttributes.compute([language], state => {\n            let lang = state.facet(language);\n            return lang && lang.name ? { \"data-language\": lang.name } : {};\n        })\n    ]\n});\n/**\nThis class bundles a [language](https://codemirror.net/6/docs/ref/#language.Language) with an\noptional set of supporting extensions. Language packages are\nencouraged to export a function that optionally takes a\nconfiguration object and returns a `LanguageSupport` instance, as\nthe main way for client code to use the package.\n*/\nclass LanguageSupport {\n    /**\n    Create a language support object.\n    */\n    constructor(\n    /**\n    The language object.\n    */\n    language, \n    /**\n    An optional set of supporting extensions. When nesting a\n    language in another language, the outer language is encouraged\n    to include the supporting extensions for its inner languages\n    in its own set of support extensions.\n    */\n    support = []) {\n        this.language = language;\n        this.support = support;\n        this.extension = [language, support];\n    }\n}\n/**\nLanguage descriptions are used to store metadata about languages\nand to dynamically load them. Their main role is finding the\nappropriate language for a filename or dynamically loading nested\nparsers.\n*/\nclass LanguageDescription {\n    constructor(\n    /**\n    The name of this language.\n    */\n    name, \n    /**\n    Alternative names for the mode (lowercased, includes `this.name`).\n    */\n    alias, \n    /**\n    File extensions associated with this language.\n    */\n    extensions, \n    /**\n    Optional filename pattern that should be associated with this\n    language.\n    */\n    filename, loadFunc, \n    /**\n    If the language has been loaded, this will hold its value.\n    */\n    support = undefined) {\n        this.name = name;\n        this.alias = alias;\n        this.extensions = extensions;\n        this.filename = filename;\n        this.loadFunc = loadFunc;\n        this.support = support;\n        this.loading = null;\n    }\n    /**\n    Start loading the the language. Will return a promise that\n    resolves to a [`LanguageSupport`](https://codemirror.net/6/docs/ref/#language.LanguageSupport)\n    object when the language successfully loads.\n    */\n    load() {\n        return this.loading || (this.loading = this.loadFunc().then(support => this.support = support, err => { this.loading = null; throw err; }));\n    }\n    /**\n    Create a language description.\n    */\n    static of(spec) {\n        let { load, support } = spec;\n        if (!load) {\n            if (!support)\n                throw new RangeError(\"Must pass either 'load' or 'support' to LanguageDescription.of\");\n            load = () => Promise.resolve(support);\n        }\n        return new LanguageDescription(spec.name, (spec.alias || []).concat(spec.name).map(s => s.toLowerCase()), spec.extensions || [], spec.filename, load, support);\n    }\n    /**\n    Look for a language in the given array of descriptions that\n    matches the filename. Will first match\n    [`filename`](https://codemirror.net/6/docs/ref/#language.LanguageDescription.filename) patterns,\n    and then [extensions](https://codemirror.net/6/docs/ref/#language.LanguageDescription.extensions),\n    and return the first language that matches.\n    */\n    static matchFilename(descs, filename) {\n        for (let d of descs)\n            if (d.filename && d.filename.test(filename))\n                return d;\n        let ext = /\\.([^.]+)$/.exec(filename);\n        if (ext)\n            for (let d of descs)\n                if (d.extensions.indexOf(ext[1]) > -1)\n                    return d;\n        return null;\n    }\n    /**\n    Look for a language whose name or alias matches the the given\n    name (case-insensitively). If `fuzzy` is true, and no direct\n    matchs is found, this'll also search for a language whose name\n    or alias occurs in the string (for names shorter than three\n    characters, only when surrounded by non-word characters).\n    */\n    static matchLanguageName(descs, name, fuzzy = true) {\n        name = name.toLowerCase();\n        for (let d of descs)\n            if (d.alias.some(a => a == name))\n                return d;\n        if (fuzzy)\n            for (let d of descs)\n                for (let a of d.alias) {\n                    let found = name.indexOf(a);\n                    if (found > -1 && (a.length > 2 || !/\\w/.test(name[found - 1]) && !/\\w/.test(name[found + a.length])))\n                        return d;\n                }\n        return null;\n    }\n}\n\n/**\nFacet that defines a way to provide a function that computes the\nappropriate indentation depth, as a column number (see\n[`indentString`](https://codemirror.net/6/docs/ref/#language.indentString)), at the start of a given\nline. A return value of `null` indicates no indentation can be\ndetermined, and the line should inherit the indentation of the one\nabove it. A return value of `undefined` defers to the next indent\nservice.\n*/\nconst indentService = /*@__PURE__*/Facet.define();\n/**\nFacet for overriding the unit by which indentation happens. Should\nbe a string consisting either entirely of the same whitespace\ncharacter. When not set, this defaults to 2 spaces.\n*/\nconst indentUnit = /*@__PURE__*/Facet.define({\n    combine: values => {\n        if (!values.length)\n            return \"  \";\n        let unit = values[0];\n        if (!unit || /\\S/.test(unit) || Array.from(unit).some(e => e != unit[0]))\n            throw new Error(\"Invalid indent unit: \" + JSON.stringify(values[0]));\n        return unit;\n    }\n});\n/**\nReturn the _column width_ of an indent unit in the state.\nDetermined by the [`indentUnit`](https://codemirror.net/6/docs/ref/#language.indentUnit)\nfacet, and [`tabSize`](https://codemirror.net/6/docs/ref/#state.EditorState^tabSize) when that\ncontains tabs.\n*/\nfunction getIndentUnit(state) {\n    let unit = state.facet(indentUnit);\n    return unit.charCodeAt(0) == 9 ? state.tabSize * unit.length : unit.length;\n}\n/**\nCreate an indentation string that covers columns 0 to `cols`.\nWill use tabs for as much of the columns as possible when the\n[`indentUnit`](https://codemirror.net/6/docs/ref/#language.indentUnit) facet contains\ntabs.\n*/\nfunction indentString(state, cols) {\n    let result = \"\", ts = state.tabSize, ch = state.facet(indentUnit)[0];\n    if (ch == \"\\t\") {\n        while (cols >= ts) {\n            result += \"\\t\";\n            cols -= ts;\n        }\n        ch = \" \";\n    }\n    for (let i = 0; i < cols; i++)\n        result += ch;\n    return result;\n}\n/**\nGet the indentation, as a column number, at the given position.\nWill first consult any [indent services](https://codemirror.net/6/docs/ref/#language.indentService)\nthat are registered, and if none of those return an indentation,\nthis will check the syntax tree for the [indent node\nprop](https://codemirror.net/6/docs/ref/#language.indentNodeProp) and use that if found. Returns a\nnumber when an indentation could be determined, and null\notherwise.\n*/\nfunction getIndentation(context, pos) {\n    if (context instanceof EditorState)\n        context = new IndentContext(context);\n    for (let service of context.state.facet(indentService)) {\n        let result = service(context, pos);\n        if (result !== undefined)\n            return result;\n    }\n    let tree = syntaxTree(context.state);\n    return tree.length >= pos ? syntaxIndentation(context, tree, pos) : null;\n}\n/**\nCreate a change set that auto-indents all lines touched by the\ngiven document range.\n*/\nfunction indentRange(state, from, to) {\n    let updated = Object.create(null);\n    let context = new IndentContext(state, { overrideIndentation: start => { var _a; return (_a = updated[start]) !== null && _a !== void 0 ? _a : -1; } });\n    let changes = [];\n    for (let pos = from; pos <= to;) {\n        let line = state.doc.lineAt(pos);\n        pos = line.to + 1;\n        let indent = getIndentation(context, line.from);\n        if (indent == null)\n            continue;\n        if (!/\\S/.test(line.text))\n            indent = 0;\n        let cur = /^\\s*/.exec(line.text)[0];\n        let norm = indentString(state, indent);\n        if (cur != norm) {\n            updated[line.from] = indent;\n            changes.push({ from: line.from, to: line.from + cur.length, insert: norm });\n        }\n    }\n    return state.changes(changes);\n}\n/**\nIndentation contexts are used when calling [indentation\nservices](https://codemirror.net/6/docs/ref/#language.indentService). They provide helper utilities\nuseful in indentation logic, and can selectively override the\nindentation reported for some lines.\n*/\nclass IndentContext {\n    /**\n    Create an indent context.\n    */\n    constructor(\n    /**\n    The editor state.\n    */\n    state, \n    /**\n    @internal\n    */\n    options = {}) {\n        this.state = state;\n        this.options = options;\n        this.unit = getIndentUnit(state);\n    }\n    /**\n    Get a description of the line at the given position, taking\n    [simulated line\n    breaks](https://codemirror.net/6/docs/ref/#language.IndentContext.constructor^options.simulateBreak)\n    into account. If there is such a break at `pos`, the `bias`\n    argument determines whether the part of the line line before or\n    after the break is used.\n    */\n    lineAt(pos, bias = 1) {\n        let line = this.state.doc.lineAt(pos);\n        let { simulateBreak, simulateDoubleBreak } = this.options;\n        if (simulateBreak != null && simulateBreak >= line.from && simulateBreak <= line.to) {\n            if (simulateDoubleBreak && simulateBreak == pos)\n                return { text: \"\", from: pos };\n            else if (bias < 0 ? simulateBreak < pos : simulateBreak <= pos)\n                return { text: line.text.slice(simulateBreak - line.from), from: simulateBreak };\n            else\n                return { text: line.text.slice(0, simulateBreak - line.from), from: line.from };\n        }\n        return line;\n    }\n    /**\n    Get the text directly after `pos`, either the entire line\n    or the next 100 characters, whichever is shorter.\n    */\n    textAfterPos(pos, bias = 1) {\n        if (this.options.simulateDoubleBreak && pos == this.options.simulateBreak)\n            return \"\";\n        let { text, from } = this.lineAt(pos, bias);\n        return text.slice(pos - from, Math.min(text.length, pos + 100 - from));\n    }\n    /**\n    Find the column for the given position.\n    */\n    column(pos, bias = 1) {\n        let { text, from } = this.lineAt(pos, bias);\n        let result = this.countColumn(text, pos - from);\n        let override = this.options.overrideIndentation ? this.options.overrideIndentation(from) : -1;\n        if (override > -1)\n            result += override - this.countColumn(text, text.search(/\\S|$/));\n        return result;\n    }\n    /**\n    Find the column position (taking tabs into account) of the given\n    position in the given string.\n    */\n    countColumn(line, pos = line.length) {\n        return countColumn(line, this.state.tabSize, pos);\n    }\n    /**\n    Find the indentation column of the line at the given point.\n    */\n    lineIndent(pos, bias = 1) {\n        let { text, from } = this.lineAt(pos, bias);\n        let override = this.options.overrideIndentation;\n        if (override) {\n            let overriden = override(from);\n            if (overriden > -1)\n                return overriden;\n        }\n        return this.countColumn(text, text.search(/\\S|$/));\n    }\n    /**\n    Returns the [simulated line\n    break](https://codemirror.net/6/docs/ref/#language.IndentContext.constructor^options.simulateBreak)\n    for this context, if any.\n    */\n    get simulatedBreak() {\n        return this.options.simulateBreak || null;\n    }\n}\n/**\nA syntax tree node prop used to associate indentation strategies\nwith node types. Such a strategy is a function from an indentation\ncontext to a column number (see also\n[`indentString`](https://codemirror.net/6/docs/ref/#language.indentString)) or null, where null\nindicates that no definitive indentation can be determined.\n*/\nconst indentNodeProp = /*@__PURE__*/new NodeProp();\n// Compute the indentation for a given position from the syntax tree.\nfunction syntaxIndentation(cx, ast, pos) {\n    let stack = ast.resolveStack(pos);\n    let inner = ast.resolveInner(pos, -1).resolve(pos, 0).enterUnfinishedNodesBefore(pos);\n    if (inner != stack.node) {\n        let add = [];\n        for (let cur = inner; cur && !(cur.from == stack.node.from && cur.type == stack.node.type); cur = cur.parent)\n            add.push(cur);\n        for (let i = add.length - 1; i >= 0; i--)\n            stack = { node: add[i], next: stack };\n    }\n    return indentFor(stack, cx, pos);\n}\nfunction indentFor(stack, cx, pos) {\n    for (let cur = stack; cur; cur = cur.next) {\n        let strategy = indentStrategy(cur.node);\n        if (strategy)\n            return strategy(TreeIndentContext.create(cx, pos, cur));\n    }\n    return 0;\n}\nfunction ignoreClosed(cx) {\n    return cx.pos == cx.options.simulateBreak && cx.options.simulateDoubleBreak;\n}\nfunction indentStrategy(tree) {\n    let strategy = tree.type.prop(indentNodeProp);\n    if (strategy)\n        return strategy;\n    let first = tree.firstChild, close;\n    if (first && (close = first.type.prop(NodeProp.closedBy))) {\n        let last = tree.lastChild, closed = last && close.indexOf(last.name) > -1;\n        return cx => delimitedStrategy(cx, true, 1, undefined, closed && !ignoreClosed(cx) ? last.from : undefined);\n    }\n    return tree.parent == null ? topIndent : null;\n}\nfunction topIndent() { return 0; }\n/**\nObjects of this type provide context information and helper\nmethods to indentation functions registered on syntax nodes.\n*/\nclass TreeIndentContext extends IndentContext {\n    constructor(base, \n    /**\n    The position at which indentation is being computed.\n    */\n    pos, \n    /**\n    @internal\n    */\n    context) {\n        super(base.state, base.options);\n        this.base = base;\n        this.pos = pos;\n        this.context = context;\n    }\n    /**\n    The syntax tree node to which the indentation strategy\n    applies.\n    */\n    get node() { return this.context.node; }\n    /**\n    @internal\n    */\n    static create(base, pos, context) {\n        return new TreeIndentContext(base, pos, context);\n    }\n    /**\n    Get the text directly after `this.pos`, either the entire line\n    or the next 100 characters, whichever is shorter.\n    */\n    get textAfter() {\n        return this.textAfterPos(this.pos);\n    }\n    /**\n    Get the indentation at the reference line for `this.node`, which\n    is the line on which it starts, unless there is a node that is\n    _not_ a parent of this node covering the start of that line. If\n    so, the line at the start of that node is tried, again skipping\n    on if it is covered by another such node.\n    */\n    get baseIndent() {\n        return this.baseIndentFor(this.node);\n    }\n    /**\n    Get the indentation for the reference line of the given node\n    (see [`baseIndent`](https://codemirror.net/6/docs/ref/#language.TreeIndentContext.baseIndent)).\n    */\n    baseIndentFor(node) {\n        let line = this.state.doc.lineAt(node.from);\n        // Skip line starts that are covered by a sibling (or cousin, etc)\n        for (;;) {\n            let atBreak = node.resolve(line.from);\n            while (atBreak.parent && atBreak.parent.from == atBreak.from)\n                atBreak = atBreak.parent;\n            if (isParent(atBreak, node))\n                break;\n            line = this.state.doc.lineAt(atBreak.from);\n        }\n        return this.lineIndent(line.from);\n    }\n    /**\n    Continue looking for indentations in the node's parent nodes,\n    and return the result of that.\n    */\n    continue() {\n        return indentFor(this.context.next, this.base, this.pos);\n    }\n}\nfunction isParent(parent, of) {\n    for (let cur = of; cur; cur = cur.parent)\n        if (parent == cur)\n            return true;\n    return false;\n}\n// Check whether a delimited node is aligned (meaning there are\n// non-skipped nodes on the same line as the opening delimiter). And\n// if so, return the opening token.\nfunction bracketedAligned(context) {\n    let tree = context.node;\n    let openToken = tree.childAfter(tree.from), last = tree.lastChild;\n    if (!openToken)\n        return null;\n    let sim = context.options.simulateBreak;\n    let openLine = context.state.doc.lineAt(openToken.from);\n    let lineEnd = sim == null || sim <= openLine.from ? openLine.to : Math.min(openLine.to, sim);\n    for (let pos = openToken.to;;) {\n        let next = tree.childAfter(pos);\n        if (!next || next == last)\n            return null;\n        if (!next.type.isSkipped) {\n            if (next.from >= lineEnd)\n                return null;\n            let space = /^ */.exec(openLine.text.slice(openToken.to - openLine.from))[0].length;\n            return { from: openToken.from, to: openToken.to + space };\n        }\n        pos = next.to;\n    }\n}\n/**\nAn indentation strategy for delimited (usually bracketed) nodes.\nWill, by default, indent one unit more than the parent's base\nindent unless the line starts with a closing token. When `align`\nis true and there are non-skipped nodes on the node's opening\nline, the content of the node will be aligned with the end of the\nopening node, like this:\n\n    foo(bar,\n        baz)\n*/\nfunction delimitedIndent({ closing, align = true, units = 1 }) {\n    return (context) => delimitedStrategy(context, align, units, closing);\n}\nfunction delimitedStrategy(context, align, units, closing, closedAt) {\n    let after = context.textAfter, space = after.match(/^\\s*/)[0].length;\n    let closed = closing && after.slice(space, space + closing.length) == closing || closedAt == context.pos + space;\n    let aligned = align ? bracketedAligned(context) : null;\n    if (aligned)\n        return closed ? context.column(aligned.from) : context.column(aligned.to);\n    return context.baseIndent + (closed ? 0 : context.unit * units);\n}\n/**\nAn indentation strategy that aligns a node's content to its base\nindentation.\n*/\nconst flatIndent = (context) => context.baseIndent;\n/**\nCreates an indentation strategy that, by default, indents\ncontinued lines one unit more than the node's base indentation.\nYou can provide `except` to prevent indentation of lines that\nmatch a pattern (for example `/^else\\b/` in `if`/`else`\nconstructs), and you can change the amount of units used with the\n`units` option.\n*/\nfunction continuedIndent({ except, units = 1 } = {}) {\n    return (context) => {\n        let matchExcept = except && except.test(context.textAfter);\n        return context.baseIndent + (matchExcept ? 0 : units * context.unit);\n    };\n}\nconst DontIndentBeyond = 200;\n/**\nEnables reindentation on input. When a language defines an\n`indentOnInput` field in its [language\ndata](https://codemirror.net/6/docs/ref/#state.EditorState.languageDataAt), which must hold a regular\nexpression, the line at the cursor will be reindented whenever new\ntext is typed and the input from the start of the line up to the\ncursor matches that regexp.\n\nTo avoid unneccesary reindents, it is recommended to start the\nregexp with `^` (usually followed by `\\s*`), and end it with `$`.\nFor example, `/^\\s*\\}$/` will reindent when a closing brace is\nadded at the start of a line.\n*/\nfunction indentOnInput() {\n    return EditorState.transactionFilter.of(tr => {\n        if (!tr.docChanged || !tr.isUserEvent(\"input.type\") && !tr.isUserEvent(\"input.complete\"))\n            return tr;\n        let rules = tr.startState.languageDataAt(\"indentOnInput\", tr.startState.selection.main.head);\n        if (!rules.length)\n            return tr;\n        let doc = tr.newDoc, { head } = tr.newSelection.main, line = doc.lineAt(head);\n        if (head > line.from + DontIndentBeyond)\n            return tr;\n        let lineStart = doc.sliceString(line.from, head);\n        if (!rules.some(r => r.test(lineStart)))\n            return tr;\n        let { state } = tr, last = -1, changes = [];\n        for (let { head } of state.selection.ranges) {\n            let line = state.doc.lineAt(head);\n            if (line.from == last)\n                continue;\n            last = line.from;\n            let indent = getIndentation(state, line.from);\n            if (indent == null)\n                continue;\n            let cur = /^\\s*/.exec(line.text)[0];\n            let norm = indentString(state, indent);\n            if (cur != norm)\n                changes.push({ from: line.from, to: line.from + cur.length, insert: norm });\n        }\n        return changes.length ? [tr, { changes, sequential: true }] : tr;\n    });\n}\n\n/**\nA facet that registers a code folding service. When called with\nthe extent of a line, such a function should return a foldable\nrange that starts on that line (but continues beyond it), if one\ncan be found.\n*/\nconst foldService = /*@__PURE__*/Facet.define();\n/**\nThis node prop is used to associate folding information with\nsyntax node types. Given a syntax node, it should check whether\nthat tree is foldable and return the range that can be collapsed\nwhen it is.\n*/\nconst foldNodeProp = /*@__PURE__*/new NodeProp();\n/**\n[Fold](https://codemirror.net/6/docs/ref/#language.foldNodeProp) function that folds everything but\nthe first and the last child of a syntax node. Useful for nodes\nthat start and end with delimiters.\n*/\nfunction foldInside(node) {\n    let first = node.firstChild, last = node.lastChild;\n    return first && first.to < last.from ? { from: first.to, to: last.type.isError ? node.to : last.from } : null;\n}\nfunction syntaxFolding(state, start, end) {\n    let tree = syntaxTree(state);\n    if (tree.length < end)\n        return null;\n    let stack = tree.resolveStack(end, 1);\n    let found = null;\n    for (let iter = stack; iter; iter = iter.next) {\n        let cur = iter.node;\n        if (cur.to <= end || cur.from > end)\n            continue;\n        if (found && cur.from < start)\n            break;\n        let prop = cur.type.prop(foldNodeProp);\n        if (prop && (cur.to < tree.length - 50 || tree.length == state.doc.length || !isUnfinished(cur))) {\n            let value = prop(cur, state);\n            if (value && value.from <= end && value.from >= start && value.to > end)\n                found = value;\n        }\n    }\n    return found;\n}\nfunction isUnfinished(node) {\n    let ch = node.lastChild;\n    return ch && ch.to == node.to && ch.type.isError;\n}\n/**\nCheck whether the given line is foldable. First asks any fold\nservices registered through\n[`foldService`](https://codemirror.net/6/docs/ref/#language.foldService), and if none of them return\na result, tries to query the [fold node\nprop](https://codemirror.net/6/docs/ref/#language.foldNodeProp) of syntax nodes that cover the end\nof the line.\n*/\nfunction foldable(state, lineStart, lineEnd) {\n    for (let service of state.facet(foldService)) {\n        let result = service(state, lineStart, lineEnd);\n        if (result)\n            return result;\n    }\n    return syntaxFolding(state, lineStart, lineEnd);\n}\nfunction mapRange(range, mapping) {\n    let from = mapping.mapPos(range.from, 1), to = mapping.mapPos(range.to, -1);\n    return from >= to ? undefined : { from, to };\n}\n/**\nState effect that can be attached to a transaction to fold the\ngiven range. (You probably only need this in exceptional\ncircumstances—usually you'll just want to let\n[`foldCode`](https://codemirror.net/6/docs/ref/#language.foldCode) and the [fold\ngutter](https://codemirror.net/6/docs/ref/#language.foldGutter) create the transactions.)\n*/\nconst foldEffect = /*@__PURE__*/StateEffect.define({ map: mapRange });\n/**\nState effect that unfolds the given range (if it was folded).\n*/\nconst unfoldEffect = /*@__PURE__*/StateEffect.define({ map: mapRange });\nfunction selectedLines(view) {\n    let lines = [];\n    for (let { head } of view.state.selection.ranges) {\n        if (lines.some(l => l.from <= head && l.to >= head))\n            continue;\n        lines.push(view.lineBlockAt(head));\n    }\n    return lines;\n}\n/**\nThe state field that stores the folded ranges (as a [decoration\nset](https://codemirror.net/6/docs/ref/#view.DecorationSet)). Can be passed to\n[`EditorState.toJSON`](https://codemirror.net/6/docs/ref/#state.EditorState.toJSON) and\n[`fromJSON`](https://codemirror.net/6/docs/ref/#state.EditorState^fromJSON) to serialize the fold\nstate.\n*/\nconst foldState = /*@__PURE__*/StateField.define({\n    create() {\n        return Decoration.none;\n    },\n    update(folded, tr) {\n        folded = folded.map(tr.changes);\n        for (let e of tr.effects) {\n            if (e.is(foldEffect) && !foldExists(folded, e.value.from, e.value.to)) {\n                let { preparePlaceholder } = tr.state.facet(foldConfig);\n                let widget = !preparePlaceholder ? foldWidget :\n                    Decoration.replace({ widget: new PreparedFoldWidget(preparePlaceholder(tr.state, e.value)) });\n                folded = folded.update({ add: [widget.range(e.value.from, e.value.to)] });\n            }\n            else if (e.is(unfoldEffect)) {\n                folded = folded.update({ filter: (from, to) => e.value.from != from || e.value.to != to,\n                    filterFrom: e.value.from, filterTo: e.value.to });\n            }\n        }\n        // Clear folded ranges that cover the selection head\n        if (tr.selection) {\n            let onSelection = false, { head } = tr.selection.main;\n            folded.between(head, head, (a, b) => { if (a < head && b > head)\n                onSelection = true; });\n            if (onSelection)\n                folded = folded.update({\n                    filterFrom: head,\n                    filterTo: head,\n                    filter: (a, b) => b <= head || a >= head\n                });\n        }\n        return folded;\n    },\n    provide: f => EditorView.decorations.from(f),\n    toJSON(folded, state) {\n        let ranges = [];\n        folded.between(0, state.doc.length, (from, to) => { ranges.push(from, to); });\n        return ranges;\n    },\n    fromJSON(value) {\n        if (!Array.isArray(value) || value.length % 2)\n            throw new RangeError(\"Invalid JSON for fold state\");\n        let ranges = [];\n        for (let i = 0; i < value.length;) {\n            let from = value[i++], to = value[i++];\n            if (typeof from != \"number\" || typeof to != \"number\")\n                throw new RangeError(\"Invalid JSON for fold state\");\n            ranges.push(foldWidget.range(from, to));\n        }\n        return Decoration.set(ranges, true);\n    }\n});\n/**\nGet a [range set](https://codemirror.net/6/docs/ref/#state.RangeSet) containing the folded ranges\nin the given state.\n*/\nfunction foldedRanges(state) {\n    return state.field(foldState, false) || RangeSet.empty;\n}\nfunction findFold(state, from, to) {\n    var _a;\n    let found = null;\n    (_a = state.field(foldState, false)) === null || _a === void 0 ? void 0 : _a.between(from, to, (from, to) => {\n        if (!found || found.from > from)\n            found = { from, to };\n    });\n    return found;\n}\nfunction foldExists(folded, from, to) {\n    let found = false;\n    folded.between(from, from, (a, b) => { if (a == from && b == to)\n        found = true; });\n    return found;\n}\nfunction maybeEnable(state, other) {\n    return state.field(foldState, false) ? other : other.concat(StateEffect.appendConfig.of(codeFolding()));\n}\n/**\nFold the lines that are selected, if possible.\n*/\nconst foldCode = view => {\n    for (let line of selectedLines(view)) {\n        let range = foldable(view.state, line.from, line.to);\n        if (range) {\n            view.dispatch({ effects: maybeEnable(view.state, [foldEffect.of(range), announceFold(view, range)]) });\n            return true;\n        }\n    }\n    return false;\n};\n/**\nUnfold folded ranges on selected lines.\n*/\nconst unfoldCode = view => {\n    if (!view.state.field(foldState, false))\n        return false;\n    let effects = [];\n    for (let line of selectedLines(view)) {\n        let folded = findFold(view.state, line.from, line.to);\n        if (folded)\n            effects.push(unfoldEffect.of(folded), announceFold(view, folded, false));\n    }\n    if (effects.length)\n        view.dispatch({ effects });\n    return effects.length > 0;\n};\nfunction announceFold(view, range, fold = true) {\n    let lineFrom = view.state.doc.lineAt(range.from).number, lineTo = view.state.doc.lineAt(range.to).number;\n    return EditorView.announce.of(`${view.state.phrase(fold ? \"Folded lines\" : \"Unfolded lines\")} ${lineFrom} ${view.state.phrase(\"to\")} ${lineTo}.`);\n}\n/**\nFold all top-level foldable ranges. Note that, in most cases,\nfolding information will depend on the [syntax\ntree](https://codemirror.net/6/docs/ref/#language.syntaxTree), and folding everything may not work\nreliably when the document hasn't been fully parsed (either\nbecause the editor state was only just initialized, or because the\ndocument is so big that the parser decided not to parse it\nentirely).\n*/\nconst foldAll = view => {\n    let { state } = view, effects = [];\n    for (let pos = 0; pos < state.doc.length;) {\n        let line = view.lineBlockAt(pos), range = foldable(state, line.from, line.to);\n        if (range)\n            effects.push(foldEffect.of(range));\n        pos = (range ? view.lineBlockAt(range.to) : line).to + 1;\n    }\n    if (effects.length)\n        view.dispatch({ effects: maybeEnable(view.state, effects) });\n    return !!effects.length;\n};\n/**\nUnfold all folded code.\n*/\nconst unfoldAll = view => {\n    let field = view.state.field(foldState, false);\n    if (!field || !field.size)\n        return false;\n    let effects = [];\n    field.between(0, view.state.doc.length, (from, to) => { effects.push(unfoldEffect.of({ from, to })); });\n    view.dispatch({ effects });\n    return true;\n};\n// Find the foldable region containing the given line, if one exists\nfunction foldableContainer(view, lineBlock) {\n    // Look backwards through line blocks until we find a foldable region that\n    // intersects with the line\n    for (let line = lineBlock;;) {\n        let foldableRegion = foldable(view.state, line.from, line.to);\n        if (foldableRegion && foldableRegion.to > lineBlock.from)\n            return foldableRegion;\n        if (!line.from)\n            return null;\n        line = view.lineBlockAt(line.from - 1);\n    }\n}\n/**\nToggle folding at cursors. Unfolds if there is an existing fold\nstarting in that line, tries to find a foldable range around it\notherwise.\n*/\nconst toggleFold = (view) => {\n    let effects = [];\n    for (let line of selectedLines(view)) {\n        let folded = findFold(view.state, line.from, line.to);\n        if (folded) {\n            effects.push(unfoldEffect.of(folded), announceFold(view, folded, false));\n        }\n        else {\n            let foldRange = foldableContainer(view, line);\n            if (foldRange)\n                effects.push(foldEffect.of(foldRange), announceFold(view, foldRange));\n        }\n    }\n    if (effects.length > 0)\n        view.dispatch({ effects: maybeEnable(view.state, effects) });\n    return !!effects.length;\n};\n/**\nDefault fold-related key bindings.\n\n - Ctrl-Shift-[ (Cmd-Alt-[ on macOS): [`foldCode`](https://codemirror.net/6/docs/ref/#language.foldCode).\n - Ctrl-Shift-] (Cmd-Alt-] on macOS): [`unfoldCode`](https://codemirror.net/6/docs/ref/#language.unfoldCode).\n - Ctrl-Alt-[: [`foldAll`](https://codemirror.net/6/docs/ref/#language.foldAll).\n - Ctrl-Alt-]: [`unfoldAll`](https://codemirror.net/6/docs/ref/#language.unfoldAll).\n*/\nconst foldKeymap = [\n    { key: \"Ctrl-Shift-[\", mac: \"Cmd-Alt-[\", run: foldCode },\n    { key: \"Ctrl-Shift-]\", mac: \"Cmd-Alt-]\", run: unfoldCode },\n    { key: \"Ctrl-Alt-[\", run: foldAll },\n    { key: \"Ctrl-Alt-]\", run: unfoldAll }\n];\nconst defaultConfig = {\n    placeholderDOM: null,\n    preparePlaceholder: null,\n    placeholderText: \"…\"\n};\nconst foldConfig = /*@__PURE__*/Facet.define({\n    combine(values) { return combineConfig(values, defaultConfig); }\n});\n/**\nCreate an extension that configures code folding.\n*/\nfunction codeFolding(config) {\n    let result = [foldState, baseTheme$1];\n    if (config)\n        result.push(foldConfig.of(config));\n    return result;\n}\nfunction widgetToDOM(view, prepared) {\n    let { state } = view, conf = state.facet(foldConfig);\n    let onclick = (event) => {\n        let line = view.lineBlockAt(view.posAtDOM(event.target));\n        let folded = findFold(view.state, line.from, line.to);\n        if (folded)\n            view.dispatch({ effects: unfoldEffect.of(folded) });\n        event.preventDefault();\n    };\n    if (conf.placeholderDOM)\n        return conf.placeholderDOM(view, onclick, prepared);\n    let element = document.createElement(\"span\");\n    element.textContent = conf.placeholderText;\n    element.setAttribute(\"aria-label\", state.phrase(\"folded code\"));\n    element.title = state.phrase(\"unfold\");\n    element.className = \"cm-foldPlaceholder\";\n    element.onclick = onclick;\n    return element;\n}\nconst foldWidget = /*@__PURE__*/Decoration.replace({ widget: /*@__PURE__*/new class extends WidgetType {\n        toDOM(view) { return widgetToDOM(view, null); }\n    } });\nclass PreparedFoldWidget extends WidgetType {\n    constructor(value) {\n        super();\n        this.value = value;\n    }\n    eq(other) { return this.value == other.value; }\n    toDOM(view) { return widgetToDOM(view, this.value); }\n}\nconst foldGutterDefaults = {\n    openText: \"⌄\",\n    closedText: \"›\",\n    markerDOM: null,\n    domEventHandlers: {},\n    foldingChanged: () => false\n};\nclass FoldMarker extends GutterMarker {\n    constructor(config, open) {\n        super();\n        this.config = config;\n        this.open = open;\n    }\n    eq(other) { return this.config == other.config && this.open == other.open; }\n    toDOM(view) {\n        if (this.config.markerDOM)\n            return this.config.markerDOM(this.open);\n        let span = document.createElement(\"span\");\n        span.textContent = this.open ? this.config.openText : this.config.closedText;\n        span.title = view.state.phrase(this.open ? \"Fold line\" : \"Unfold line\");\n        return span;\n    }\n}\n/**\nCreate an extension that registers a fold gutter, which shows a\nfold status indicator before foldable lines (which can be clicked\nto fold or unfold the line).\n*/\nfunction foldGutter(config = {}) {\n    let fullConfig = Object.assign(Object.assign({}, foldGutterDefaults), config);\n    let canFold = new FoldMarker(fullConfig, true), canUnfold = new FoldMarker(fullConfig, false);\n    let markers = ViewPlugin.fromClass(class {\n        constructor(view) {\n            this.from = view.viewport.from;\n            this.markers = this.buildMarkers(view);\n        }\n        update(update) {\n            if (update.docChanged || update.viewportChanged ||\n                update.startState.facet(language) != update.state.facet(language) ||\n                update.startState.field(foldState, false) != update.state.field(foldState, false) ||\n                syntaxTree(update.startState) != syntaxTree(update.state) ||\n                fullConfig.foldingChanged(update))\n                this.markers = this.buildMarkers(update.view);\n        }\n        buildMarkers(view) {\n            let builder = new RangeSetBuilder();\n            for (let line of view.viewportLineBlocks) {\n                let mark = findFold(view.state, line.from, line.to) ? canUnfold\n                    : foldable(view.state, line.from, line.to) ? canFold : null;\n                if (mark)\n                    builder.add(line.from, line.from, mark);\n            }\n            return builder.finish();\n        }\n    });\n    let { domEventHandlers } = fullConfig;\n    return [\n        markers,\n        gutter({\n            class: \"cm-foldGutter\",\n            markers(view) { var _a; return ((_a = view.plugin(markers)) === null || _a === void 0 ? void 0 : _a.markers) || RangeSet.empty; },\n            initialSpacer() {\n                return new FoldMarker(fullConfig, false);\n            },\n            domEventHandlers: Object.assign(Object.assign({}, domEventHandlers), { click: (view, line, event) => {\n                    if (domEventHandlers.click && domEventHandlers.click(view, line, event))\n                        return true;\n                    let folded = findFold(view.state, line.from, line.to);\n                    if (folded) {\n                        view.dispatch({ effects: unfoldEffect.of(folded) });\n                        return true;\n                    }\n                    let range = foldable(view.state, line.from, line.to);\n                    if (range) {\n                        view.dispatch({ effects: foldEffect.of(range) });\n                        return true;\n                    }\n                    return false;\n                } })\n        }),\n        codeFolding()\n    ];\n}\nconst baseTheme$1 = /*@__PURE__*/EditorView.baseTheme({\n    \".cm-foldPlaceholder\": {\n        backgroundColor: \"#eee\",\n        border: \"1px solid #ddd\",\n        color: \"#888\",\n        borderRadius: \".2em\",\n        margin: \"0 1px\",\n        padding: \"0 1px\",\n        cursor: \"pointer\"\n    },\n    \".cm-foldGutter span\": {\n        padding: \"0 1px\",\n        cursor: \"pointer\"\n    }\n});\n\n/**\nA highlight style associates CSS styles with higlighting\n[tags](https://lezer.codemirror.net/docs/ref#highlight.Tag).\n*/\nclass HighlightStyle {\n    constructor(\n    /**\n    The tag styles used to create this highlight style.\n    */\n    specs, options) {\n        this.specs = specs;\n        let modSpec;\n        function def(spec) {\n            let cls = StyleModule.newName();\n            (modSpec || (modSpec = Object.create(null)))[\".\" + cls] = spec;\n            return cls;\n        }\n        const all = typeof options.all == \"string\" ? options.all : options.all ? def(options.all) : undefined;\n        const scopeOpt = options.scope;\n        this.scope = scopeOpt instanceof Language ? (type) => type.prop(languageDataProp) == scopeOpt.data\n            : scopeOpt ? (type) => type == scopeOpt : undefined;\n        this.style = tagHighlighter(specs.map(style => ({\n            tag: style.tag,\n            class: style.class || def(Object.assign({}, style, { tag: null }))\n        })), {\n            all,\n        }).style;\n        this.module = modSpec ? new StyleModule(modSpec) : null;\n        this.themeType = options.themeType;\n    }\n    /**\n    Create a highlighter style that associates the given styles to\n    the given tags. The specs must be objects that hold a style tag\n    or array of tags in their `tag` property, and either a single\n    `class` property providing a static CSS class (for highlighter\n    that rely on external styling), or a\n    [`style-mod`](https://github.com/marijnh/style-mod#documentation)-style\n    set of CSS properties (which define the styling for those tags).\n    \n    The CSS rules created for a highlighter will be emitted in the\n    order of the spec's properties. That means that for elements that\n    have multiple tags associated with them, styles defined further\n    down in the list will have a higher CSS precedence than styles\n    defined earlier.\n    */\n    static define(specs, options) {\n        return new HighlightStyle(specs, options || {});\n    }\n}\nconst highlighterFacet = /*@__PURE__*/Facet.define();\nconst fallbackHighlighter = /*@__PURE__*/Facet.define({\n    combine(values) { return values.length ? [values[0]] : null; }\n});\nfunction getHighlighters(state) {\n    let main = state.facet(highlighterFacet);\n    return main.length ? main : state.facet(fallbackHighlighter);\n}\n/**\nWrap a highlighter in an editor extension that uses it to apply\nsyntax highlighting to the editor content.\n\nWhen multiple (non-fallback) styles are provided, the styling\napplied is the union of the classes they emit.\n*/\nfunction syntaxHighlighting(highlighter, options) {\n    let ext = [treeHighlighter], themeType;\n    if (highlighter instanceof HighlightStyle) {\n        if (highlighter.module)\n            ext.push(EditorView.styleModule.of(highlighter.module));\n        themeType = highlighter.themeType;\n    }\n    if (options === null || options === void 0 ? void 0 : options.fallback)\n        ext.push(fallbackHighlighter.of(highlighter));\n    else if (themeType)\n        ext.push(highlighterFacet.computeN([EditorView.darkTheme], state => {\n            return state.facet(EditorView.darkTheme) == (themeType == \"dark\") ? [highlighter] : [];\n        }));\n    else\n        ext.push(highlighterFacet.of(highlighter));\n    return ext;\n}\n/**\nReturns the CSS classes (if any) that the highlighters active in\nthe state would assign to the given style\n[tags](https://lezer.codemirror.net/docs/ref#highlight.Tag) and\n(optional) language\n[scope](https://codemirror.net/6/docs/ref/#language.HighlightStyle^define^options.scope).\n*/\nfunction highlightingFor(state, tags, scope) {\n    let highlighters = getHighlighters(state);\n    let result = null;\n    if (highlighters)\n        for (let highlighter of highlighters) {\n            if (!highlighter.scope || scope && highlighter.scope(scope)) {\n                let cls = highlighter.style(tags);\n                if (cls)\n                    result = result ? result + \" \" + cls : cls;\n            }\n        }\n    return result;\n}\nclass TreeHighlighter {\n    constructor(view) {\n        this.markCache = Object.create(null);\n        this.tree = syntaxTree(view.state);\n        this.decorations = this.buildDeco(view, getHighlighters(view.state));\n        this.decoratedTo = view.viewport.to;\n    }\n    update(update) {\n        let tree = syntaxTree(update.state), highlighters = getHighlighters(update.state);\n        let styleChange = highlighters != getHighlighters(update.startState);\n        let { viewport } = update.view, decoratedToMapped = update.changes.mapPos(this.decoratedTo, 1);\n        if (tree.length < viewport.to && !styleChange && tree.type == this.tree.type && decoratedToMapped >= viewport.to) {\n            this.decorations = this.decorations.map(update.changes);\n            this.decoratedTo = decoratedToMapped;\n        }\n        else if (tree != this.tree || update.viewportChanged || styleChange) {\n            this.tree = tree;\n            this.decorations = this.buildDeco(update.view, highlighters);\n            this.decoratedTo = viewport.to;\n        }\n    }\n    buildDeco(view, highlighters) {\n        if (!highlighters || !this.tree.length)\n            return Decoration.none;\n        let builder = new RangeSetBuilder();\n        for (let { from, to } of view.visibleRanges) {\n            highlightTree(this.tree, highlighters, (from, to, style) => {\n                builder.add(from, to, this.markCache[style] || (this.markCache[style] = Decoration.mark({ class: style })));\n            }, from, to);\n        }\n        return builder.finish();\n    }\n}\nconst treeHighlighter = /*@__PURE__*/Prec.high(/*@__PURE__*/ViewPlugin.fromClass(TreeHighlighter, {\n    decorations: v => v.decorations\n}));\n/**\nA default highlight style (works well with light themes).\n*/\nconst defaultHighlightStyle = /*@__PURE__*/HighlightStyle.define([\n    { tag: tags.meta,\n        color: \"#404740\" },\n    { tag: tags.link,\n        textDecoration: \"underline\" },\n    { tag: tags.heading,\n        textDecoration: \"underline\",\n        fontWeight: \"bold\" },\n    { tag: tags.emphasis,\n        fontStyle: \"italic\" },\n    { tag: tags.strong,\n        fontWeight: \"bold\" },\n    { tag: tags.strikethrough,\n        textDecoration: \"line-through\" },\n    { tag: tags.keyword,\n        color: \"#708\" },\n    { tag: [tags.atom, tags.bool, tags.url, tags.contentSeparator, tags.labelName],\n        color: \"#219\" },\n    { tag: [tags.literal, tags.inserted],\n        color: \"#164\" },\n    { tag: [tags.string, tags.deleted],\n        color: \"#a11\" },\n    { tag: [tags.regexp, tags.escape, /*@__PURE__*/tags.special(tags.string)],\n        color: \"#e40\" },\n    { tag: /*@__PURE__*/tags.definition(tags.variableName),\n        color: \"#00f\" },\n    { tag: /*@__PURE__*/tags.local(tags.variableName),\n        color: \"#30a\" },\n    { tag: [tags.typeName, tags.namespace],\n        color: \"#085\" },\n    { tag: tags.className,\n        color: \"#167\" },\n    { tag: [/*@__PURE__*/tags.special(tags.variableName), tags.macroName],\n        color: \"#256\" },\n    { tag: /*@__PURE__*/tags.definition(tags.propertyName),\n        color: \"#00c\" },\n    { tag: tags.comment,\n        color: \"#940\" },\n    { tag: tags.invalid,\n        color: \"#f00\" }\n]);\n\nconst baseTheme = /*@__PURE__*/EditorView.baseTheme({\n    \"&.cm-focused .cm-matchingBracket\": { backgroundColor: \"#328c8252\" },\n    \"&.cm-focused .cm-nonmatchingBracket\": { backgroundColor: \"#bb555544\" }\n});\nconst DefaultScanDist = 10000, DefaultBrackets = \"()[]{}\";\nconst bracketMatchingConfig = /*@__PURE__*/Facet.define({\n    combine(configs) {\n        return combineConfig(configs, {\n            afterCursor: true,\n            brackets: DefaultBrackets,\n            maxScanDistance: DefaultScanDist,\n            renderMatch: defaultRenderMatch\n        });\n    }\n});\nconst matchingMark = /*@__PURE__*/Decoration.mark({ class: \"cm-matchingBracket\" }), nonmatchingMark = /*@__PURE__*/Decoration.mark({ class: \"cm-nonmatchingBracket\" });\nfunction defaultRenderMatch(match) {\n    let decorations = [];\n    let mark = match.matched ? matchingMark : nonmatchingMark;\n    decorations.push(mark.range(match.start.from, match.start.to));\n    if (match.end)\n        decorations.push(mark.range(match.end.from, match.end.to));\n    return decorations;\n}\nconst bracketMatchingState = /*@__PURE__*/StateField.define({\n    create() { return Decoration.none; },\n    update(deco, tr) {\n        if (!tr.docChanged && !tr.selection)\n            return deco;\n        let decorations = [];\n        let config = tr.state.facet(bracketMatchingConfig);\n        for (let range of tr.state.selection.ranges) {\n            if (!range.empty)\n                continue;\n            let match = matchBrackets(tr.state, range.head, -1, config)\n                || (range.head > 0 && matchBrackets(tr.state, range.head - 1, 1, config))\n                || (config.afterCursor &&\n                    (matchBrackets(tr.state, range.head, 1, config) ||\n                        (range.head < tr.state.doc.length && matchBrackets(tr.state, range.head + 1, -1, config))));\n            if (match)\n                decorations = decorations.concat(config.renderMatch(match, tr.state));\n        }\n        return Decoration.set(decorations, true);\n    },\n    provide: f => EditorView.decorations.from(f)\n});\nconst bracketMatchingUnique = [\n    bracketMatchingState,\n    baseTheme\n];\n/**\nCreate an extension that enables bracket matching. Whenever the\ncursor is next to a bracket, that bracket and the one it matches\nare highlighted. Or, when no matching bracket is found, another\nhighlighting style is used to indicate this.\n*/\nfunction bracketMatching(config = {}) {\n    return [bracketMatchingConfig.of(config), bracketMatchingUnique];\n}\n/**\nWhen larger syntax nodes, such as HTML tags, are marked as\nopening/closing, it can be a bit messy to treat the whole node as\na matchable bracket. This node prop allows you to define, for such\na node, a ‘handle’—the part of the node that is highlighted, and\nthat the cursor must be on to activate highlighting in the first\nplace.\n*/\nconst bracketMatchingHandle = /*@__PURE__*/new NodeProp();\nfunction matchingNodes(node, dir, brackets) {\n    let byProp = node.prop(dir < 0 ? NodeProp.openedBy : NodeProp.closedBy);\n    if (byProp)\n        return byProp;\n    if (node.name.length == 1) {\n        let index = brackets.indexOf(node.name);\n        if (index > -1 && index % 2 == (dir < 0 ? 1 : 0))\n            return [brackets[index + dir]];\n    }\n    return null;\n}\nfunction findHandle(node) {\n    let hasHandle = node.type.prop(bracketMatchingHandle);\n    return hasHandle ? hasHandle(node.node) : node;\n}\n/**\nFind the matching bracket for the token at `pos`, scanning\ndirection `dir`. Only the `brackets` and `maxScanDistance`\nproperties are used from `config`, if given. Returns null if no\nbracket was found at `pos`, or a match result otherwise.\n*/\nfunction matchBrackets(state, pos, dir, config = {}) {\n    let maxScanDistance = config.maxScanDistance || DefaultScanDist, brackets = config.brackets || DefaultBrackets;\n    let tree = syntaxTree(state), node = tree.resolveInner(pos, dir);\n    for (let cur = node; cur; cur = cur.parent) {\n        let matches = matchingNodes(cur.type, dir, brackets);\n        if (matches && cur.from < cur.to) {\n            let handle = findHandle(cur);\n            if (handle && (dir > 0 ? pos >= handle.from && pos < handle.to : pos > handle.from && pos <= handle.to))\n                return matchMarkedBrackets(state, pos, dir, cur, handle, matches, brackets);\n        }\n    }\n    return matchPlainBrackets(state, pos, dir, tree, node.type, maxScanDistance, brackets);\n}\nfunction matchMarkedBrackets(_state, _pos, dir, token, handle, matching, brackets) {\n    let parent = token.parent, firstToken = { from: handle.from, to: handle.to };\n    let depth = 0, cursor = parent === null || parent === void 0 ? void 0 : parent.cursor();\n    if (cursor && (dir < 0 ? cursor.childBefore(token.from) : cursor.childAfter(token.to)))\n        do {\n            if (dir < 0 ? cursor.to <= token.from : cursor.from >= token.to) {\n                if (depth == 0 && matching.indexOf(cursor.type.name) > -1 && cursor.from < cursor.to) {\n                    let endHandle = findHandle(cursor);\n                    return { start: firstToken, end: endHandle ? { from: endHandle.from, to: endHandle.to } : undefined, matched: true };\n                }\n                else if (matchingNodes(cursor.type, dir, brackets)) {\n                    depth++;\n                }\n                else if (matchingNodes(cursor.type, -dir, brackets)) {\n                    if (depth == 0) {\n                        let endHandle = findHandle(cursor);\n                        return {\n                            start: firstToken,\n                            end: endHandle && endHandle.from < endHandle.to ? { from: endHandle.from, to: endHandle.to } : undefined,\n                            matched: false\n                        };\n                    }\n                    depth--;\n                }\n            }\n        } while (dir < 0 ? cursor.prevSibling() : cursor.nextSibling());\n    return { start: firstToken, matched: false };\n}\nfunction matchPlainBrackets(state, pos, dir, tree, tokenType, maxScanDistance, brackets) {\n    let startCh = dir < 0 ? state.sliceDoc(pos - 1, pos) : state.sliceDoc(pos, pos + 1);\n    let bracket = brackets.indexOf(startCh);\n    if (bracket < 0 || (bracket % 2 == 0) != (dir > 0))\n        return null;\n    let startToken = { from: dir < 0 ? pos - 1 : pos, to: dir > 0 ? pos + 1 : pos };\n    let iter = state.doc.iterRange(pos, dir > 0 ? state.doc.length : 0), depth = 0;\n    for (let distance = 0; !(iter.next()).done && distance <= maxScanDistance;) {\n        let text = iter.value;\n        if (dir < 0)\n            distance += text.length;\n        let basePos = pos + distance * dir;\n        for (let pos = dir > 0 ? 0 : text.length - 1, end = dir > 0 ? text.length : -1; pos != end; pos += dir) {\n            let found = brackets.indexOf(text[pos]);\n            if (found < 0 || tree.resolveInner(basePos + pos, 1).type != tokenType)\n                continue;\n            if ((found % 2 == 0) == (dir > 0)) {\n                depth++;\n            }\n            else if (depth == 1) { // Closing\n                return { start: startToken, end: { from: basePos + pos, to: basePos + pos + 1 }, matched: (found >> 1) == (bracket >> 1) };\n            }\n            else {\n                depth--;\n            }\n        }\n        if (dir > 0)\n            distance += text.length;\n    }\n    return iter.done ? { start: startToken, matched: false } : null;\n}\n\n// Counts the column offset in a string, taking tabs into account.\n// Used mostly to find indentation.\nfunction countCol(string, end, tabSize, startIndex = 0, startValue = 0) {\n    if (end == null) {\n        end = string.search(/[^\\s\\u00a0]/);\n        if (end == -1)\n            end = string.length;\n    }\n    let n = startValue;\n    for (let i = startIndex; i < end; i++) {\n        if (string.charCodeAt(i) == 9)\n            n += tabSize - (n % tabSize);\n        else\n            n++;\n    }\n    return n;\n}\n/**\nEncapsulates a single line of input. Given to stream syntax code,\nwhich uses it to tokenize the content.\n*/\nclass StringStream {\n    /**\n    Create a stream.\n    */\n    constructor(\n    /**\n    The line.\n    */\n    string, tabSize, \n    /**\n    The current indent unit size.\n    */\n    indentUnit, overrideIndent) {\n        this.string = string;\n        this.tabSize = tabSize;\n        this.indentUnit = indentUnit;\n        this.overrideIndent = overrideIndent;\n        /**\n        The current position on the line.\n        */\n        this.pos = 0;\n        /**\n        The start position of the current token.\n        */\n        this.start = 0;\n        this.lastColumnPos = 0;\n        this.lastColumnValue = 0;\n    }\n    /**\n    True if we are at the end of the line.\n    */\n    eol() { return this.pos >= this.string.length; }\n    /**\n    True if we are at the start of the line.\n    */\n    sol() { return this.pos == 0; }\n    /**\n    Get the next code unit after the current position, or undefined\n    if we're at the end of the line.\n    */\n    peek() { return this.string.charAt(this.pos) || undefined; }\n    /**\n    Read the next code unit and advance `this.pos`.\n    */\n    next() {\n        if (this.pos < this.string.length)\n            return this.string.charAt(this.pos++);\n    }\n    /**\n    Match the next character against the given string, regular\n    expression, or predicate. Consume and return it if it matches.\n    */\n    eat(match) {\n        let ch = this.string.charAt(this.pos);\n        let ok;\n        if (typeof match == \"string\")\n            ok = ch == match;\n        else\n            ok = ch && (match instanceof RegExp ? match.test(ch) : match(ch));\n        if (ok) {\n            ++this.pos;\n            return ch;\n        }\n    }\n    /**\n    Continue matching characters that match the given string,\n    regular expression, or predicate function. Return true if any\n    characters were consumed.\n    */\n    eatWhile(match) {\n        let start = this.pos;\n        while (this.eat(match)) { }\n        return this.pos > start;\n    }\n    /**\n    Consume whitespace ahead of `this.pos`. Return true if any was\n    found.\n    */\n    eatSpace() {\n        let start = this.pos;\n        while (/[\\s\\u00a0]/.test(this.string.charAt(this.pos)))\n            ++this.pos;\n        return this.pos > start;\n    }\n    /**\n    Move to the end of the line.\n    */\n    skipToEnd() { this.pos = this.string.length; }\n    /**\n    Move to directly before the given character, if found on the\n    current line.\n    */\n    skipTo(ch) {\n        let found = this.string.indexOf(ch, this.pos);\n        if (found > -1) {\n            this.pos = found;\n            return true;\n        }\n    }\n    /**\n    Move back `n` characters.\n    */\n    backUp(n) { this.pos -= n; }\n    /**\n    Get the column position at `this.pos`.\n    */\n    column() {\n        if (this.lastColumnPos < this.start) {\n            this.lastColumnValue = countCol(this.string, this.start, this.tabSize, this.lastColumnPos, this.lastColumnValue);\n            this.lastColumnPos = this.start;\n        }\n        return this.lastColumnValue;\n    }\n    /**\n    Get the indentation column of the current line.\n    */\n    indentation() {\n        var _a;\n        return (_a = this.overrideIndent) !== null && _a !== void 0 ? _a : countCol(this.string, null, this.tabSize);\n    }\n    /**\n    Match the input against the given string or regular expression\n    (which should start with a `^`). Return true or the regexp match\n    if it matches.\n    \n    Unless `consume` is set to `false`, this will move `this.pos`\n    past the matched text.\n    \n    When matching a string `caseInsensitive` can be set to true to\n    make the match case-insensitive.\n    */\n    match(pattern, consume, caseInsensitive) {\n        if (typeof pattern == \"string\") {\n            let cased = (str) => caseInsensitive ? str.toLowerCase() : str;\n            let substr = this.string.substr(this.pos, pattern.length);\n            if (cased(substr) == cased(pattern)) {\n                if (consume !== false)\n                    this.pos += pattern.length;\n                return true;\n            }\n            else\n                return null;\n        }\n        else {\n            let match = this.string.slice(this.pos).match(pattern);\n            if (match && match.index > 0)\n                return null;\n            if (match && consume !== false)\n                this.pos += match[0].length;\n            return match;\n        }\n    }\n    /**\n    Get the current token.\n    */\n    current() { return this.string.slice(this.start, this.pos); }\n}\n\nfunction fullParser(spec) {\n    return {\n        name: spec.name || \"\",\n        token: spec.token,\n        blankLine: spec.blankLine || (() => { }),\n        startState: spec.startState || (() => true),\n        copyState: spec.copyState || defaultCopyState,\n        indent: spec.indent || (() => null),\n        languageData: spec.languageData || {},\n        tokenTable: spec.tokenTable || noTokens,\n        mergeTokens: spec.mergeTokens !== false\n    };\n}\nfunction defaultCopyState(state) {\n    if (typeof state != \"object\")\n        return state;\n    let newState = {};\n    for (let prop in state) {\n        let val = state[prop];\n        newState[prop] = (val instanceof Array ? val.slice() : val);\n    }\n    return newState;\n}\nconst IndentedFrom = /*@__PURE__*/new WeakMap();\n/**\nA [language](https://codemirror.net/6/docs/ref/#language.Language) class based on a CodeMirror\n5-style [streaming parser](https://codemirror.net/6/docs/ref/#language.StreamParser).\n*/\nclass StreamLanguage extends Language {\n    constructor(parser) {\n        let data = defineLanguageFacet(parser.languageData);\n        let p = fullParser(parser), self;\n        let impl = new class extends Parser {\n            createParse(input, fragments, ranges) {\n                return new Parse(self, input, fragments, ranges);\n            }\n        };\n        super(data, impl, [], parser.name);\n        this.topNode = docID(data, this);\n        self = this;\n        this.streamParser = p;\n        this.stateAfter = new NodeProp({ perNode: true });\n        this.tokenTable = parser.tokenTable ? new TokenTable(p.tokenTable) : defaultTokenTable;\n    }\n    /**\n    Define a stream language.\n    */\n    static define(spec) { return new StreamLanguage(spec); }\n    /**\n    @internal\n    */\n    getIndent(cx) {\n        let from = undefined;\n        let { overrideIndentation } = cx.options;\n        if (overrideIndentation) {\n            from = IndentedFrom.get(cx.state);\n            if (from != null && from < cx.pos - 1e4)\n                from = undefined;\n        }\n        let start = findState(this, cx.node.tree, cx.node.from, cx.node.from, from !== null && from !== void 0 ? from : cx.pos), statePos, state;\n        if (start) {\n            state = start.state;\n            statePos = start.pos + 1;\n        }\n        else {\n            state = this.streamParser.startState(cx.unit);\n            statePos = cx.node.from;\n        }\n        if (cx.pos - statePos > 10000 /* C.MaxIndentScanDist */)\n            return null;\n        while (statePos < cx.pos) {\n            let line = cx.state.doc.lineAt(statePos), end = Math.min(cx.pos, line.to);\n            if (line.length) {\n                let indentation = overrideIndentation ? overrideIndentation(line.from) : -1;\n                let stream = new StringStream(line.text, cx.state.tabSize, cx.unit, indentation < 0 ? undefined : indentation);\n                while (stream.pos < end - line.from)\n                    readToken(this.streamParser.token, stream, state);\n            }\n            else {\n                this.streamParser.blankLine(state, cx.unit);\n            }\n            if (end == cx.pos)\n                break;\n            statePos = line.to + 1;\n        }\n        let line = cx.lineAt(cx.pos);\n        if (overrideIndentation && from == null)\n            IndentedFrom.set(cx.state, line.from);\n        return this.streamParser.indent(state, /^\\s*(.*)/.exec(line.text)[1], cx);\n    }\n    get allowsNesting() { return false; }\n}\nfunction findState(lang, tree, off, startPos, before) {\n    let state = off >= startPos && off + tree.length <= before && tree.prop(lang.stateAfter);\n    if (state)\n        return { state: lang.streamParser.copyState(state), pos: off + tree.length };\n    for (let i = tree.children.length - 1; i >= 0; i--) {\n        let child = tree.children[i], pos = off + tree.positions[i];\n        let found = child instanceof Tree && pos < before && findState(lang, child, pos, startPos, before);\n        if (found)\n            return found;\n    }\n    return null;\n}\nfunction cutTree(lang, tree, from, to, inside) {\n    if (inside && from <= 0 && to >= tree.length)\n        return tree;\n    if (!inside && from == 0 && tree.type == lang.topNode)\n        inside = true;\n    for (let i = tree.children.length - 1; i >= 0; i--) {\n        let pos = tree.positions[i], child = tree.children[i], inner;\n        if (pos < to && child instanceof Tree) {\n            if (!(inner = cutTree(lang, child, from - pos, to - pos, inside)))\n                break;\n            return !inside ? inner\n                : new Tree(tree.type, tree.children.slice(0, i).concat(inner), tree.positions.slice(0, i + 1), pos + inner.length);\n        }\n    }\n    return null;\n}\nfunction findStartInFragments(lang, fragments, startPos, endPos, editorState) {\n    for (let f of fragments) {\n        let from = f.from + (f.openStart ? 25 : 0), to = f.to - (f.openEnd ? 25 : 0);\n        let found = from <= startPos && to > startPos && findState(lang, f.tree, 0 - f.offset, startPos, to), tree;\n        if (found && found.pos <= endPos && (tree = cutTree(lang, f.tree, startPos + f.offset, found.pos + f.offset, false)))\n            return { state: found.state, tree };\n    }\n    return { state: lang.streamParser.startState(editorState ? getIndentUnit(editorState) : 4), tree: Tree.empty };\n}\nclass Parse {\n    constructor(lang, input, fragments, ranges) {\n        this.lang = lang;\n        this.input = input;\n        this.fragments = fragments;\n        this.ranges = ranges;\n        this.stoppedAt = null;\n        this.chunks = [];\n        this.chunkPos = [];\n        this.chunk = [];\n        this.chunkReused = undefined;\n        this.rangeIndex = 0;\n        this.to = ranges[ranges.length - 1].to;\n        let context = ParseContext.get(), from = ranges[0].from;\n        let { state, tree } = findStartInFragments(lang, fragments, from, this.to, context === null || context === void 0 ? void 0 : context.state);\n        this.state = state;\n        this.parsedPos = this.chunkStart = from + tree.length;\n        for (let i = 0; i < tree.children.length; i++) {\n            this.chunks.push(tree.children[i]);\n            this.chunkPos.push(tree.positions[i]);\n        }\n        if (context && this.parsedPos < context.viewport.from - 100000 /* C.MaxDistanceBeforeViewport */ &&\n            ranges.some(r => r.from <= context.viewport.from && r.to >= context.viewport.from)) {\n            this.state = this.lang.streamParser.startState(getIndentUnit(context.state));\n            context.skipUntilInView(this.parsedPos, context.viewport.from);\n            this.parsedPos = context.viewport.from;\n        }\n        this.moveRangeIndex();\n    }\n    advance() {\n        let context = ParseContext.get();\n        let parseEnd = this.stoppedAt == null ? this.to : Math.min(this.to, this.stoppedAt);\n        let end = Math.min(parseEnd, this.chunkStart + 2048 /* C.ChunkSize */);\n        if (context)\n            end = Math.min(end, context.viewport.to);\n        while (this.parsedPos < end)\n            this.parseLine(context);\n        if (this.chunkStart < this.parsedPos)\n            this.finishChunk();\n        if (this.parsedPos >= parseEnd)\n            return this.finish();\n        if (context && this.parsedPos >= context.viewport.to) {\n            context.skipUntilInView(this.parsedPos, parseEnd);\n            return this.finish();\n        }\n        return null;\n    }\n    stopAt(pos) {\n        this.stoppedAt = pos;\n    }\n    lineAfter(pos) {\n        let chunk = this.input.chunk(pos);\n        if (!this.input.lineChunks) {\n            let eol = chunk.indexOf(\"\\n\");\n            if (eol > -1)\n                chunk = chunk.slice(0, eol);\n        }\n        else if (chunk == \"\\n\") {\n            chunk = \"\";\n        }\n        return pos + chunk.length <= this.to ? chunk : chunk.slice(0, this.to - pos);\n    }\n    nextLine() {\n        let from = this.parsedPos, line = this.lineAfter(from), end = from + line.length;\n        for (let index = this.rangeIndex;;) {\n            let rangeEnd = this.ranges[index].to;\n            if (rangeEnd >= end)\n                break;\n            line = line.slice(0, rangeEnd - (end - line.length));\n            index++;\n            if (index == this.ranges.length)\n                break;\n            let rangeStart = this.ranges[index].from;\n            let after = this.lineAfter(rangeStart);\n            line += after;\n            end = rangeStart + after.length;\n        }\n        return { line, end };\n    }\n    skipGapsTo(pos, offset, side) {\n        for (;;) {\n            let end = this.ranges[this.rangeIndex].to, offPos = pos + offset;\n            if (side > 0 ? end > offPos : end >= offPos)\n                break;\n            let start = this.ranges[++this.rangeIndex].from;\n            offset += start - end;\n        }\n        return offset;\n    }\n    moveRangeIndex() {\n        while (this.ranges[this.rangeIndex].to < this.parsedPos)\n            this.rangeIndex++;\n    }\n    emitToken(id, from, to, offset) {\n        let size = 4;\n        if (this.ranges.length > 1) {\n            offset = this.skipGapsTo(from, offset, 1);\n            from += offset;\n            let len0 = this.chunk.length;\n            offset = this.skipGapsTo(to, offset, -1);\n            to += offset;\n            size += this.chunk.length - len0;\n        }\n        let last = this.chunk.length - 4;\n        if (this.lang.streamParser.mergeTokens && size == 4 && last >= 0 &&\n            this.chunk[last] == id && this.chunk[last + 2] == from)\n            this.chunk[last + 2] = to;\n        else\n            this.chunk.push(id, from, to, size);\n        return offset;\n    }\n    parseLine(context) {\n        let { line, end } = this.nextLine(), offset = 0, { streamParser } = this.lang;\n        let stream = new StringStream(line, context ? context.state.tabSize : 4, context ? getIndentUnit(context.state) : 2);\n        if (stream.eol()) {\n            streamParser.blankLine(this.state, stream.indentUnit);\n        }\n        else {\n            while (!stream.eol()) {\n                let token = readToken(streamParser.token, stream, this.state);\n                if (token)\n                    offset = this.emitToken(this.lang.tokenTable.resolve(token), this.parsedPos + stream.start, this.parsedPos + stream.pos, offset);\n                if (stream.start > 10000 /* C.MaxLineLength */)\n                    break;\n            }\n        }\n        this.parsedPos = end;\n        this.moveRangeIndex();\n        if (this.parsedPos < this.to)\n            this.parsedPos++;\n    }\n    finishChunk() {\n        let tree = Tree.build({\n            buffer: this.chunk,\n            start: this.chunkStart,\n            length: this.parsedPos - this.chunkStart,\n            nodeSet,\n            topID: 0,\n            maxBufferLength: 2048 /* C.ChunkSize */,\n            reused: this.chunkReused\n        });\n        tree = new Tree(tree.type, tree.children, tree.positions, tree.length, [[this.lang.stateAfter, this.lang.streamParser.copyState(this.state)]]);\n        this.chunks.push(tree);\n        this.chunkPos.push(this.chunkStart - this.ranges[0].from);\n        this.chunk = [];\n        this.chunkReused = undefined;\n        this.chunkStart = this.parsedPos;\n    }\n    finish() {\n        return new Tree(this.lang.topNode, this.chunks, this.chunkPos, this.parsedPos - this.ranges[0].from).balance();\n    }\n}\nfunction readToken(token, stream, state) {\n    stream.start = stream.pos;\n    for (let i = 0; i < 10; i++) {\n        let result = token(stream, state);\n        if (stream.pos > stream.start)\n            return result;\n    }\n    throw new Error(\"Stream parser failed to advance stream.\");\n}\nconst noTokens = /*@__PURE__*/Object.create(null);\nconst typeArray = [NodeType.none];\nconst nodeSet = /*@__PURE__*/new NodeSet(typeArray);\nconst warned = [];\n// Cache of node types by name and tags\nconst byTag = /*@__PURE__*/Object.create(null);\nconst defaultTable = /*@__PURE__*/Object.create(null);\nfor (let [legacyName, name] of [\n    [\"variable\", \"variableName\"],\n    [\"variable-2\", \"variableName.special\"],\n    [\"string-2\", \"string.special\"],\n    [\"def\", \"variableName.definition\"],\n    [\"tag\", \"tagName\"],\n    [\"attribute\", \"attributeName\"],\n    [\"type\", \"typeName\"],\n    [\"builtin\", \"variableName.standard\"],\n    [\"qualifier\", \"modifier\"],\n    [\"error\", \"invalid\"],\n    [\"header\", \"heading\"],\n    [\"property\", \"propertyName\"]\n])\n    defaultTable[legacyName] = /*@__PURE__*/createTokenType(noTokens, name);\nclass TokenTable {\n    constructor(extra) {\n        this.extra = extra;\n        this.table = Object.assign(Object.create(null), defaultTable);\n    }\n    resolve(tag) {\n        return !tag ? 0 : this.table[tag] || (this.table[tag] = createTokenType(this.extra, tag));\n    }\n}\nconst defaultTokenTable = /*@__PURE__*/new TokenTable(noTokens);\nfunction warnForPart(part, msg) {\n    if (warned.indexOf(part) > -1)\n        return;\n    warned.push(part);\n    console.warn(msg);\n}\nfunction createTokenType(extra, tagStr) {\n    let tags$1 = [];\n    for (let name of tagStr.split(\" \")) {\n        let found = [];\n        for (let part of name.split(\".\")) {\n            let value = (extra[part] || tags[part]);\n            if (!value) {\n                warnForPart(part, `Unknown highlighting tag ${part}`);\n            }\n            else if (typeof value == \"function\") {\n                if (!found.length)\n                    warnForPart(part, `Modifier ${part} used at start of tag`);\n                else\n                    found = found.map(value);\n            }\n            else {\n                if (found.length)\n                    warnForPart(part, `Tag ${part} used as modifier`);\n                else\n                    found = Array.isArray(value) ? value : [value];\n            }\n        }\n        for (let tag of found)\n            tags$1.push(tag);\n    }\n    if (!tags$1.length)\n        return 0;\n    let name = tagStr.replace(/ /g, \"_\"), key = name + \" \" + tags$1.map(t => t.id);\n    let known = byTag[key];\n    if (known)\n        return known.id;\n    let type = byTag[key] = NodeType.define({\n        id: typeArray.length,\n        name,\n        props: [styleTags({ [name]: tags$1 })]\n    });\n    typeArray.push(type);\n    return type.id;\n}\nfunction docID(data, lang) {\n    let type = NodeType.define({ id: typeArray.length, name: \"Document\", props: [\n            languageDataProp.add(() => data),\n            indentNodeProp.add(() => cx => lang.getIndent(cx))\n        ], top: true });\n    typeArray.push(type);\n    return type;\n}\n\nfunction buildForLine(line) {\n    return line.length <= 4096 && /[\\u0590-\\u05f4\\u0600-\\u06ff\\u0700-\\u08ac\\ufb50-\\ufdff]/.test(line);\n}\nfunction textHasRTL(text) {\n    for (let i = text.iter(); !i.next().done;)\n        if (buildForLine(i.value))\n            return true;\n    return false;\n}\nfunction changeAddsRTL(change) {\n    let added = false;\n    change.iterChanges((fA, tA, fB, tB, ins) => {\n        if (!added && textHasRTL(ins))\n            added = true;\n    });\n    return added;\n}\nconst alwaysIsolate = /*@__PURE__*/Facet.define({ combine: values => values.some(x => x) });\n/**\nMake sure nodes\n[marked](https://lezer.codemirror.net/docs/ref/#common.NodeProp^isolate)\nas isolating for bidirectional text are rendered in a way that\nisolates them from the surrounding text.\n*/\nfunction bidiIsolates(options = {}) {\n    let extensions = [isolateMarks];\n    if (options.alwaysIsolate)\n        extensions.push(alwaysIsolate.of(true));\n    return extensions;\n}\nconst isolateMarks = /*@__PURE__*/ViewPlugin.fromClass(class {\n    constructor(view) {\n        this.always = view.state.facet(alwaysIsolate) ||\n            view.textDirection != Direction.LTR ||\n            view.state.facet(EditorView.perLineTextDirection);\n        this.hasRTL = !this.always && textHasRTL(view.state.doc);\n        this.tree = syntaxTree(view.state);\n        this.decorations = this.always || this.hasRTL ? buildDeco(view, this.tree, this.always) : Decoration.none;\n    }\n    update(update) {\n        let always = update.state.facet(alwaysIsolate) ||\n            update.view.textDirection != Direction.LTR ||\n            update.state.facet(EditorView.perLineTextDirection);\n        if (!always && !this.hasRTL && changeAddsRTL(update.changes))\n            this.hasRTL = true;\n        if (!always && !this.hasRTL)\n            return;\n        let tree = syntaxTree(update.state);\n        if (always != this.always || tree != this.tree || update.docChanged || update.viewportChanged) {\n            this.tree = tree;\n            this.always = always;\n            this.decorations = buildDeco(update.view, tree, always);\n        }\n    }\n}, {\n    provide: plugin => {\n        function access(view) {\n            var _a, _b;\n            return (_b = (_a = view.plugin(plugin)) === null || _a === void 0 ? void 0 : _a.decorations) !== null && _b !== void 0 ? _b : Decoration.none;\n        }\n        return [EditorView.outerDecorations.of(access),\n            Prec.lowest(EditorView.bidiIsolatedRanges.of(access))];\n    }\n});\nfunction buildDeco(view, tree, always) {\n    let deco = new RangeSetBuilder();\n    let ranges = view.visibleRanges;\n    if (!always)\n        ranges = clipRTLLines(ranges, view.state.doc);\n    for (let { from, to } of ranges) {\n        tree.iterate({\n            enter: node => {\n                let iso = node.type.prop(NodeProp.isolate);\n                if (iso)\n                    deco.add(node.from, node.to, marks[iso]);\n            },\n            from, to\n        });\n    }\n    return deco.finish();\n}\nfunction clipRTLLines(ranges, doc) {\n    let cur = doc.iter(), pos = 0, result = [], last = null;\n    for (let { from, to } of ranges) {\n        if (last && last.to > from) {\n            from = last.to;\n            if (from >= to)\n                continue;\n        }\n        if (pos + cur.value.length < from) {\n            cur.next(from - (pos + cur.value.length));\n            pos = from;\n        }\n        for (;;) {\n            let start = pos, end = pos + cur.value.length;\n            if (!cur.lineBreak && buildForLine(cur.value)) {\n                if (last && last.to > start - 10)\n                    last.to = Math.min(to, end);\n                else\n                    result.push(last = { from: start, to: Math.min(to, end) });\n            }\n            if (end >= to)\n                break;\n            pos = end;\n            cur.next();\n        }\n    }\n    return result;\n}\nconst marks = {\n    rtl: /*@__PURE__*/Decoration.mark({ class: \"cm-iso\", inclusive: true, attributes: { dir: \"rtl\" }, bidiIsolate: Direction.RTL }),\n    ltr: /*@__PURE__*/Decoration.mark({ class: \"cm-iso\", inclusive: true, attributes: { dir: \"ltr\" }, bidiIsolate: Direction.LTR }),\n    auto: /*@__PURE__*/Decoration.mark({ class: \"cm-iso\", inclusive: true, attributes: { dir: \"auto\" }, bidiIsolate: null })\n};\n\nexport { DocInput, HighlightStyle, IndentContext, LRLanguage, Language, LanguageDescription, LanguageSupport, ParseContext, StreamLanguage, StringStream, TreeIndentContext, bidiIsolates, bracketMatching, bracketMatchingHandle, codeFolding, continuedIndent, defaultHighlightStyle, defineLanguageFacet, delimitedIndent, ensureSyntaxTree, flatIndent, foldAll, foldCode, foldEffect, foldGutter, foldInside, foldKeymap, foldNodeProp, foldService, foldState, foldable, foldedRanges, forceParsing, getIndentUnit, getIndentation, highlightingFor, indentNodeProp, indentOnInput, indentRange, indentService, indentString, indentUnit, language, languageDataProp, matchBrackets, sublanguageProp, syntaxHighlighting, syntaxParserRunning, syntaxTree, syntaxTreeAvailable, toggleFold, unfoldAll, unfoldCode, unfoldEffect };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAGA,IAAM,sBAAsB;AAC5B,IAAI,aAAa;AACjB,IAAM,QAAN,MAAY;AAAA,EACR,YAAY,MAAM,IAAI;AAClB,SAAK,OAAO;AACZ,SAAK,KAAK;AAAA,EACd;AACJ;AAMA,IAAM,WAAN,MAAe;AAAA;AAAA;AAAA;AAAA,EAIX,YAAY,SAAS,CAAC,GAAG;AACrB,SAAK,KAAK;AACV,SAAK,UAAU,CAAC,CAAC,OAAO;AACxB,SAAK,cAAc,OAAO,gBAAgB,MAAM;AAC5C,YAAM,IAAI,MAAM,sDAAsD;AAAA,IAC1E;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,IAAI,OAAO;AACP,QAAI,KAAK;AACL,YAAM,IAAI,WAAW,wCAAwC;AACjE,QAAI,OAAO,SAAS;AAChB,cAAQ,SAAS,MAAM,KAAK;AAChC,WAAO,CAAC,SAAS;AACb,UAAI,SAAS,MAAM,IAAI;AACvB,aAAO,WAAW,SAAY,OAAO,CAAC,MAAM,MAAM;AAAA,IACtD;AAAA,EACJ;AACJ;AAOA,SAAS,WAAW,IAAI,SAAS,EAAE,aAAa,SAAO,IAAI,MAAM,GAAG,EAAE,CAAC;AAMvE,SAAS,WAAW,IAAI,SAAS,EAAE,aAAa,SAAO,IAAI,MAAM,GAAG,EAAE,CAAC;AAMvE,SAAS,QAAQ,IAAI,SAAS,EAAE,aAAa,SAAO,IAAI,MAAM,GAAG,EAAE,CAAC;AAYpE,SAAS,UAAU,IAAI,SAAS,EAAE,aAAa,WAAS;AAChD,MAAI,SAAS,SAAS,SAAS,SAAS,SAAS,SAAS;AACtD,UAAM,IAAI,WAAW,gCAAgC,KAAK;AAC9D,SAAO,SAAS;AACpB,EAAE,CAAC;AAMP,SAAS,cAAc,IAAI,SAAS,EAAE,SAAS,KAAK,CAAC;AAOrD,SAAS,YAAY,IAAI,SAAS,EAAE,SAAS,KAAK,CAAC;AAMnD,SAAS,UAAU,IAAI,SAAS,EAAE,SAAS,KAAK,CAAC;AAMjD,IAAM,cAAN,MAAkB;AAAA,EACd,YAIA,MAUA,SAIA,QAAQ;AACJ,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,IAAI,MAAM;AACb,WAAO,QAAQ,KAAK,SAAS,KAAK,MAAM,SAAS,QAAQ,EAAE;AAAA,EAC/D;AACJ;AACA,IAAM,UAAU,uBAAO,OAAO,IAAI;AAIlC,IAAM,WAAN,MAAM,UAAS;AAAA;AAAA;AAAA;AAAA,EAIX,YAOAA,OAIA,OAKA,IAIA,QAAQ,GAAG;AACP,SAAK,OAAOA;AACZ,SAAK,QAAQ;AACb,SAAK,KAAK;AACV,SAAK,QAAQ;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,OAAO,MAAM;AAChB,QAAI,QAAQ,KAAK,SAAS,KAAK,MAAM,SAAS,uBAAO,OAAO,IAAI,IAAI;AACpE,QAAI,SAAS,KAAK,MAAM,IAAuB,MAAM,KAAK,UAAU,IAA2B,MAC1F,KAAK,QAAQ,IAAyB,MAAM,KAAK,QAAQ,OAAO,IAA6B;AAClG,QAAI,OAAO,IAAI,UAAS,KAAK,QAAQ,IAAI,OAAO,KAAK,IAAI,KAAK;AAC9D,QAAI,KAAK;AACL,eAAS,OAAO,KAAK,OAAO;AACxB,YAAI,CAAC,MAAM,QAAQ,GAAG;AAClB,gBAAM,IAAI,IAAI;AAClB,YAAI,KAAK;AACL,cAAI,IAAI,CAAC,EAAE;AACP,kBAAM,IAAI,WAAW,4CAA4C;AACrE,gBAAM,IAAI,CAAC,EAAE,EAAE,IAAI,IAAI,CAAC;AAAA,QAC5B;AAAA,MACJ;AACJ,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,MAAM;AAAE,WAAO,KAAK,MAAM,KAAK,EAAE;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAIzC,IAAI,QAAQ;AAAE,YAAQ,KAAK,QAAQ,KAAwB;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAI9D,IAAI,YAAY;AAAE,YAAQ,KAAK,QAAQ,KAA4B;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAItE,IAAI,UAAU;AAAE,YAAQ,KAAK,QAAQ,KAA0B;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlE,IAAI,cAAc;AAAE,YAAQ,KAAK,QAAQ,KAA8B;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1E,GAAGA,OAAM;AACL,QAAI,OAAOA,SAAQ,UAAU;AACzB,UAAI,KAAK,QAAQA;AACb,eAAO;AACX,UAAI,QAAQ,KAAK,KAAK,SAAS,KAAK;AACpC,aAAO,QAAQ,MAAM,QAAQA,KAAI,IAAI,KAAK;AAAA,IAC9C;AACA,WAAO,KAAK,MAAMA;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,MAAM,KAAK;AACd,QAAI,SAAS,uBAAO,OAAO,IAAI;AAC/B,aAAS,QAAQ;AACb,eAASA,SAAQ,KAAK,MAAM,GAAG;AAC3B,eAAOA,KAAI,IAAI,IAAI,IAAI;AAC/B,WAAO,CAAC,SAAS;AACb,eAAS,SAAS,KAAK,KAAK,SAAS,KAAK,GAAG,IAAI,IAAI,KAAK,SAAS,OAAO,SAAS,IAAI,KAAK;AACxF,YAAI,QAAQ,OAAO,IAAI,IAAI,KAAK,OAAO,OAAO,CAAC,CAAC;AAChD,YAAI;AACA,iBAAO;AAAA,MACf;AAAA,IACJ;AAAA,EACJ;AACJ;AAIA,SAAS,OAAO,IAAI;AAAA,EAAS;AAAA,EAAI,uBAAO,OAAO,IAAI;AAAA,EAAG;AAAA,EAAG;AAAA;AAA0B;AAUnF,IAAM,UAAN,MAAM,SAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKV,YAIA,OAAO;AACH,SAAK,QAAQ;AACb,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAC9B,UAAI,MAAM,CAAC,EAAE,MAAM;AACf,cAAM,IAAI,WAAW,6EAA6E;AAAA,EAC9G;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,OAAO;AACb,QAAI,WAAW,CAAC;AAChB,aAAS,QAAQ,KAAK,OAAO;AACzB,UAAI,WAAW;AACf,eAAS,UAAU,OAAO;AACtB,YAAI,MAAM,OAAO,IAAI;AACrB,YAAI,KAAK;AACL,cAAI,CAAC;AACD,uBAAW,OAAO,OAAO,CAAC,GAAG,KAAK,KAAK;AAC3C,mBAAS,IAAI,CAAC,EAAE,EAAE,IAAI,IAAI,CAAC;AAAA,QAC/B;AAAA,MACJ;AACA,eAAS,KAAK,WAAW,IAAI,SAAS,KAAK,MAAM,UAAU,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI;AAAA,IAC1F;AACA,WAAO,IAAI,SAAQ,QAAQ;AAAA,EAC/B;AACJ;AACA,IAAM,aAAa,oBAAI,QAAQ;AAA/B,IAAkC,kBAAkB,oBAAI,QAAQ;AAKhE,IAAI;AAAA,CACH,SAAUC,WAAU;AAMjB,EAAAA,UAASA,UAAS,gBAAgB,IAAI,CAAC,IAAI;AAM3C,EAAAA,UAASA,UAAS,kBAAkB,IAAI,CAAC,IAAI;AAM7C,EAAAA,UAASA,UAAS,cAAc,IAAI,CAAC,IAAI;AAOzC,EAAAA,UAASA,UAAS,gBAAgB,IAAI,CAAC,IAAI;AAC/C,GAAG,aAAa,WAAW,CAAC,EAAE;AAiB9B,IAAM,OAAN,MAAM,MAAK;AAAA;AAAA;AAAA;AAAA,EAIP,YAIA,MAIA,UAKA,WAIA,QAIA,OAAO;AACH,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,SAAS;AAId,SAAK,QAAQ;AACb,QAAI,SAAS,MAAM,QAAQ;AACvB,WAAK,QAAQ,uBAAO,OAAO,IAAI;AAC/B,eAAS,CAAC,MAAM,KAAK,KAAK;AACtB,aAAK,MAAM,OAAO,QAAQ,WAAW,OAAO,KAAK,EAAE,IAAI;AAAA,IAC/D;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACP,QAAI,UAAU,YAAY,IAAI,IAAI;AAClC,QAAI,WAAW,CAAC,QAAQ;AACpB,aAAO,QAAQ,KAAK,SAAS;AACjC,QAAI,WAAW;AACf,aAAS,MAAM,KAAK,UAAU;AAC1B,UAAI,MAAM,GAAG,SAAS;AACtB,UAAI,KAAK;AACL,YAAI;AACA,sBAAY;AAChB,oBAAY;AAAA,MAChB;AAAA,IACJ;AACA,WAAO,CAAC,KAAK,KAAK,OAAO,YACpB,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,CAAC,KAAK,KAAK,UAAU,KAAK,UAAU,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SACzF,SAAS,SAAS,MAAM,WAAW,MAAM;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,OAAO,GAAG;AACb,WAAO,IAAI,WAAW,KAAK,SAAS,IAAI;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,KAAK,OAAO,GAAG,OAAO,GAAG;AAC9B,QAAI,QAAQ,WAAW,IAAI,IAAI,KAAK,KAAK;AACzC,QAAI,SAAS,IAAI,WAAW,KAAK;AACjC,WAAO,OAAO,KAAK,IAAI;AACvB,eAAW,IAAI,MAAM,OAAO,KAAK;AACjC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,UAAU;AACV,WAAO,IAAI,SAAS,MAAM,GAAG,GAAG,IAAI;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,QAAQ,KAAK,OAAO,GAAG;AACnB,QAAI,OAAO,YAAY,WAAW,IAAI,IAAI,KAAK,KAAK,SAAS,KAAK,MAAM,KAAK;AAC7E,eAAW,IAAI,MAAM,IAAI;AACzB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa,KAAK,OAAO,GAAG;AACxB,QAAI,OAAO,YAAY,gBAAgB,IAAI,IAAI,KAAK,KAAK,SAAS,KAAK,MAAM,IAAI;AACjF,oBAAgB,IAAI,MAAM,IAAI;AAC9B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,aAAa,KAAK,OAAO,GAAG;AACxB,WAAO,cAAc,MAAM,KAAK,IAAI;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ,MAAM;AACV,QAAI,EAAE,OAAO,OAAO,OAAO,GAAG,KAAK,KAAK,OAAO,IAAI;AACnD,QAAI,OAAO,KAAK,QAAQ,GAAG,QAAQ,OAAO,SAAS,oBAAoB;AACvE,aAAS,IAAI,KAAK,OAAO,OAAO,SAAS,gBAAgB,OAAK;AAC1D,UAAI,UAAU;AACd,UAAI,EAAE,QAAQ,MAAM,EAAE,MAAM,SAAS,CAAC,QAAQ,EAAE,KAAK,eAAe,MAAM,CAAC,MAAM,QAAQ;AACrF,YAAI,EAAE,WAAW;AACb;AACJ,kBAAU;AAAA,MACd;AACA,iBAAS;AACL,YAAI,WAAW,UAAU,QAAQ,CAAC,EAAE,KAAK;AACrC,gBAAM,CAAC;AACX,YAAI,EAAE,YAAY;AACd;AACJ,YAAI,CAAC,EAAE,OAAO;AACV;AACJ,kBAAU;AAAA,MACd;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,MAAM;AACP,WAAO,CAAC,KAAK,UAAU,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,QAAQ,KAAK,MAAM,KAAK,EAAE,IAAI;AAAA,EACrF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,aAAa;AACb,QAAI,SAAS,CAAC;AACd,QAAI,KAAK;AACL,eAAS,MAAM,KAAK;AAChB,eAAO,KAAK,CAAC,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC,CAAC;AACzC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,SAAS,CAAC,GAAG;AACjB,WAAO,KAAK,SAAS,UAAU,IAA+B,OAC1D,aAAa,SAAS,MAAM,KAAK,UAAU,KAAK,WAAW,GAAG,KAAK,SAAS,QAAQ,GAAG,KAAK,QAAQ,CAAC,UAAU,WAAW,WAAW,IAAI,MAAK,KAAK,MAAM,UAAU,WAAW,QAAQ,KAAK,UAAU,GAAG,OAAO,aAAa,CAAC,UAAU,WAAW,WAAW,IAAI,MAAK,SAAS,MAAM,UAAU,WAAW,MAAM,EAAE;AAAA,EAC1T;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,MAAM,MAAM;AAAE,WAAO,UAAU,IAAI;AAAA,EAAG;AACjD;AAIA,KAAK,QAAQ,IAAI,KAAK,SAAS,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;AAC9C,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EACnB,YAAY,QAAQ,OAAO;AACvB,SAAK,SAAS;AACd,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,IAAI,KAAK;AAAE,WAAO,KAAK,OAAO,KAAK,QAAQ,CAAC;AAAA,EAAG;AAAA,EAC/C,IAAI,QAAQ;AAAE,WAAO,KAAK,OAAO,KAAK,QAAQ,CAAC;AAAA,EAAG;AAAA,EAClD,IAAI,MAAM;AAAE,WAAO,KAAK,OAAO,KAAK,QAAQ,CAAC;AAAA,EAAG;AAAA,EAChD,IAAI,OAAO;AAAE,WAAO,KAAK,OAAO,KAAK,QAAQ,CAAC;AAAA,EAAG;AAAA,EACjD,IAAI,MAAM;AAAE,WAAO,KAAK;AAAA,EAAO;AAAA,EAC/B,OAAO;AAAE,SAAK,SAAS;AAAA,EAAG;AAAA,EAC1B,OAAO;AAAE,WAAO,IAAI,kBAAiB,KAAK,QAAQ,KAAK,KAAK;AAAA,EAAG;AACnE;AAOA,IAAM,aAAN,MAAM,YAAW;AAAA;AAAA;AAAA;AAAA,EAIb,YAIA,QAIA,QAIA,KAAK;AACD,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,MAAM;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,OAAO;AAAE,WAAO,SAAS;AAAA,EAAM;AAAA;AAAA;AAAA;AAAA,EAInC,WAAW;AACP,QAAI,SAAS,CAAC;AACd,aAAS,QAAQ,GAAG,QAAQ,KAAK,OAAO,UAAS;AAC7C,aAAO,KAAK,KAAK,YAAY,KAAK,CAAC;AACnC,cAAQ,KAAK,OAAO,QAAQ,CAAC;AAAA,IACjC;AACA,WAAO,OAAO,KAAK,GAAG;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,OAAO;AACf,QAAI,KAAK,KAAK,OAAO,KAAK,GAAG,WAAW,KAAK,OAAO,QAAQ,CAAC;AAC7D,QAAI,OAAO,KAAK,IAAI,MAAM,EAAE,GAAG,SAAS,KAAK;AAC7C,QAAI,KAAK,KAAK,MAAM,KAAK,CAAC,KAAK;AAC3B,eAAS,KAAK,UAAU,MAAM;AAClC,aAAS;AACT,QAAI,YAAY;AACZ,aAAO;AACX,QAAI,WAAW,CAAC;AAChB,WAAO,QAAQ,UAAU;AACrB,eAAS,KAAK,KAAK,YAAY,KAAK,CAAC;AACrC,cAAQ,KAAK,OAAO,QAAQ,CAAC;AAAA,IACjC;AACA,WAAO,SAAS,MAAM,SAAS,KAAK,GAAG,IAAI;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU,YAAY,UAAU,KAAK,KAAK,MAAM;AAC5C,QAAI,EAAE,OAAO,IAAI,MAAM,OAAO;AAC9B,aAAS,IAAI,YAAY,KAAK,UAAU,IAAI,OAAO,IAAI,CAAC,GAAG;AACvD,UAAI,UAAU,MAAM,KAAK,OAAO,IAAI,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC,GAAG;AACpD,eAAO;AACP,YAAI,MAAM;AACN;AAAA,MACR;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,QAAQ,MAAM,MAAM;AACtB,QAAI,IAAI,KAAK;AACb,QAAI,OAAO,IAAI,YAAY,OAAO,MAAM,GAAG,MAAM;AACjD,aAAS,IAAI,QAAQ,IAAI,GAAG,IAAI,QAAO;AACnC,WAAK,GAAG,IAAI,EAAE,GAAG;AACjB,WAAK,GAAG,IAAI,EAAE,GAAG,IAAI;AACrB,UAAI,KAAK,KAAK,GAAG,IAAI,EAAE,GAAG,IAAI;AAC9B,WAAK,GAAG,IAAI,EAAE,GAAG,IAAI;AACrB,YAAM,KAAK,IAAI,KAAK,EAAE;AAAA,IAC1B;AACA,WAAO,IAAI,YAAW,MAAM,KAAK,KAAK,GAAG;AAAA,EAC7C;AACJ;AACA,SAAS,UAAU,MAAM,KAAK,MAAM,IAAI;AACpC,UAAQ,MAAM;AAAA,IACV,KAAK;AAAsB,aAAO,OAAO;AAAA,IACzC,KAAK;AAA0B,aAAO,MAAM,OAAO,OAAO;AAAA,IAC1D,KAAK;AAAqB,aAAO,OAAO,OAAO,KAAK;AAAA,IACpD,KAAK;AAAwB,aAAO,QAAQ,OAAO,KAAK;AAAA,IACxD,KAAK;AAAoB,aAAO,KAAK;AAAA,IACrC,KAAK;AAAuB,aAAO;AAAA,EACvC;AACJ;AACA,SAAS,YAAY,MAAM,KAAK,MAAM,UAAU;AAC5C,MAAIC;AAEJ,SAAO,KAAK,QAAQ,KAAK,OACpB,OAAO,IAAI,KAAK,QAAQ,MAAM,KAAK,OAAO,SAC1C,OAAO,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,MAAM;AAC9C,QAAI,SAAS,CAAC,YAAY,gBAAgB,YAAY,KAAK,QAAQ,IAAI,OAAO,KAAK;AACnF,QAAI,CAAC;AACD,aAAO;AACX,WAAO;AAAA,EACX;AACA,MAAI,OAAO,WAAW,IAAI,SAAS;AAEnC,MAAI;AACA,aAAS,OAAO,MAAM,SAAS,KAAK,QAAQ,QAAQ,OAAO,QAAQ,SAAS,KAAK,QAAQ;AACrF,UAAI,gBAAgB,YAAY,KAAK,QAAQ,OAAOA,MAAK,OAAO,MAAM,KAAK,MAAM,IAAI,OAAO,QAAQA,QAAO,SAAS,SAASA,IAAG,SAAS,KAAK;AAC1I,eAAO;AAAA,IACf;AACJ,aAAS;AACL,QAAI,QAAQ,KAAK,MAAM,KAAK,MAAM,IAAI;AACtC,QAAI,CAAC;AACD,aAAO;AACX,WAAO;AAAA,EACX;AACJ;AACA,IAAM,WAAN,MAAe;AAAA,EACX,OAAO,OAAO,GAAG;AAAE,WAAO,IAAI,WAAW,MAAM,IAAI;AAAA,EAAG;AAAA,EACtD,SAAS,MAAM,SAAS,MAAM,QAAQ,MAAM;AACxC,QAAI,IAAI,YAAY,MAAM,MAAM,QAAQ,KAAK;AAC7C,WAAO,EAAE,SAAS,EAAE,CAAC,IAAI;AAAA,EAC7B;AAAA,EACA,YAAY,MAAM,SAAS,MAAM,QAAQ,MAAM;AAC3C,WAAO,YAAY,MAAM,MAAM,QAAQ,KAAK;AAAA,EAChD;AAAA,EACA,QAAQ,KAAK,OAAO,GAAG;AACnB,WAAO,YAAY,MAAM,KAAK,MAAM,KAAK;AAAA,EAC7C;AAAA,EACA,aAAa,KAAK,OAAO,GAAG;AACxB,WAAO,YAAY,MAAM,KAAK,MAAM,IAAI;AAAA,EAC5C;AAAA,EACA,aAAa,SAAS;AAClB,WAAO,iBAAiB,KAAK,QAAQ,OAAO;AAAA,EAChD;AAAA,EACA,2BAA2B,KAAK;AAC5B,QAAI,OAAO,KAAK,YAAY,GAAG,GAAG,OAAO;AACzC,WAAO,MAAM;AACT,UAAI,OAAO,KAAK;AAChB,UAAI,CAAC,QAAQ,KAAK,MAAM,KAAK;AACzB;AACJ,UAAI,KAAK,KAAK,WAAW,KAAK,QAAQ,KAAK,IAAI;AAC3C,eAAO;AACP,eAAO,KAAK;AAAA,MAChB,OACK;AACD,eAAO;AAAA,MACX;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EACA,IAAI,OAAO;AAAE,WAAO;AAAA,EAAM;AAAA,EAC1B,IAAI,OAAO;AAAE,WAAO,KAAK;AAAA,EAAQ;AACrC;AACA,IAAM,WAAN,MAAM,kBAAiB,SAAS;AAAA,EAC5B,YAAY,OAAO,MAEnB,OAAO,SAAS;AACZ,UAAM;AACN,SAAK,QAAQ;AACb,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,IAAI,OAAO;AAAE,WAAO,KAAK,MAAM;AAAA,EAAM;AAAA,EACrC,IAAI,OAAO;AAAE,WAAO,KAAK,MAAM,KAAK;AAAA,EAAM;AAAA,EAC1C,IAAI,KAAK;AAAE,WAAO,KAAK,OAAO,KAAK,MAAM;AAAA,EAAQ;AAAA,EACjD,UAAU,GAAG,KAAK,KAAK,MAAM,OAAO,GAAG;AACnC,aAAS,SAAS,UAAQ;AACtB,eAAS,EAAE,UAAU,UAAU,IAAI,OAAO,OAAO,IAAI,MAAM,IAAI,SAAS,SAAS,IAAI,KAAK,GAAG,KAAK,KAAK;AACnG,YAAI,OAAO,SAAS,CAAC,GAAG,QAAQ,UAAU,CAAC,IAAI,OAAO;AACtD,YAAI,CAAC,UAAU,MAAM,KAAK,OAAO,QAAQ,KAAK,MAAM;AAChD;AACJ,YAAI,gBAAgB,YAAY;AAC5B,cAAI,OAAO,SAAS;AAChB;AACJ,cAAI,QAAQ,KAAK,UAAU,GAAG,KAAK,OAAO,QAAQ,KAAK,MAAM,OAAO,IAAI;AACxE,cAAI,QAAQ;AACR,mBAAO,IAAI,WAAW,IAAI,cAAc,QAAQ,MAAM,GAAG,KAAK,GAAG,MAAM,KAAK;AAAA,QACpF,WACU,OAAO,SAAS,qBAAsB,CAAC,KAAK,KAAK,eAAe,SAAS,IAAI,IAAI;AACvF,cAAI;AACJ,cAAI,EAAE,OAAO,SAAS,kBAAkB,UAAU,YAAY,IAAI,IAAI,MAAM,CAAC,QAAQ;AACjF,mBAAO,IAAI,UAAS,QAAQ,MAAM,OAAO,GAAG,MAAM;AACtD,cAAI,QAAQ,IAAI,UAAS,MAAM,OAAO,GAAG,MAAM;AAC/C,iBAAQ,OAAO,SAAS,oBAAqB,CAAC,MAAM,KAAK,cAAc,QACjE,MAAM,UAAU,MAAM,IAAI,KAAK,SAAS,SAAS,IAAI,GAAG,KAAK,KAAK,IAAI;AAAA,QAChF;AAAA,MACJ;AACA,UAAK,OAAO,SAAS,oBAAqB,CAAC,OAAO,KAAK;AACnD,eAAO;AACX,UAAI,OAAO,SAAS;AAChB,YAAI,OAAO,QAAQ;AAAA;AAEnB,YAAI,MAAM,IAAI,KAAK,OAAO,QAAQ,MAAM,SAAS;AACrD,eAAS,OAAO;AAChB,UAAI,CAAC;AACD,eAAO;AAAA,IACf;AAAA,EACJ;AAAA,EACA,IAAI,aAAa;AAAE,WAAO,KAAK;AAAA,MAAU;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA;AAAA,IAAqB;AAAA,EAAG;AAAA,EAC1E,IAAI,YAAY;AAAE,WAAO,KAAK;AAAA,MAAU,KAAK,MAAM,SAAS,SAAS;AAAA,MAAG;AAAA,MAAI;AAAA,MAAG;AAAA;AAAA,IAAqB;AAAA,EAAG;AAAA,EACvG,WAAW,KAAK;AAAE,WAAO,KAAK;AAAA,MAAU;AAAA,MAAG;AAAA,MAAG;AAAA,MAAK;AAAA;AAAA,IAAkB;AAAA,EAAG;AAAA,EACxE,YAAY,KAAK;AAAE,WAAO,KAAK;AAAA,MAAU,KAAK,MAAM,SAAS,SAAS;AAAA,MAAG;AAAA,MAAI;AAAA,MAAK;AAAA;AAAA,IAAoB;AAAA,EAAG;AAAA,EACzG,MAAM,KAAK,MAAM,OAAO,GAAG;AACvB,QAAI;AACJ,QAAI,EAAE,OAAO,SAAS,oBAAoB,UAAU,YAAY,IAAI,KAAK,KAAK,MAAM,QAAQ,SAAS;AACjG,UAAI,OAAO,MAAM,KAAK;AACtB,eAAS,EAAE,MAAM,GAAG,KAAK,QAAQ,SAAS;AACtC,aAAK,OAAO,IAAI,QAAQ,OAAO,OAAO,UACjC,OAAO,IAAI,MAAM,OAAO,KAAK;AAC9B,iBAAO,IAAI,UAAS,QAAQ,MAAM,QAAQ,QAAQ,CAAC,EAAE,OAAO,KAAK,MAAM,IAAI,IAAI;AAAA,MACvF;AAAA,IACJ;AACA,WAAO,KAAK,UAAU,GAAG,GAAG,KAAK,MAAM,IAAI;AAAA,EAC/C;AAAA,EACA,wBAAwB;AACpB,QAAI,MAAM;AACV,WAAO,IAAI,KAAK,eAAe,IAAI;AAC/B,YAAM,IAAI;AACd,WAAO;AAAA,EACX;AAAA,EACA,IAAI,SAAS;AACT,WAAO,KAAK,UAAU,KAAK,QAAQ,sBAAsB,IAAI;AAAA,EACjE;AAAA,EACA,IAAI,cAAc;AACd,WAAO,KAAK,WAAW,KAAK,SAAS,IAAI,KAAK,QAAQ;AAAA,MAAU,KAAK,QAAQ;AAAA,MAAG;AAAA,MAAG;AAAA,MAAG;AAAA;AAAA,IAAqB,IAAI;AAAA,EACnH;AAAA,EACA,IAAI,cAAc;AACd,WAAO,KAAK,WAAW,KAAK,SAAS,IAAI,KAAK,QAAQ;AAAA,MAAU,KAAK,QAAQ;AAAA,MAAG;AAAA,MAAI;AAAA,MAAG;AAAA;AAAA,IAAqB,IAAI;AAAA,EACpH;AAAA,EACA,IAAI,OAAO;AAAE,WAAO,KAAK;AAAA,EAAO;AAAA,EAChC,SAAS;AAAE,WAAO,KAAK;AAAA,EAAO;AAAA;AAAA;AAAA;AAAA,EAI9B,WAAW;AAAE,WAAO,KAAK,MAAM,SAAS;AAAA,EAAG;AAC/C;AACA,SAAS,YAAY,MAAM,MAAM,QAAQ,OAAO;AAC5C,MAAI,MAAM,KAAK,OAAO,GAAG,SAAS,CAAC;AACnC,MAAI,CAAC,IAAI,WAAW;AAChB,WAAO;AACX,MAAI,UAAU;AACV,aAAS,QAAQ,OAAO,CAAC,SAAQ;AAC7B,cAAQ,IAAI,KAAK,GAAG,MAAM;AAC1B,UAAI,CAAC,IAAI,YAAY;AACjB,eAAO;AAAA,IACf;AACJ,aAAS;AACL,QAAI,SAAS,QAAQ,IAAI,KAAK,GAAG,KAAK;AAClC,aAAO;AACX,QAAI,IAAI,KAAK,GAAG,IAAI;AAChB,aAAO,KAAK,IAAI,IAAI;AACxB,QAAI,CAAC,IAAI,YAAY;AACjB,aAAO,SAAS,OAAO,SAAS,CAAC;AAAA,EACzC;AACJ;AACA,SAAS,iBAAiB,MAAM,SAAS,IAAI,QAAQ,SAAS,GAAG;AAC7D,WAAS,IAAI,MAAM,KAAK,GAAG,IAAI,EAAE,QAAQ;AACrC,QAAI,CAAC;AACD,aAAO;AACX,QAAI,CAAC,EAAE,KAAK,aAAa;AACrB,UAAI,QAAQ,CAAC,KAAK,QAAQ,CAAC,KAAK,EAAE;AAC9B,eAAO;AACX;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAM,gBAAN,MAAoB;AAAA,EAChB,YAAY,QAAQ,QAAQ,OAAO,OAAO;AACtC,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,QAAQ;AAAA,EACjB;AACJ;AACA,IAAM,aAAN,MAAM,oBAAmB,SAAS;AAAA,EAC9B,IAAI,OAAO;AAAE,WAAO,KAAK,KAAK;AAAA,EAAM;AAAA,EACpC,IAAI,OAAO;AAAE,WAAO,KAAK,QAAQ,QAAQ,KAAK,QAAQ,OAAO,OAAO,KAAK,QAAQ,CAAC;AAAA,EAAG;AAAA,EACrF,IAAI,KAAK;AAAE,WAAO,KAAK,QAAQ,QAAQ,KAAK,QAAQ,OAAO,OAAO,KAAK,QAAQ,CAAC;AAAA,EAAG;AAAA,EACnF,YAAY,SAAS,SAAS,OAAO;AACjC,UAAM;AACN,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,OAAO,QAAQ,OAAO,IAAI,MAAM,QAAQ,OAAO,OAAO,KAAK,CAAC;AAAA,EACrE;AAAA,EACA,MAAM,KAAK,KAAK,MAAM;AAClB,QAAI,EAAE,OAAO,IAAI,KAAK;AACtB,QAAI,QAAQ,OAAO,UAAU,KAAK,QAAQ,GAAG,OAAO,OAAO,KAAK,QAAQ,CAAC,GAAG,KAAK,MAAM,KAAK,QAAQ,OAAO,IAAI;AAC/G,WAAO,QAAQ,IAAI,OAAO,IAAI,YAAW,KAAK,SAAS,MAAM,KAAK;AAAA,EACtE;AAAA,EACA,IAAI,aAAa;AAAE,WAAO,KAAK;AAAA,MAAM;AAAA,MAAG;AAAA,MAAG;AAAA;AAAA,IAAqB;AAAA,EAAG;AAAA,EACnE,IAAI,YAAY;AAAE,WAAO,KAAK;AAAA,MAAM;AAAA,MAAI;AAAA,MAAG;AAAA;AAAA,IAAqB;AAAA,EAAG;AAAA,EACnE,WAAW,KAAK;AAAE,WAAO,KAAK;AAAA,MAAM;AAAA,MAAG;AAAA,MAAK;AAAA;AAAA,IAAkB;AAAA,EAAG;AAAA,EACjE,YAAY,KAAK;AAAE,WAAO,KAAK;AAAA,MAAM;AAAA,MAAI;AAAA,MAAK;AAAA;AAAA,IAAoB;AAAA,EAAG;AAAA,EACrE,MAAM,KAAK,MAAM,OAAO,GAAG;AACvB,QAAI,OAAO,SAAS;AAChB,aAAO;AACX,QAAI,EAAE,OAAO,IAAI,KAAK;AACtB,QAAI,QAAQ,OAAO,UAAU,KAAK,QAAQ,GAAG,OAAO,OAAO,KAAK,QAAQ,CAAC,GAAG,OAAO,IAAI,IAAI,IAAI,MAAM,KAAK,QAAQ,OAAO,IAAI;AAC7H,WAAO,QAAQ,IAAI,OAAO,IAAI,YAAW,KAAK,SAAS,MAAM,KAAK;AAAA,EACtE;AAAA,EACA,IAAI,SAAS;AACT,WAAO,KAAK,WAAW,KAAK,QAAQ,OAAO,sBAAsB;AAAA,EACrE;AAAA,EACA,gBAAgB,KAAK;AACjB,WAAO,KAAK,UAAU,OAAO,KAAK,QAAQ,OAAO;AAAA,MAAU,KAAK,QAAQ,QAAQ;AAAA,MAAK;AAAA,MAAK;AAAA,MAAG;AAAA;AAAA,IAAqB;AAAA,EACtH;AAAA,EACA,IAAI,cAAc;AACd,QAAI,EAAE,OAAO,IAAI,KAAK;AACtB,QAAI,QAAQ,OAAO,OAAO,KAAK,QAAQ,CAAC;AACxC,QAAI,SAAS,KAAK,UAAU,OAAO,OAAO,KAAK,QAAQ,QAAQ,CAAC,IAAI,OAAO,OAAO;AAC9E,aAAO,IAAI,YAAW,KAAK,SAAS,KAAK,SAAS,KAAK;AAC3D,WAAO,KAAK,gBAAgB,CAAC;AAAA,EACjC;AAAA,EACA,IAAI,cAAc;AACd,QAAI,EAAE,OAAO,IAAI,KAAK;AACtB,QAAI,cAAc,KAAK,UAAU,KAAK,QAAQ,QAAQ,IAAI;AAC1D,QAAI,KAAK,SAAS;AACd,aAAO,KAAK,gBAAgB,EAAE;AAClC,WAAO,IAAI,YAAW,KAAK,SAAS,KAAK,SAAS,OAAO;AAAA,MAAU;AAAA,MAAa,KAAK;AAAA,MAAO;AAAA,MAAI;AAAA,MAAG;AAAA;AAAA,IAAqB,CAAC;AAAA,EAC7H;AAAA,EACA,IAAI,OAAO;AAAE,WAAO;AAAA,EAAM;AAAA,EAC1B,SAAS;AACL,QAAI,WAAW,CAAC,GAAG,YAAY,CAAC;AAChC,QAAI,EAAE,OAAO,IAAI,KAAK;AACtB,QAAI,SAAS,KAAK,QAAQ,GAAG,OAAO,OAAO,OAAO,KAAK,QAAQ,CAAC;AAChE,QAAI,OAAO,QAAQ;AACf,UAAI,OAAO,OAAO,OAAO,KAAK,QAAQ,CAAC;AACvC,eAAS,KAAK,OAAO,MAAM,QAAQ,MAAM,IAAI,CAAC;AAC9C,gBAAU,KAAK,CAAC;AAAA,IACpB;AACA,WAAO,IAAI,KAAK,KAAK,MAAM,UAAU,WAAW,KAAK,KAAK,KAAK,IAAI;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AAAE,WAAO,KAAK,QAAQ,OAAO,YAAY,KAAK,KAAK;AAAA,EAAG;AACrE;AACA,SAAS,UAAU,OAAO;AACtB,MAAI,CAAC,MAAM;AACP,WAAO;AACX,MAAI,OAAO,GAAG,SAAS,MAAM,CAAC;AAC9B,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,QAAI,OAAO,MAAM,CAAC;AAClB,QAAI,KAAK,OAAO,OAAO,QAAQ,KAAK,KAAK,OAAO,IAAI;AAChD,eAAS;AACT,aAAO;AAAA,IACX;AAAA,EACJ;AACA,MAAI,OAAO,kBAAkB,YAAY,OAAO,QAAQ,IAAI,OAAO,OAAO;AAC1E,MAAI,WAAW,MAAM,MAAM;AAC3B,MAAI;AACA,aAAS,IAAI,IAAI;AAAA;AAEjB,aAAS,OAAO,MAAM,CAAC;AAC3B,SAAO,IAAI,cAAc,UAAU,MAAM;AAC7C;AACA,IAAM,gBAAN,MAAoB;AAAA,EAChB,YAAY,OAAO,MAAM;AACrB,SAAK,QAAQ;AACb,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,IAAI,OAAO;AAAE,WAAO,UAAU,KAAK,KAAK;AAAA,EAAG;AAC/C;AACA,SAAS,cAAc,MAAM,KAAK,MAAM;AACpC,MAAI,QAAQ,KAAK,aAAa,KAAK,IAAI,GAAG,SAAS;AACnD,WAAS,OAAO,iBAAiB,WAAW,QAAQ,MAAM,QAAQ,QAAQ,MAAM,OAAO,KAAK,QAAQ;AAChG,QAAI,KAAK,QAAQ,GAAG;AAChB,UAAI,SAAS,KAAK;AAClB,OAAC,WAAW,SAAS,CAAC,KAAK,IAAI,KAAK,OAAO,QAAQ,KAAK,IAAI,CAAC;AAC7D,aAAO;AAAA,IACX,OACK;AACD,UAAI,QAAQ,YAAY,IAAI,KAAK,IAAI;AAErC,UAAI,SAAS,MAAM,WAAW,MAAM,QAAQ,CAAC,EAAE,QAAQ,OAAO,MAAM,QAAQ,MAAM,QAAQ,SAAS,CAAC,EAAE,MAAM,KAAK;AAC7G,YAAI,OAAO,IAAI,SAAS,MAAM,MAAM,MAAM,QAAQ,CAAC,EAAE,OAAO,KAAK,MAAM,IAAI,IAAI;AAC/E,SAAC,WAAW,SAAS,CAAC,KAAK,IAAI,KAAK,YAAY,MAAM,KAAK,MAAM,KAAK,CAAC;AAAA,MAC3E;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,SAAS,UAAU,MAAM,IAAI;AACxC;AAKA,IAAM,aAAN,MAAiB;AAAA;AAAA;AAAA;AAAA,EAIb,IAAI,OAAO;AAAE,WAAO,KAAK,KAAK;AAAA,EAAM;AAAA;AAAA;AAAA;AAAA,EAIpC,YAAY,MAIZ,OAAO,GAAG;AACN,SAAK,OAAO;AAIZ,SAAK,SAAS;AACd,SAAK,QAAQ,CAAC;AAId,SAAK,QAAQ;AACb,SAAK,aAAa;AAClB,QAAI,gBAAgB,UAAU;AAC1B,WAAK,UAAU,IAAI;AAAA,IACvB,OACK;AACD,WAAK,QAAQ,KAAK,QAAQ;AAC1B,WAAK,SAAS,KAAK;AACnB,eAAS,IAAI,KAAK,SAAS,GAAG,IAAI,EAAE;AAChC,aAAK,MAAM,QAAQ,EAAE,KAAK;AAC9B,WAAK,aAAa;AAClB,WAAK,SAAS,KAAK,KAAK;AAAA,IAC5B;AAAA,EACJ;AAAA,EACA,UAAU,MAAM;AACZ,QAAI,CAAC;AACD,aAAO;AACX,SAAK,QAAQ;AACb,SAAK,OAAO,KAAK;AACjB,SAAK,OAAO,KAAK;AACjB,SAAK,KAAK,KAAK;AACf,WAAO;AAAA,EACX;AAAA,EACA,SAAS,OAAO,MAAM;AAClB,SAAK,QAAQ;AACb,QAAI,EAAE,OAAO,OAAO,IAAI,KAAK;AAC7B,SAAK,OAAO,QAAQ,OAAO,IAAI,MAAM,OAAO,OAAO,KAAK,CAAC;AACzD,SAAK,OAAO,QAAQ,OAAO,OAAO,QAAQ,CAAC;AAC3C,SAAK,KAAK,QAAQ,OAAO,OAAO,QAAQ,CAAC;AACzC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,MAAM;AACR,QAAI,CAAC;AACD,aAAO;AACX,QAAI,gBAAgB,UAAU;AAC1B,WAAK,SAAS;AACd,aAAO,KAAK,UAAU,IAAI;AAAA,IAC9B;AACA,SAAK,SAAS,KAAK;AACnB,WAAO,KAAK,SAAS,KAAK,OAAO,KAAK,IAAI;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACP,WAAO,KAAK,SAAS,KAAK,OAAO,OAAO,YAAY,KAAK,KAAK,IAAI,KAAK,MAAM,SAAS;AAAA,EAC1F;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,KAAK,KAAK,MAAM;AACvB,QAAI,CAAC,KAAK;AACN,aAAO,KAAK,MAAM,KAAK,MAAM,UAAU,MAAM,IAAI,KAAK,MAAM,MAAM,SAAS,SAAS,IAAI,GAAG,KAAK,KAAK,MAAM,KAAK,IAAI,CAAC;AACzH,QAAI,EAAE,OAAO,IAAI,KAAK;AACtB,QAAI,QAAQ,OAAO,UAAU,KAAK,QAAQ,GAAG,OAAO,OAAO,KAAK,QAAQ,CAAC,GAAG,KAAK,MAAM,KAAK,OAAO,OAAO,IAAI;AAC9G,QAAI,QAAQ;AACR,aAAO;AACX,SAAK,MAAM,KAAK,KAAK,KAAK;AAC1B,WAAO,KAAK,SAAS,KAAK;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AAAE,WAAO,KAAK;AAAA,MAAW;AAAA,MAAG;AAAA,MAAG;AAAA;AAAA,IAAqB;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAIpE,YAAY;AAAE,WAAO,KAAK;AAAA,MAAW;AAAA,MAAI;AAAA,MAAG;AAAA;AAAA,IAAqB;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAIpE,WAAW,KAAK;AAAE,WAAO,KAAK;AAAA,MAAW;AAAA,MAAG;AAAA,MAAK;AAAA;AAAA,IAAkB;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAItE,YAAY,KAAK;AAAE,WAAO,KAAK;AAAA,MAAW;AAAA,MAAI;AAAA,MAAK;AAAA;AAAA,IAAoB;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ1E,MAAM,KAAK,MAAM,OAAO,KAAK,MAAM;AAC/B,QAAI,CAAC,KAAK;AACN,aAAO,KAAK,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,IAAI,CAAC;AACvD,WAAO,OAAO,SAAS,iBAAiB,QAAQ,KAAK,WAAW,GAAG,KAAK,IAAI;AAAA,EAChF;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACL,QAAI,CAAC,KAAK;AACN,aAAO,KAAK,UAAW,KAAK,OAAO,SAAS,mBAAoB,KAAK,MAAM,UAAU,KAAK,MAAM,MAAM;AAC1G,QAAI,KAAK,MAAM;AACX,aAAO,KAAK,SAAS,KAAK,MAAM,IAAI,CAAC;AACzC,QAAI,SAAU,KAAK,OAAO,SAAS,mBAAoB,KAAK,OAAO,SAAS,KAAK,OAAO,OAAO,sBAAsB;AACrH,SAAK,SAAS;AACd,WAAO,KAAK,UAAU,MAAM;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,KAAK;AACT,QAAI,CAAC,KAAK;AACN,aAAO,CAAC,KAAK,MAAM,UAAU,QACvB,KAAK,MAAM,KAAK,MAAM,QAAQ,IAAI,OAC9B,KAAK,MAAM,QAAQ,UAAU,KAAK,MAAM,QAAQ,KAAK,KAAK,GAAG,GAAuB,KAAK,IAAI,CAAC;AAC5G,QAAI,EAAE,OAAO,IAAI,KAAK,QAAQ,IAAI,KAAK,MAAM,SAAS;AACtD,QAAI,MAAM,GAAG;AACT,UAAI,cAAc,IAAI,IAAI,IAAI,KAAK,MAAM,CAAC,IAAI;AAC9C,UAAI,KAAK,SAAS;AACd,eAAO,KAAK,SAAS,OAAO;AAAA,UAAU;AAAA,UAAa,KAAK;AAAA,UAAO;AAAA,UAAI;AAAA,UAAG;AAAA;AAAA,QAAqB,CAAC;AAAA,IACpG,OACK;AACD,UAAI,QAAQ,OAAO,OAAO,KAAK,QAAQ,CAAC;AACxC,UAAI,SAAS,IAAI,IAAI,OAAO,OAAO,SAAS,OAAO,OAAO,KAAK,MAAM,CAAC,IAAI,CAAC;AACvE,eAAO,KAAK,SAAS,KAAK;AAAA,IAClC;AACA,WAAO,IAAI,IAAI,KAAK,MAAM,KAAK,OAAO,OAAO,UAAU,KAAK,OAAO,QAAQ,KAAK,KAAK,GAAG,GAAuB,KAAK,IAAI,CAAC,IAAI;AAAA,EACjI;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AAAE,WAAO,KAAK,QAAQ,CAAC;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAIxC,cAAc;AAAE,WAAO,KAAK,QAAQ,EAAE;AAAA,EAAG;AAAA,EACzC,WAAW,KAAK;AACZ,QAAI,OAAO,QAAQ,EAAE,OAAO,IAAI;AAChC,QAAI,QAAQ;AACR,UAAI,MAAM,GAAG;AACT,YAAI,KAAK,QAAQ,OAAO,OAAO,OAAO;AAClC,iBAAO;AAAA,MACf,OACK;AACD,iBAAS,IAAI,GAAG,IAAI,KAAK,OAAO;AAC5B,cAAI,OAAO,OAAO,OAAO,IAAI,CAAC,IAAI,KAAK;AACnC,mBAAO;AAAA,MACnB;AACA,OAAC,EAAE,OAAO,OAAO,IAAI;AAAA,IACzB,OACK;AACD,OAAC,EAAE,OAAO,SAAS,OAAO,IAAI,KAAK;AAAA,IACvC;AACA,WAAO,QAAQ,EAAE,OAAO,SAAS,OAAO,IAAI,QAAQ;AAChD,UAAI,QAAQ;AACR,iBAAS,IAAI,QAAQ,KAAK,IAAI,MAAM,IAAI,KAAK,OAAO,MAAM,SAAS,QAAQ,KAAK,GAAG,KAAK,KAAK;AACzF,cAAI,QAAQ,OAAO,MAAM,SAAS,CAAC;AACnC,cAAK,KAAK,OAAO,SAAS,oBACtB,iBAAiB,cACjB,CAAC,MAAM,KAAK,eACZ,SAAS,KAAK;AACd,mBAAO;AAAA,QACf;AAAA,IACR;AACA,WAAO;AAAA,EACX;AAAA,EACA,KAAK,KAAK,OAAO;AACb,QAAI,SAAS,KAAK;AAAA,MAAW;AAAA,MAAK;AAAA,MAAG;AAAA;AAAA,IAAqB;AACtD,aAAO;AACX,eAAS;AACL,UAAI,KAAK,QAAQ,GAAG;AAChB,eAAO;AACX,UAAI,KAAK,WAAW,GAAG,KAAK,CAAC,KAAK,OAAO;AACrC,eAAO;AAAA,IACf;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,KAAK,QAAQ,MAAM;AAAE,WAAO,KAAK,KAAK,GAAG,KAAK;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOjD,KAAK,QAAQ,MAAM;AAAE,WAAO,KAAK,KAAK,IAAI,KAAK;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMlD,OAAO,KAAK,OAAO,GAAG;AAElB,WAAO,KAAK,QAAQ,KAAK,OACpB,OAAO,IAAI,KAAK,QAAQ,MAAM,KAAK,OAAO,SAC1C,OAAO,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK;AACxC,UAAI,CAAC,KAAK,OAAO;AACb;AAER,WAAO,KAAK,WAAW,GAAG,KAAK,IAAI,GAAG;AAAA,IAAE;AACxC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,OAAO;AACP,QAAI,CAAC,KAAK;AACN,aAAO,KAAK;AAChB,QAAI,QAAQ,KAAK,YAAY,SAAS,MAAM,QAAQ;AACpD,QAAI,SAAS,MAAM,WAAW,KAAK,QAAQ;AACvC,WAAM,UAAS,QAAQ,KAAK,OAAO,IAAI,KAAK,MAAM,QAAQ,KAAK,KAAI;AAC/D,iBAAS,IAAI,OAAO,GAAG,IAAI,EAAE;AACzB,cAAI,EAAE,SAAS,OAAO;AAClB,gBAAI,SAAS,KAAK;AACd,qBAAO;AACX,qBAAS;AACT,oBAAQ,IAAI;AACZ,kBAAM;AAAA,UACV;AACJ,gBAAQ,KAAK,MAAM,EAAE,CAAC;AAAA,MAC1B;AAAA,IACJ;AACA,aAAS,IAAI,OAAO,IAAI,KAAK,MAAM,QAAQ;AACvC,eAAS,IAAI,WAAW,KAAK,QAAQ,QAAQ,KAAK,MAAM,CAAC,CAAC;AAC9D,WAAO,KAAK,aAAa,IAAI,WAAW,KAAK,QAAQ,QAAQ,KAAK,KAAK;AAAA,EAC3E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,OAAO;AACP,WAAO,KAAK,SAAS,OAAO,KAAK,MAAM;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,OAAO,OAAO;AAClB,aAAS,QAAQ,OAAK;AAClB,UAAI,YAAY;AAChB,UAAI,KAAK,KAAK,eAAe,MAAM,IAAI,MAAM,OAAO;AAChD,YAAI,KAAK,WAAW,GAAG;AACnB;AACA;AAAA,QACJ;AACA,YAAI,CAAC,KAAK,KAAK;AACX,sBAAY;AAAA,MACpB;AACA,iBAAS;AACL,YAAI,aAAa;AACb,gBAAM,IAAI;AACd,oBAAY,KAAK,KAAK;AACtB,YAAI,CAAC;AACD;AACJ,YAAI,KAAK,YAAY;AACjB;AACJ,aAAK,OAAO;AACZ;AACA,oBAAY;AAAA,MAChB;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,SAAS;AAClB,QAAI,CAAC,KAAK;AACN,aAAO,iBAAiB,KAAK,KAAK,QAAQ,OAAO;AACrD,QAAI,EAAE,OAAO,IAAI,KAAK,QAAQ,EAAE,MAAM,IAAI,OAAO;AACjD,aAAS,IAAI,QAAQ,SAAS,GAAG,IAAI,KAAK,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AACrE,UAAI,IAAI;AACJ,eAAO,iBAAiB,KAAK,OAAO,SAAS,CAAC;AAClD,UAAI,OAAO,MAAM,OAAO,OAAO,KAAK,MAAM,CAAC,CAAC,CAAC;AAC7C,UAAI,CAAC,KAAK,aAAa;AACnB,YAAI,QAAQ,CAAC,KAAK,QAAQ,CAAC,KAAK,KAAK;AACjC,iBAAO;AACX;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;AACA,SAAS,SAAS,MAAM;AACpB,SAAO,KAAK,SAAS,KAAK,QAAM,cAAc,cAAc,CAAC,GAAG,KAAK,eAAe,SAAS,EAAE,CAAC;AACpG;AACA,SAAS,UAAU,MAAM;AACrB,MAAIA;AACJ,MAAI,EAAE,QAAQ,SAAAC,UAAS,kBAAkB,qBAAqB,SAAS,CAAC,GAAG,gBAAgBA,SAAQ,MAAM,OAAO,IAAI;AACpH,MAAI,SAAS,MAAM,QAAQ,MAAM,IAAI,IAAI,iBAAiB,QAAQ,OAAO,MAAM,IAAI;AACnF,MAAI,QAAQA,SAAQ;AACpB,MAAI,cAAc,GAAG,YAAY;AACjC,WAAS,SAAS,aAAa,QAAQC,WAAUC,YAAW,UAAU,OAAO;AACzE,QAAI,EAAE,IAAI,OAAO,KAAK,KAAK,IAAI;AAC/B,QAAI,mBAAmB,WAAW,iBAAiB;AACnD,WAAO,OAAO,GAAG;AACb,aAAO,KAAK;AACZ,UAAI,QAAQ,IAA8B;AACtC,YAAIC,QAAO,OAAO,EAAE;AACpB,QAAAF,UAAS,KAAKE,KAAI;AAClB,QAAAD,WAAU,KAAK,QAAQ,WAAW;AAClC;AAAA,MACJ,WACS,QAAQ,IAAsC;AACnD,sBAAc;AACd;AAAA,MACJ,WACS,QAAQ,IAAkC;AAC/C,oBAAY;AACZ;AAAA,MACJ,OACK;AACD,cAAM,IAAI,WAAW,6BAA6B,IAAI,EAAE;AAAA,MAC5D;AAAA,IACJ;AACA,QAAI,OAAO,MAAM,EAAE,GAAG,MAAME;AAC5B,QAAI,WAAW,QAAQ;AACvB,QAAI,MAAM,SAAS,oBAAoBA,UAAS,eAAe,OAAO,MAAM,QAAQ,QAAQ,IAAI;AAE5F,UAAIC,QAAO,IAAI,YAAYD,QAAO,OAAOA,QAAO,IAAI;AACpD,UAAI,SAAS,OAAO,MAAMA,QAAO,MAAM,QAAQC,MAAK;AACpD,aAAO,OAAO,MAAM;AAChB,gBAAQ,aAAaD,QAAO,OAAOC,OAAM,KAAK;AAClD,aAAO,IAAI,WAAWA,OAAM,MAAMD,QAAO,OAAOJ,QAAO;AACvD,iBAAWI,QAAO,QAAQ;AAAA,IAC9B,OACK;AACD,UAAI,SAAS,OAAO,MAAM;AAC1B,aAAO,KAAK;AACZ,UAAI,gBAAgB,CAAC,GAAG,iBAAiB,CAAC;AAC1C,UAAI,gBAAgB,MAAM,gBAAgB,KAAK;AAC/C,UAAI,YAAY,GAAG,UAAU;AAC7B,aAAO,OAAO,MAAM,QAAQ;AACxB,YAAI,iBAAiB,KAAK,OAAO,MAAM,iBAAiB,OAAO,QAAQ,GAAG;AACtE,cAAI,OAAO,OAAO,UAAU,iBAAiB;AACzC,2BAAe,eAAe,gBAAgB,OAAO,WAAW,OAAO,KAAK,SAAS,eAAe,kBAAkB,cAAc;AACpI,wBAAY,cAAc;AAC1B,sBAAU,OAAO;AAAA,UACrB;AACA,iBAAO,KAAK;AAAA,QAChB,WACS,QAAQ,MAAyB;AACtC,uBAAa,OAAO,QAAQ,eAAe,cAAc;AAAA,QAC7D,OACK;AACD,mBAAS,OAAO,QAAQ,eAAe,gBAAgB,eAAe,QAAQ,CAAC;AAAA,QACnF;AAAA,MACJ;AACA,UAAI,iBAAiB,KAAK,YAAY,KAAK,YAAY,cAAc;AACjE,uBAAe,eAAe,gBAAgB,OAAO,WAAW,OAAO,SAAS,eAAe,kBAAkB,cAAc;AACnI,oBAAc,QAAQ;AACtB,qBAAe,QAAQ;AACvB,UAAI,gBAAgB,MAAM,YAAY,GAAG;AACrC,YAAI,OAAO,aAAa,MAAM,cAAc;AAC5C,eAAO,aAAa,MAAM,eAAe,gBAAgB,GAAG,cAAc,QAAQ,GAAG,MAAM,OAAO,MAAM,IAAI;AAAA,MAChH,OACK;AACD,eAAO,SAAS,MAAM,eAAe,gBAAgB,MAAM,OAAO,mBAAmB,KAAK,cAAc;AAAA,MAC5G;AAAA,IACJ;AACA,IAAAH,UAAS,KAAK,IAAI;AAClB,IAAAC,WAAU,KAAK,QAAQ;AAAA,EAC3B;AACA,WAAS,aAAa,aAAa,QAAQD,WAAUC,YAAW;AAC5D,QAAI,QAAQ,CAAC;AACb,QAAI,YAAY,GAAG,SAAS;AAC5B,WAAO,OAAO,MAAM,QAAQ;AACxB,UAAI,EAAE,IAAI,OAAO,KAAK,KAAK,IAAI;AAC/B,UAAI,OAAO,GAAG;AACV,eAAO,KAAK;AAAA,MAChB,WACS,SAAS,MAAM,QAAQ,QAAQ;AACpC;AAAA,MACJ,OACK;AACD,YAAI,SAAS;AACT,mBAAS,MAAM;AACnB,cAAM,KAAK,IAAI,OAAO,GAAG;AACzB;AACA,eAAO,KAAK;AAAA,MAChB;AAAA,IACJ;AACA,QAAI,WAAW;AACX,UAAIE,UAAS,IAAI,YAAY,YAAY,CAAC;AAC1C,UAAI,QAAQ,MAAM,MAAM,SAAS,CAAC;AAClC,eAAS,IAAI,MAAM,SAAS,GAAG,IAAI,GAAG,KAAK,GAAG,KAAK,GAAG;AAClD,QAAAA,QAAO,GAAG,IAAI,MAAM,CAAC;AACrB,QAAAA,QAAO,GAAG,IAAI,MAAM,IAAI,CAAC,IAAI;AAC7B,QAAAA,QAAO,GAAG,IAAI,MAAM,IAAI,CAAC,IAAI;AAC7B,QAAAA,QAAO,GAAG,IAAI;AAAA,MAClB;AACA,MAAAH,UAAS,KAAK,IAAI,WAAWG,SAAQ,MAAM,CAAC,IAAI,OAAOJ,QAAO,CAAC;AAC/D,MAAAE,WAAU,KAAK,QAAQ,WAAW;AAAA,IACtC;AAAA,EACJ;AACA,WAAS,aAAa,MAAMI,cAAa;AACrC,WAAO,CAACL,WAAUC,YAAWK,YAAW;AACpC,UAAIC,aAAY,GAAG,QAAQP,UAAS,SAAS,GAAG,MAAM;AACtD,UAAI,SAAS,MAAM,OAAOA,UAAS,KAAK,cAAc,MAAM;AACxD,YAAI,CAAC,SAAS,KAAK,QAAQ,QAAQ,KAAK,UAAUM;AAC9C,iBAAO;AACX,YAAI,gBAAgB,KAAK,KAAK,SAAS,SAAS;AAC5C,UAAAC,aAAYN,WAAU,KAAK,IAAI,KAAK,SAAS;AAAA,MACrD;AACA,aAAO,SAAS,MAAMD,WAAUC,YAAWK,SAAQC,YAAWF,YAAW;AAAA,IAC7E;AAAA,EACJ;AACA,WAAS,eAAeL,WAAUC,YAAW,MAAM,GAAG,MAAM,IAAI,MAAMM,YAAWF,cAAa;AAC1F,QAAI,gBAAgB,CAAC,GAAG,iBAAiB,CAAC;AAC1C,WAAOL,UAAS,SAAS,GAAG;AACxB,oBAAc,KAAKA,UAAS,IAAI,CAAC;AACjC,qBAAe,KAAKC,WAAU,IAAI,IAAI,OAAO,IAAI;AAAA,IACrD;AACA,IAAAD,UAAS,KAAK,SAASD,SAAQ,MAAM,IAAI,GAAG,eAAe,gBAAgB,KAAK,MAAMQ,aAAY,IAAIF,YAAW,CAAC;AAClH,IAAAJ,WAAU,KAAK,OAAO,IAAI;AAAA,EAC9B;AACA,WAAS,SAAS,MAAMD,WAAUC,YAAWK,SAAQC,YAAWF,cAAa,OAAO;AAChF,QAAIA,cAAa;AACb,UAAI,OAAO,CAAC,SAAS,aAAaA,YAAW;AAC7C,cAAQ,QAAQ,CAAC,IAAI,EAAE,OAAO,KAAK,IAAI,CAAC,IAAI;AAAA,IAChD;AACA,QAAIE,aAAY,IAAI;AAChB,UAAI,OAAO,CAAC,SAAS,WAAWA,UAAS;AACzC,cAAQ,QAAQ,CAAC,IAAI,EAAE,OAAO,KAAK,IAAI,CAAC,IAAI;AAAA,IAChD;AACA,WAAO,IAAI,KAAK,MAAMP,WAAUC,YAAWK,SAAQ,KAAK;AAAA,EAC5D;AACA,WAAS,eAAe,SAAS,UAAU;AAOvC,QAAI,OAAO,OAAO,KAAK;AACvB,QAAI,OAAO,GAAG,QAAQ,GAAG,OAAO,GAAG,WAAW,KAAK,MAAM;AACzD,QAAI,SAAS,EAAE,MAAM,GAAG,OAAO,GAAG,MAAM,EAAE;AAC1C,SAAM,UAAS,SAAS,KAAK,MAAM,SAAS,KAAK,MAAM,UAAS;AAC5D,UAAIE,YAAW,KAAK;AAEpB,UAAI,KAAK,MAAM,YAAYA,aAAY,GAAG;AAGtC,eAAO,OAAO;AACd,eAAO,QAAQ;AACf,eAAO,OAAO;AACd,gBAAQ;AACR,gBAAQ;AACR,aAAK,KAAK;AACV;AAAA,MACJ;AACA,UAAI,WAAW,KAAK,MAAMA;AAC1B,UAAIA,YAAW,KAAK,WAAW,UAAU,KAAK,QAAQ;AAClD;AACJ,UAAI,eAAe,KAAK,MAAM,gBAAgB,IAAI;AAClD,UAAI,YAAY,KAAK;AACrB,WAAK,KAAK;AACV,aAAO,KAAK,MAAM,UAAU;AACxB,YAAI,KAAK,OAAO,GAAG;AACf,cAAI,KAAK,QAAQ;AACb,4BAAgB;AAAA;AAEhB,kBAAM;AAAA,QACd,WACS,KAAK,MAAM,eAAe;AAC/B,0BAAgB;AAAA,QACpB;AACA,aAAK,KAAK;AAAA,MACd;AACA,cAAQ;AACR,cAAQA;AACR,cAAQ;AAAA,IACZ;AACA,QAAI,WAAW,KAAK,QAAQ,SAAS;AACjC,aAAO,OAAO;AACd,aAAO,QAAQ;AACf,aAAO,OAAO;AAAA,IAClB;AACA,WAAO,OAAO,OAAO,IAAI,SAAS;AAAA,EACtC;AACA,WAAS,aAAa,aAAaL,SAAQ,OAAO;AAC9C,QAAI,EAAE,IAAI,OAAO,KAAK,KAAK,IAAI;AAC/B,WAAO,KAAK;AACZ,QAAI,QAAQ,KAAK,KAAK,eAAe;AACjC,UAAI,aAAa;AACjB,UAAI,OAAO,GAAG;AACV,YAAI,SAAS,OAAO,OAAO,OAAO;AAClC,eAAO,OAAO,MAAM;AAChB,kBAAQ,aAAa,aAAaA,SAAQ,KAAK;AAAA,MACvD;AACA,MAAAA,QAAO,EAAE,KAAK,IAAI;AAClB,MAAAA,QAAO,EAAE,KAAK,IAAI,MAAM;AACxB,MAAAA,QAAO,EAAE,KAAK,IAAI,QAAQ;AAC1B,MAAAA,QAAO,EAAE,KAAK,IAAI;AAAA,IACtB,WACS,QAAQ,IAAsC;AACnD,oBAAc;AAAA,IAClB,WACS,QAAQ,IAAkC;AAC/C,kBAAY;AAAA,IAChB;AACA,WAAO;AAAA,EACX;AACA,MAAI,WAAW,CAAC,GAAG,YAAY,CAAC;AAChC,SAAO,OAAO,MAAM;AAChB,aAAS,KAAK,SAAS,GAAG,KAAK,eAAe,GAAG,UAAU,WAAW,IAAI,CAAC;AAC/E,MAAI,UAAUL,MAAK,KAAK,YAAY,QAAQA,QAAO,SAASA,MAAM,SAAS,SAAS,UAAU,CAAC,IAAI,SAAS,CAAC,EAAE,SAAS;AACxH,SAAO,IAAI,KAAK,MAAM,KAAK,KAAK,GAAG,SAAS,QAAQ,GAAG,UAAU,QAAQ,GAAG,MAAM;AACtF;AACA,IAAM,gBAAgB,oBAAI;AAC1B,SAAS,SAAS,aAAa,MAAM;AACjC,MAAI,CAAC,YAAY,eAAe,gBAAgB,cAAc,KAAK,QAAQ;AACvE,WAAO;AACX,MAAI,OAAO,cAAc,IAAI,IAAI;AACjC,MAAI,QAAQ,MAAM;AACd,WAAO;AACP,aAAS,SAAS,KAAK,UAAU;AAC7B,UAAI,MAAM,QAAQ,eAAe,EAAE,iBAAiB,OAAO;AACvD,eAAO;AACP;AAAA,MACJ;AACA,cAAQ,SAAS,aAAa,KAAK;AAAA,IACvC;AACA,kBAAc,IAAI,MAAM,IAAI;AAAA,EAChC;AACA,SAAO;AACX;AACA,SAAS,aAET,aAEA,UAAU,WAEV,MAAM,IAEN,OAEA,QAEA,OAEA,QAAQ;AACJ,MAAI,QAAQ;AACZ,WAAS,IAAI,MAAM,IAAI,IAAI;AACvB,aAAS,SAAS,aAAa,SAAS,CAAC,CAAC;AAC9C,MAAI,WAAW,KAAK;AAAA,IAAM,QAAQ,MAAO;AAAA;AAAA,EAA4B;AACrE,MAAI,gBAAgB,CAAC,GAAG,iBAAiB,CAAC;AAC1C,WAAS,OAAOE,WAAUC,YAAWQ,OAAMC,KAAI,QAAQ;AACnD,aAAS,IAAID,OAAM,IAAIC,OAAK;AACxB,UAAI,YAAY,GAAG,aAAaT,WAAU,CAAC,GAAG,YAAY,SAAS,aAAaD,UAAS,CAAC,CAAC;AAC3F;AACA,aAAO,IAAIU,KAAI,KAAK;AAChB,YAAI,WAAW,SAAS,aAAaV,UAAS,CAAC,CAAC;AAChD,YAAI,YAAY,YAAY;AACxB;AACJ,qBAAa;AAAA,MACjB;AACA,UAAI,KAAK,YAAY,GAAG;AACpB,YAAI,YAAY,UAAU;AACtB,cAAI,OAAOA,UAAS,SAAS;AAC7B,iBAAO,KAAK,UAAU,KAAK,WAAW,GAAG,KAAK,SAAS,QAAQC,WAAU,SAAS,IAAI,MAAM;AAC5F;AAAA,QACJ;AACA,sBAAc,KAAKD,UAAS,SAAS,CAAC;AAAA,MAC1C,OACK;AACD,YAAIM,UAASL,WAAU,IAAI,CAAC,IAAID,UAAS,IAAI,CAAC,EAAE,SAAS;AACzD,sBAAc,KAAK,aAAa,aAAaA,WAAUC,YAAW,WAAW,GAAG,YAAYK,SAAQ,MAAM,MAAM,CAAC;AAAA,MACrH;AACA,qBAAe,KAAK,aAAa,SAAS,KAAK;AAAA,IACnD;AAAA,EACJ;AACA,SAAO,UAAU,WAAW,MAAM,IAAI,CAAC;AACvC,UAAQ,SAAS,QAAQ,eAAe,gBAAgB,MAAM;AAClE;AAMA,IAAM,cAAN,MAAkB;AAAA,EACd,cAAc;AACV,SAAK,MAAM,oBAAI,QAAQ;AAAA,EAC3B;AAAA,EACA,UAAU,QAAQ,OAAO,OAAO;AAC5B,QAAI,QAAQ,KAAK,IAAI,IAAI,MAAM;AAC/B,QAAI,CAAC;AACD,WAAK,IAAI,IAAI,QAAQ,QAAQ,oBAAI,KAAG;AACxC,UAAM,IAAI,OAAO,KAAK;AAAA,EAC1B;AAAA,EACA,UAAU,QAAQ,OAAO;AACrB,QAAI,QAAQ,KAAK,IAAI,IAAI,MAAM;AAC/B,WAAO,SAAS,MAAM,IAAI,KAAK;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,MAAM,OAAO;AACb,QAAI,gBAAgB;AAChB,WAAK,UAAU,KAAK,QAAQ,QAAQ,KAAK,OAAO,KAAK;AAAA,aAChD,gBAAgB;AACrB,WAAK,IAAI,IAAI,KAAK,MAAM,KAAK;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,MAAM;AACN,WAAO,gBAAgB,aAAa,KAAK,UAAU,KAAK,QAAQ,QAAQ,KAAK,KAAK,IAC5E,gBAAgB,WAAW,KAAK,IAAI,IAAI,KAAK,IAAI,IAAI;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU,QAAQ,OAAO;AACrB,QAAI,OAAO;AACP,WAAK,UAAU,OAAO,OAAO,QAAQ,OAAO,OAAO,KAAK;AAAA;AAExD,WAAK,IAAI,IAAI,OAAO,MAAM,KAAK;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,QAAQ;AACd,WAAO,OAAO,SAAS,KAAK,UAAU,OAAO,OAAO,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,IAAI,OAAO,IAAI;AAAA,EACxG;AACJ;AAWA,IAAM,eAAN,MAAM,cAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOf,YAMA,MAIA,IAIA,MAOA,QAAQ,YAAY,OAAO,UAAU,OAAO;AACxC,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,QAAQ,YAAY,IAAqB,MAAM,UAAU,IAAmB;AAAA,EACrF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,YAAY;AAAE,YAAQ,KAAK,OAAO,KAAsB;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/D,IAAI,UAAU;AAAE,YAAQ,KAAK,OAAO,KAAoB;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAS3D,OAAO,QAAQ,MAAM,YAAY,CAAC,GAAG,UAAU,OAAO;AAClD,QAAI,SAAS,CAAC,IAAI,cAAa,GAAG,KAAK,QAAQ,MAAM,GAAG,OAAO,OAAO,CAAC;AACvE,aAAS,KAAK;AACV,UAAI,EAAE,KAAK,KAAK;AACZ,eAAO,KAAK,CAAC;AACrB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,aAAa,WAAW,SAAS,SAAS,KAAK;AAClD,QAAI,CAAC,QAAQ;AACT,aAAO;AACX,QAAI,SAAS,CAAC;AACd,QAAI,KAAK,GAAG,QAAQ,UAAU,SAAS,UAAU,CAAC,IAAI;AACtD,aAAS,KAAK,GAAG,MAAM,GAAG,MAAM,KAAI,MAAM;AACtC,UAAI,QAAQ,KAAK,QAAQ,SAAS,QAAQ,EAAE,IAAI;AAChD,UAAI,UAAU,QAAQ,MAAM,QAAQ;AACpC,UAAI,UAAU,OAAO;AACjB,eAAO,SAAS,MAAM,OAAO,SAAS;AAClC,cAAI,MAAM;AACV,cAAI,OAAO,IAAI,QAAQ,WAAW,IAAI,MAAM,KAAK;AAC7C,gBAAI,QAAQ,KAAK,IAAI,IAAI,MAAM,GAAG,IAAI,KAAK,MAAM,KAAK,IAAI,IAAI,IAAI,OAAO,IAAI;AAC7E,kBAAM,SAAS,MAAM,OAAO,IAAI,cAAa,OAAO,KAAK,IAAI,MAAM,IAAI,SAAS,KAAK,KAAK,GAAG,CAAC,CAAC,KAAK;AAAA,UACxG;AACA,cAAI;AACA,mBAAO,KAAK,GAAG;AACnB,cAAI,MAAM,KAAK;AACX;AACJ,kBAAQ,KAAK,UAAU,SAAS,UAAU,IAAI,IAAI;AAAA,QACtD;AACJ,UAAI,CAAC;AACD;AACJ,YAAM,MAAM;AACZ,YAAM,MAAM,MAAM,MAAM;AAAA,IAC5B;AACA,WAAO;AAAA,EACX;AACJ;AAIA,IAAM,SAAN,MAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWT,WAAW,OAAO,WAAW,QAAQ;AACjC,QAAI,OAAO,SAAS;AAChB,cAAQ,IAAI,YAAY,KAAK;AACjC,aAAS,CAAC,SAAS,CAAC,IAAI,MAAM,GAAG,MAAM,MAAM,CAAC,IAAI,OAAO,SAAS,OAAO,IAAI,OAAK,IAAI,MAAM,EAAE,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC;AAC7H,WAAO,KAAK,YAAY,OAAO,aAAa,CAAC,GAAG,MAAM;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,OAAO,WAAW,QAAQ;AAC5B,QAAI,QAAQ,KAAK,WAAW,OAAO,WAAW,MAAM;AACpD,eAAS;AACL,UAAI,OAAO,MAAM,QAAQ;AACzB,UAAI;AACA,eAAO;AAAA,IACf;AAAA,EACJ;AACJ;AACA,IAAM,cAAN,MAAkB;AAAA,EACd,YAAYK,SAAQ;AAChB,SAAK,SAASA;AAAA,EAClB;AAAA,EACA,IAAI,SAAS;AAAE,WAAO,KAAK,OAAO;AAAA,EAAQ;AAAA,EAC1C,MAAM,MAAM;AAAE,WAAO,KAAK,OAAO,MAAM,IAAI;AAAA,EAAG;AAAA,EAC9C,IAAI,aAAa;AAAE,WAAO;AAAA,EAAO;AAAA,EACjC,KAAK,MAAM,IAAI;AAAE,WAAO,KAAK,OAAO,MAAM,MAAM,EAAE;AAAA,EAAG;AACzD;AASA,SAAS,WAAW,MAAM;AACtB,SAAO,CAAC,OAAO,OAAO,WAAW,WAAW,IAAI,WAAW,OAAO,MAAM,OAAO,WAAW,MAAM;AACpG;AACA,IAAM,aAAN,MAAiB;AAAA,EACb,YAAY,QAAQ,OAAO,SAAS,QAAQ,MAAM;AAC9C,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EAChB;AACJ;AACA,SAAS,YAAY,QAAQ;AACzB,MAAI,CAAC,OAAO,UAAU,OAAO,KAAK,OAAK,EAAE,QAAQ,EAAE,EAAE;AACjD,UAAM,IAAI,WAAW,uCAAuC,KAAK,UAAU,MAAM,CAAC;AAC1F;AACA,IAAM,gBAAN,MAAoB;AAAA,EAChB,YAAY,QAAQ,WAAW,QAAQ,OAAO,OAAO,QAAQ,MAAM;AAC/D,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,SAAS,CAAC;AAAA,EACnB;AACJ;AACA,IAAM,eAAe,IAAI,SAAS,EAAE,SAAS,KAAK,CAAC;AACnD,IAAM,aAAN,MAAiB;AAAA,EACb,YAAY,MAAM,MAAM,OAAO,WAAW,QAAQ;AAC9C,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,QAAQ,CAAC;AACd,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,UAAU;AACN,QAAI,KAAK,WAAW;AAChB,UAAIC,QAAO,KAAK,UAAU,QAAQ;AAClC,UAAI,CAACA;AACD,eAAO;AACX,WAAK,YAAY;AACjB,WAAK,WAAWA;AAChB,WAAK,WAAW;AAChB,UAAI,KAAK,aAAa;AAClB,iBAASC,UAAS,KAAK;AACnB,UAAAA,OAAM,MAAM,OAAO,KAAK,SAAS;AAAA,IAC7C;AACA,QAAI,KAAK,aAAa,KAAK,MAAM,QAAQ;AACrC,UAAI,SAAS,KAAK;AAClB,UAAI,KAAK,aAAa;AAClB,iBAAS,IAAI,KAAK,OAAO,MAAM,OAAO,UAAU,OAAO,WAAW,OAAO,QAAQ,OAAO,WAAW,OAAO,CAAC,CAAC,cAAc,KAAK,SAAS,CAAC,CAAC,CAAC;AAC/I,aAAO;AAAA,IACX;AACA,QAAI,QAAQ,KAAK,MAAM,KAAK,SAAS,GAAG,OAAO,MAAM,MAAM,QAAQ;AACnE,QAAI,MAAM;AACN,WAAK;AAKL,UAAI,QAAQ,OAAO,OAAO,uBAAO,OAAO,IAAI,GAAG,MAAM,OAAO,KAAK;AACjE,YAAM,SAAS,QAAQ,EAAE,IAAI,IAAI,YAAY,MAAM,MAAM,SAAS,MAAM,MAAM;AAC9E,YAAM,OAAO,QAAQ;AAAA,IACzB;AACA,WAAO;AAAA,EACX;AAAA,EACA,IAAI,YAAY;AACZ,QAAI,KAAK;AACL,aAAO;AACX,QAAI,MAAM,KAAK,MAAM;AACrB,aAAS,IAAI,KAAK,WAAW,IAAI,KAAK,MAAM,QAAQ,KAAK;AACrD,UAAI,KAAK,MAAM,CAAC,EAAE,OAAO;AACrB,cAAM,KAAK,IAAI,KAAK,KAAK,MAAM,CAAC,EAAE,MAAM,SAAS;AAAA,IACzD;AACA,WAAO;AAAA,EACX;AAAA,EACA,OAAO,KAAK;AACR,SAAK,YAAY;AACjB,QAAI,KAAK;AACL,WAAK,UAAU,OAAO,GAAG;AAAA;AAEzB,eAAS,IAAI,KAAK,WAAW,IAAI,KAAK,MAAM,QAAQ;AAChD,aAAK,MAAM,CAAC,EAAE,MAAM,OAAO,GAAG;AAAA,EAC1C;AAAA,EACA,aAAa;AACT,QAAI,iBAAiB,IAAI,eAAe,KAAK,SAAS;AACtD,QAAI,UAAU;AACd,QAAI,UAAU;AACd,QAAI,SAAS,IAAI,WAAW,IAAI,SAAS,KAAK,UAAU,KAAK,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,mBAAmB,SAAS,YAAY;AACxI,SAAM,UAAS,MAAM,eAAa;AAC9B,UAAI,QAAQ,MAAM;AAClB,UAAI,KAAK,aAAa,QAAQ,OAAO,QAAQ,KAAK,WAAW;AACzD,gBAAQ;AAAA,MACZ,WACS,eAAe,QAAQ,MAAM,GAAG;AACrC,YAAI,SAAS;AACT,cAAI,QAAQ,QAAQ,OAAO,KAAK,OAAK,EAAE,KAAK,QAAQ,OAAO,QAAQ,EAAE,KAAK,MAAM,OAAO,MAAM,EAAE,MAAM,OAAO;AAC5G,cAAI;AACA,qBAAS,KAAK,MAAM,MAAM,SAAS;AAC/B,kBAAI,OAAO,EAAE,OAAO,MAAM,KAAK,KAAK,EAAE,KAAK,MAAM;AACjD,kBAAI,QAAQ,OAAO,QAAQ,MAAM,OAAO,MAAM,CAAC,QAAQ,OAAO,KAAK,CAAAC,OAAKA,GAAE,OAAO,MAAMA,GAAE,KAAK,IAAI;AAC9F,wBAAQ,OAAO,KAAK,EAAE,MAAM,GAAG,CAAC;AAAA,YACxC;AAAA,QACR;AACA,gBAAQ;AAAA,MACZ,WACS,YAAY,YAAY,WAAW,QAAQ,QAAQ,OAAO,MAAM,OAAO,EAAE,IAAI;AAClF,gBAAQ,aAAa;AAAA,MACzB,WACS,CAAC,OAAO,KAAK,gBAAgB,OAAO,KAAK,KAAK,QAAQ,KAAK,KAAK,OACpE,OAAO,OAAO,OAAO,MAAM,CAAC,KAAK,UAAU;AAC5C,YAAI,CAAC,OAAO;AACR,sBAAY,MAAM;AACtB,YAAI,YAAY,eAAe,WAAW,OAAO,MAAM,KAAK,MAAM;AAClE,YAAI,OAAO,KAAK,WAAW,YAAY;AACnC,oBAAU,IAAI,cAAc,KAAK,QAAQ,KAAK,SAAS,WAAW,KAAK,MAAM,QAAQ,OAAO,MAAM,OAAO,MAAM,OAAO;AAAA,QAC1H,OACK;AACD,cAAI,SAAS,YAAY,KAAK,QAAQ,KAAK,YACtC,OAAO,OAAO,OAAO,KAAK,CAAC,IAAI,MAAM,OAAO,MAAM,OAAO,EAAE,CAAC,IAAI,CAAC,EAAE;AACxE,cAAI,OAAO;AACP,wBAAY,MAAM;AACtB,cAAI,OAAO,UAAU,CAAC,KAAK;AACvB,iBAAK,MAAM,KAAK,IAAI,WAAW,KAAK,QAAQ,OAAO,SAAS,KAAK,OAAO,WAAW,KAAK,OAAO,eAAe,WAAW,MAAM,GAAG,MAAM,IAClI,KAAK,OAAO,WAAW,EAAE,GAAG,KAAK,UAAU,KAAK,QAAQ,IAAI,OAAK,IAAI,MAAM,EAAE,OAAO,OAAO,MAAM,EAAE,KAAK,OAAO,IAAI,CAAC,IAAI,MAAM,OAAO,MAAM,OAAO,SAAS,OAAO,CAAC,EAAE,OAAO,OAAO,IAAI,CAAC;AAClM,cAAI,CAAC,KAAK;AACN,oBAAQ;AAAA,mBACH,OAAO;AACZ,sBAAU,EAAE,QAAQ,OAAO,GAAG,MAAM,QAAQ;AAAA,QACpD;AAAA,MACJ,WACS,YAAY,QAAQ,QAAQ,UAAU,MAAM,IAAI;AACrD,YAAI,UAAU;AACV,kBAAQ,IAAI,MAAM,OAAO,MAAM,OAAO,EAAE;AAC5C,YAAI,MAAM,OAAO,MAAM,IAAI;AACvB,cAAI,OAAO,QAAQ,OAAO,SAAS;AACnC,cAAI,QAAQ,KAAK,QAAQ,OAAO,IAAI,EAAE,MAAM,MAAM;AAC9C,oBAAQ,OAAO,IAAI,IAAI,EAAE,MAAM,QAAQ,OAAO,IAAI,EAAE,MAAM,IAAI,MAAM,GAAG;AAAA;AAEvE,oBAAQ,OAAO,KAAK,KAAK;AAAA,QACjC;AAAA,MACJ;AACA,UAAI,SAAS,OAAO,WAAW,GAAG;AAC9B,YAAI;AACA,kBAAQ;AACZ,YAAI;AACA,kBAAQ;AAAA,MAChB,OACK;AACD,mBAAS;AACL,cAAI,OAAO,YAAY;AACnB;AACJ,cAAI,CAAC,OAAO,OAAO;AACf,kBAAM;AACV,cAAI,WAAW,CAAC,EAAE,QAAQ,OAAO;AAC7B,gBAAI,SAAS,YAAY,KAAK,QAAQ,QAAQ,MAAM;AACpD,gBAAI,OAAO,QAAQ;AACf,0BAAY,MAAM;AAClB,mBAAK,MAAM,OAAO,QAAQ,OAAO,GAAG,IAAI,WAAW,QAAQ,QAAQ,QAAQ,OAAO,WAAW,KAAK,OAAO,eAAe,QAAQ,QAAQ,MAAM,GAAG,MAAM,GAAG,QAAQ,OAAO,IAAI,OAAK,IAAI,MAAM,EAAE,OAAO,QAAQ,OAAO,EAAE,KAAK,QAAQ,KAAK,CAAC,GAAG,QAAQ,QAAQ,OAAO,CAAC,EAAE,IAAI,CAAC;AAAA,YAC/Q;AACA,sBAAU,QAAQ;AAAA,UACtB;AACA,cAAI,WAAW,CAAC,EAAE,QAAQ;AACtB,sBAAU,QAAQ;AAAA,QAC1B;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,SAAS,WAAW,SAAS,MAAM,IAAI;AACnC,WAAS,SAAS,SAAS;AACvB,QAAI,MAAM,QAAQ;AACd;AACJ,QAAI,MAAM,KAAK;AACX,aAAO,MAAM,QAAQ,QAAQ,MAAM,MAAM,KAAK,IAAqB;AAAA,EAC3E;AACA,SAAO;AACX;AAGA,SAAS,SAAS,KAAK,QAAQ,MAAM,OAAO,WAAW,KAAK;AACxD,MAAI,SAAS,MAAM;AACf,QAAI,OAAO,IAAI,OAAO,SAAS,CAAC;AAChC,UAAM,KAAK,IAAI,MAAM,QAAQ,MAAM,IAAI,CAAC;AACxC,cAAU,KAAK,OAAO,GAAG;AAAA,EAC7B;AACJ;AAMA,SAAS,YAAY,QAAQ;AACzB,MAAI,EAAE,KAAK,IAAI,QAAQ,QAAQ,CAAC;AAChC,MAAI,SAAS,KAAK,QAAQ;AAE1B,KAAG;AACC,UAAM,KAAK,OAAO,KAAK;AACvB,WAAO,OAAO;AAAA,EAClB,SAAS,CAAC,OAAO;AAEjB,MAAI,OAAO,OAAO,MAAM,IAAI,KAAK,SAAS,QAAQ,MAAM;AACxD,MAAI,MAAM,KAAK,SAAS,CAAC,GAAG,IAAI,IAAI,QAAQ,WAAW,CAAC,CAAC;AAGzD,WAAS,MAAM,QAAQ,MAAM,MAAM,aAAa,QAAQ,UAAU;AAC9D,QAAI,UAAU,MAAM,QAAQ;AAC5B,QAAI,WAAW,CAAC,GAAG,YAAY,CAAC;AAChC,aAAS,KAAK,QAAQ,SAAS,UAAU,WAAW,WAAW;AAC/D,QAAI,OAAO,EAAE,UAAU,CAAC,GAAG,KAAK,EAAE,UAAU,CAAC;AAC7C,aAAS,KAAK,SAAS,MAAM;AAC7B,QAAI,QAAQ,WACN,MAAM,UAAU,GAAG,EAAE,UAAU,CAAC,GAAG,IAAI,IAAI,MAAM,EAAE,OAAO,CAAC,GAAG,MAAM,KAAK,MAAM,WAAW,CAAC,IAC3F,KAAK,OAAO;AAClB,aAAS,KAAK,KAAK;AACnB,cAAU,KAAK,OAAO,WAAW;AACjC,aAAS,KAAK,EAAE,UAAU,CAAC,GAAG,MAAM,UAAU,WAAW,WAAW;AACpE,WAAO,IAAI,KAAK,MAAM,UAAU,WAAW,MAAM;AAAA,EACrD;AACA,OAAK,SAAS,CAAC,IAAI,MAAM,GAAG,EAAE,QAAQ,SAAS,MAAM,GAAG,IAAI,QAAQ,MAAM,SAAS,CAAC;AAEpF,WAAS,SAAS,UAAU;AACxB,QAAI,OAAO,OAAO,KAAK,SAAS,KAAK,GAAG,MAAM,OAAO,KAAK,UAAU,KAAK;AACzE,WAAO,MAAM,IAAI,SAAS,MAAM,MAAM,OAAO,MAAM,OAAO,OAAO,KAAK,CAAC;AAAA,EAC3E;AACJ;AACA,IAAM,kBAAN,MAAsB;AAAA,EAClB,YAAY,MAAM,QAAQ;AACtB,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,SAAS,KAAK,OAAO,SAAS,mBAAmB,SAAS,YAAY;AAAA,EAC/E;AAAA;AAAA,EAEA,OAAO,KAAK;AACR,QAAI,EAAE,OAAO,IAAI,MAAM,IAAI,MAAM,KAAK;AACtC,WAAO,CAAC,KAAK,QAAQ,OAAO,OAAO,GAAG;AAClC,UAAI,OAAO,MAAM,OAAO,OAAO,MAAM,GAAG,GAAG,SAAS,iBAAiB,SAAS,cAAc,EAAG;AAAA,eACtF,CAAC,OAAO,KAAK,KAAK;AACvB,aAAK,OAAO;AAAA,IACpB;AAAA,EACJ;AAAA,EACA,QAAQ,QAAQ;AACZ,SAAK,OAAO,OAAO,IAAI;AACvB,QAAI,CAAC,KAAK,QAAQ,KAAK,OAAO,OAAO,KAAK,UAAU,OAAO,QAAQ,KAAK,OAAO,MAAM;AACjF,eAAS,OAAO,KAAK,OAAO,UAAQ;AAChC,YAAI,QAAQ,OAAO;AACf,iBAAO;AACX,YAAI,KAAK,SAAS,UAAU,KAAK,UAAU,CAAC,KAAK,KAAK,KAAK,SAAS,CAAC,aAAa;AAC9E,iBAAO,KAAK,SAAS,CAAC;AAAA;AAEtB;AAAA,MACR;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;AACA,IAAM,iBAAN,MAAqB;AAAA,EACjB,YAAY,WAAW;AACnB,QAAIhB;AACJ,SAAK,YAAY;AACjB,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,QAAI,UAAU,QAAQ;AAClB,UAAI,QAAQ,KAAK,UAAU,UAAU,CAAC;AACtC,WAAK,SAASA,MAAK,MAAM,KAAK,KAAK,YAAY,OAAO,QAAQA,QAAO,SAASA,MAAK,MAAM;AACzF,WAAK,QAAQ,IAAI,gBAAgB,MAAM,MAAM,CAAC,MAAM,MAAM;AAAA,IAC9D,OACK;AACD,WAAK,UAAU,KAAK,QAAQ;AAAA,IAChC;AAAA,EACJ;AAAA,EACA,QAAQ,MAAM;AACV,WAAO,KAAK,WAAW,KAAK,QAAQ,KAAK;AACrC,WAAK,SAAS;AAClB,WAAO,KAAK,WAAW,KAAK,QAAQ,QAAQ,KAAK,QAAQ,KAAK,SAAS,KAAK,MAAM,KAAK,MAAM,QAAQ,IAAI;AAAA,EAC7G;AAAA,EACA,WAAW;AACP,QAAIA;AACJ,SAAK;AACL,QAAI,KAAK,SAAS,KAAK,UAAU,QAAQ;AACrC,WAAK,UAAU,KAAK,QAAQ;AAAA,IAChC,OACK;AACD,UAAI,OAAO,KAAK,UAAU,KAAK,UAAU,KAAK,KAAK;AACnD,WAAK,SAASA,MAAK,KAAK,KAAK,KAAK,YAAY,OAAO,QAAQA,QAAO,SAASA,MAAK,KAAK;AACvF,WAAK,QAAQ,IAAI,gBAAgB,KAAK,MAAM,CAAC,KAAK,MAAM;AAAA,IAC5D;AAAA,EACJ;AAAA,EACA,WAAW,KAAK,QAAQ;AACpB,QAAIA;AACJ,QAAI,SAAS,CAAC;AACd,QAAI,KAAK,OAAO;AACZ,WAAK,MAAM,OAAO,OAAO,KAAK,CAAC;AAC/B,eAASiB,OAAM,KAAK,MAAM,OAAO,MAAMA,MAAKA,OAAMA,KAAI,QAAQ;AAC1D,YAAI,SAASjB,MAAKiB,KAAI,UAAU,QAAQjB,QAAO,SAAS,SAASA,IAAG,KAAK,SAAS,OAAO;AACzF,YAAI,SAAS,MAAM,UAAU,QAAQ;AACjC,mBAAS,IAAI,KAAK,OAAO,IAAI,KAAK,UAAU,QAAQ,KAAK;AACrD,gBAAI,OAAO,KAAK,UAAU,CAAC;AAC3B,gBAAI,KAAK,QAAQiB,KAAI;AACjB;AACJ,gBAAI,KAAK,QAAQ,KAAK,QAAQ;AAC1B,qBAAO,KAAK;AAAA,gBACR;AAAA,gBACA,KAAKA,KAAI,OAAO,KAAK;AAAA,gBACrB;AAAA,cACJ,CAAC;AAAA,UACT;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;AACA,SAAS,YAAY,OAAO,QAAQ;AAChC,MAAI,OAAO,MAAM,UAAU;AAC3B,WAAS,IAAI,GAAG,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AAC1C,QAAI,UAAU,MAAM,IAAI,CAAC,EAAE,IAAI,QAAQ,MAAM,CAAC,EAAE;AAChD,WAAO,IAAI,QAAQ,QAAQ,KAAK;AAC5B,UAAI,IAAI,QAAQ,CAAC;AACjB,UAAI,EAAE,QAAQ;AACV;AACJ,UAAI,EAAE,MAAM;AACR;AACJ,UAAI,CAAC;AACD,kBAAU,OAAO,OAAO,MAAM;AAClC,UAAI,EAAE,OAAO,SAAS;AAClB,aAAK,CAAC,IAAI,IAAI,MAAM,EAAE,MAAM,OAAO;AACnC,YAAI,EAAE,KAAK;AACP,eAAK,OAAO,IAAI,GAAG,GAAG,IAAI,MAAM,OAAO,EAAE,EAAE,CAAC;AAAA,MACpD,WACS,EAAE,KAAK,OAAO;AACnB,aAAK,GAAG,IAAI,IAAI,MAAM,OAAO,EAAE,EAAE;AAAA,MACrC,OACK;AACD,aAAK,OAAO,KAAK,CAAC;AAAA,MACtB;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,iBAAiB,GAAG,GAAG,MAAM,IAAI;AACtC,MAAI,KAAK,GAAG,KAAK,GAAG,MAAM,OAAO,MAAM,OAAO,MAAM;AACpD,MAAI,SAAS,CAAC;AACd,aAAS;AACL,QAAI,QAAQ,MAAM,EAAE,SAAS,MAAM,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE;AAC1D,QAAI,QAAQ,MAAM,EAAE,SAAS,MAAM,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE;AAC1D,QAAI,OAAO,KAAK;AACZ,UAAI,QAAQ,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,KAAK,IAAI,OAAO,OAAO,EAAE;AAChE,UAAI,QAAQ;AACR,eAAO,KAAK,IAAI,MAAM,OAAO,GAAG,CAAC;AAAA,IACzC;AACA,UAAM,KAAK,IAAI,OAAO,KAAK;AAC3B,QAAI,OAAO;AACP;AACJ,QAAI,SAAS,KAAK;AACd,UAAI,CAAC;AACD,cAAM;AAAA,WACL;AACD,cAAM;AACN;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,SAAS,KAAK;AACd,UAAI,CAAC;AACD,cAAM;AAAA,WACL;AACD,cAAM;AACN;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAIA,SAAS,eAAe,QAAQ,QAAQ;AACpC,MAAI,SAAS,CAAC;AACd,WAAS,EAAE,KAAK,OAAO,KAAK,KAAK,QAAQ;AACrC,QAAI,WAAW,OAAO,MAAM,UAAU,MAAM,QAAQ,CAAC,EAAE,OAAO,IAAI,SAAS,WAAW,MAAM,KAAK;AACjG,QAAI,OAAO,KAAK,IAAI,KAAK,MAAM,QAAQ,GAAG,KAAK,KAAK,IAAI,KAAK,IAAI,MAAM;AACvE,QAAI,MAAM,SAAS;AACf,UAAI,UAAU,MAAM,QAAQ,IAAI,OAAK,IAAI,MAAM,EAAE,OAAO,KAAK,EAAE,KAAK,GAAG,CAAC;AACxE,UAAI,UAAU,iBAAiB,QAAQ,SAAS,MAAM,EAAE;AACxD,eAAS,IAAI,GAAGA,OAAM,QAAO,KAAK;AAC9B,YAAI,OAAO,KAAK,QAAQ,QAAQ,MAAM,OAAO,KAAK,QAAQ,CAAC,EAAE;AAC7D,YAAI,MAAMA;AACN,iBAAO,KAAK,IAAI,aAAaA,MAAK,KAAK,MAAM,MAAM,CAAC,UAAU,KAAK,QAAQA,QAAO,KAAK,WAAW,KAAK,MAAM,OAAO,KAAK,OAAO,CAAC;AACrI,YAAI;AACA;AACJ,QAAAA,OAAM,QAAQ,CAAC,EAAE;AAAA,MACrB;AAAA,IACJ,OACK;AACD,aAAO,KAAK,IAAI,aAAa,MAAM,IAAI,MAAM,MAAM,CAAC,UAAU,KAAK,QAAQ,YAAY,KAAK,WAAW,KAAK,MAAM,UAAU,KAAK,OAAO,CAAC;AAAA,IAC7I;AAAA,EACJ;AACA,SAAO;AACX;;;ACnnEA,IAAI,YAAY;AAoBhB,IAAM,MAAN,MAAM,KAAI;AAAA;AAAA;AAAA;AAAA,EAIN,YAIAC,OAKA,KAKA,MAIA,UAAU;AACN,SAAK,OAAOA;AACZ,SAAK,MAAM;AACX,SAAK,OAAO;AACZ,SAAK,WAAW;AAIhB,SAAK,KAAK;AAAA,EACd;AAAA,EACA,WAAW;AACP,QAAI,EAAE,MAAAA,MAAK,IAAI;AACf,aAAS,OAAO,KAAK;AACjB,UAAI,IAAI;AACJ,QAAAA,QAAO,GAAG,IAAI,IAAI,IAAIA,KAAI;AAClC,WAAOA;AAAA,EACX;AAAA,EACA,OAAO,OAAO,cAAc,QAAQ;AAChC,QAAIA,QAAO,OAAO,gBAAgB,WAAW,eAAe;AAC5D,QAAI,wBAAwB;AACxB,eAAS;AACb,QAAI,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO;AACvD,YAAM,IAAI,MAAM,oCAAoC;AACxD,QAAI,MAAM,IAAI,KAAIA,OAAM,CAAC,GAAG,MAAM,CAAC,CAAC;AACpC,QAAI,IAAI,KAAK,GAAG;AAChB,QAAI;AACA,eAASC,MAAK,OAAO;AACjB,YAAI,IAAI,KAAKA,EAAC;AACtB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,OAAO,eAAeD,OAAM;AACxB,QAAI,MAAM,IAAI,SAASA,KAAI;AAC3B,WAAO,CAAC,QAAQ;AACZ,UAAI,IAAI,SAAS,QAAQ,GAAG,IAAI;AAC5B,eAAO;AACX,aAAO,SAAS,IAAI,IAAI,QAAQ,KAAK,IAAI,SAAS,OAAO,GAAG,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;AAAA,IAC7F;AAAA,EACJ;AACJ;AACA,IAAI,iBAAiB;AACrB,IAAM,WAAN,MAAM,UAAS;AAAA,EACX,YAAYA,OAAM;AACd,SAAK,OAAOA;AACZ,SAAK,YAAY,CAAC;AAClB,SAAK,KAAK;AAAA,EACd;AAAA,EACA,OAAO,IAAI,MAAM,MAAM;AACnB,QAAI,CAAC,KAAK;AACN,aAAO;AACX,QAAI,SAAS,KAAK,CAAC,EAAE,UAAU,KAAK,CAAAC,OAAKA,GAAE,QAAQ,QAAQ,UAAU,MAAMA,GAAE,QAAQ,CAAC;AACtF,QAAI;AACA,aAAO;AACX,QAAI,MAAM,CAAC,GAAG,MAAM,IAAI,IAAI,KAAK,MAAM,KAAK,MAAM,IAAI;AACtD,aAAS,KAAK;AACV,QAAE,UAAU,KAAK,GAAG;AACxB,QAAI,UAAU,SAAS,IAAI;AAC3B,aAAS,UAAU,KAAK;AACpB,UAAI,CAAC,OAAO,SAAS;AACjB,iBAAS,UAAU;AACf,cAAI,KAAK,UAAS,IAAI,QAAQ,MAAM,CAAC;AACjD,WAAO;AAAA,EACX;AACJ;AACA,SAAS,UAAU,GAAG,GAAG;AACrB,SAAO,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,CAAC,GAAG,MAAM,KAAK,EAAE,CAAC,CAAC;AAC9D;AACA,SAAS,SAAS,OAAO;AACrB,MAAI,OAAO,CAAC,CAAC,CAAC;AACd,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,KAAK;AACzC,WAAK,KAAK,KAAK,CAAC,EAAE,OAAO,MAAM,CAAC,CAAC,CAAC;AAAA,IACtC;AAAA,EACJ;AACA,SAAO,KAAK,KAAK,CAAC,GAAG,MAAM,EAAE,SAAS,EAAE,MAAM;AAClD;AAoDA,SAAS,UAAU,MAAM;AACrB,MAAI,SAAS,uBAAO,OAAO,IAAI;AAC/B,WAAS,QAAQ,MAAM;AACnB,QAAIC,QAAO,KAAK,IAAI;AACpB,QAAI,CAAC,MAAM,QAAQA,KAAI;AACnB,MAAAA,QAAO,CAACA,KAAI;AAChB,aAAS,QAAQ,KAAK,MAAM,GAAG;AAC3B,UAAI,MAAM;AACN,YAAI,SAAS,CAAC,GAAG,OAAO,GAAqB,OAAO;AACpD,iBAAS,MAAM,OAAK;AAChB,cAAI,QAAQ,SAAS,MAAM,KAAK,MAAM,KAAK,KAAK,QAAQ;AACpD,mBAAO;AACP;AAAA,UACJ;AACA,cAAI,IAAI,8BAA8B,KAAK,IAAI;AAC/C,cAAI,CAAC;AACD,kBAAM,IAAI,WAAW,mBAAmB,IAAI;AAChD,iBAAO,KAAK,EAAE,CAAC,KAAK,MAAM,KAAK,EAAE,CAAC,EAAE,CAAC,KAAK,MAAM,KAAK,MAAM,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;AACvE,iBAAO,EAAE,CAAC,EAAE;AACZ,cAAI,OAAO,KAAK;AACZ;AACJ,cAAI,OAAO,KAAK,KAAK;AACrB,cAAI,OAAO,KAAK,UAAU,QAAQ,KAAK;AACnC,mBAAO;AACP;AAAA,UACJ;AACA,cAAI,QAAQ;AACR,kBAAM,IAAI,WAAW,mBAAmB,IAAI;AAChD,iBAAO,KAAK,MAAM,GAAG;AAAA,QACzB;AACA,YAAI,OAAO,OAAO,SAAS,GAAG,QAAQ,OAAO,IAAI;AACjD,YAAI,CAAC;AACD,gBAAM,IAAI,WAAW,mBAAmB,IAAI;AAChD,YAAI,OAAO,IAAI,KAAKA,OAAM,MAAM,OAAO,IAAI,OAAO,MAAM,GAAG,IAAI,IAAI,IAAI;AACvE,eAAO,KAAK,IAAI,KAAK,KAAK,OAAO,KAAK,CAAC;AAAA,MAC3C;AAAA,EACR;AACA,SAAO,aAAa,IAAI,MAAM;AAClC;AACA,IAAM,eAAe,IAAI,SAAS;AAClC,IAAM,OAAN,MAAW;AAAA,EACP,YAAYA,OAAM,MAAM,SAAS,MAAM;AACnC,SAAK,OAAOA;AACZ,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,IAAI,SAAS;AAAE,WAAO,KAAK,QAAQ;AAAA,EAAqB;AAAA,EACxD,IAAI,UAAU;AAAE,WAAO,KAAK,QAAQ;AAAA,EAAsB;AAAA,EAC1D,KAAK,OAAO;AACR,QAAI,CAAC,SAAS,MAAM,QAAQ,KAAK,OAAO;AACpC,WAAK,OAAO;AACZ,aAAO;AAAA,IACX;AACA,UAAM,OAAO,KAAK,KAAK,MAAM,IAAI;AACjC,WAAO;AAAA,EACX;AAAA,EACA,IAAI,QAAQ;AAAE,WAAO,KAAK,UAAU,KAAK,QAAQ,SAAS;AAAA,EAAG;AACjE;AACA,KAAK,QAAQ,IAAI,KAAK,CAAC,GAAG,GAAqB,IAAI;AAMnD,SAAS,eAAeA,OAAM,SAAS;AACnC,MAAI,MAAM,uBAAO,OAAO,IAAI;AAC5B,WAAS,SAASA,OAAM;AACpB,QAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AACxB,UAAI,MAAM,IAAI,EAAE,IAAI,MAAM;AAAA;AAE1B,eAAS,OAAO,MAAM;AAClB,YAAI,IAAI,EAAE,IAAI,MAAM;AAAA,EAChC;AACA,MAAI,EAAE,OAAO,MAAM,KAAK,IAAI,WAAW,CAAC;AACxC,SAAO;AAAA,IACH,OAAO,CAACA,UAAS;AACb,UAAI,MAAM;AACV,eAAS,OAAOA,OAAM;AAClB,iBAAS,OAAO,IAAI,KAAK;AACrB,cAAI,WAAW,IAAI,IAAI,EAAE;AACzB,cAAI,UAAU;AACV,kBAAM,MAAM,MAAM,MAAM,WAAW;AACnC;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA,IACA;AAAA,EACJ;AACJ;AACA,SAAS,cAAc,cAAcA,OAAM;AACvC,MAAI,SAAS;AACb,WAAS,eAAe,cAAc;AAClC,QAAI,QAAQ,YAAY,MAAMA,KAAI;AAClC,QAAI;AACA,eAAS,SAAS,SAAS,MAAM,QAAQ;AAAA,EACjD;AACA,SAAO;AACX;AAOA,SAAS,cAAc,MAAM,aAM7B,UAIA,OAAO,GAIP,KAAK,KAAK,QAAQ;AACd,MAAI,UAAU,IAAI,iBAAiB,MAAM,MAAM,QAAQ,WAAW,IAAI,cAAc,CAAC,WAAW,GAAG,QAAQ;AAC3G,UAAQ,eAAe,KAAK,OAAO,GAAG,MAAM,IAAI,IAAI,QAAQ,YAAY;AACxE,UAAQ,MAAM,EAAE;AACpB;AA8BA,IAAM,mBAAN,MAAuB;AAAA,EACnB,YAAY,IAAI,cAAc,MAAM;AAChC,SAAK,KAAK;AACV,SAAK,eAAe;AACpB,SAAK,OAAO;AACZ,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,UAAU,IAAI,KAAK;AACf,QAAI,OAAO,KAAK,OAAO;AACnB,WAAK,MAAM,EAAE;AACb,UAAI,KAAK,KAAK;AACV,aAAK,KAAK;AACd,WAAK,QAAQ;AAAA,IACjB;AAAA,EACJ;AAAA,EACA,MAAM,IAAI;AACN,QAAI,KAAK,KAAK,MAAM,KAAK;AACrB,WAAK,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK;AAAA,EACzC;AAAA,EACA,eAAe,QAAQ,MAAM,IAAI,gBAAgB,cAAc;AAC3D,QAAI,EAAE,MAAM,MAAM,OAAO,IAAI,IAAI,IAAI;AACrC,QAAI,SAAS,MAAM,OAAO;AACtB;AACJ,QAAI,KAAK;AACL,qBAAe,KAAK,aAAa,OAAO,OAAK,CAAC,EAAE,SAAS,EAAE,MAAM,IAAI,CAAC;AAC1E,QAAI,MAAM;AACV,QAAI,OAAO,aAAa,MAAM,KAAK,KAAK;AACxC,QAAI,SAAS,cAAc,cAAc,KAAK,IAAI;AAClD,QAAI,QAAQ;AACR,UAAI;AACA,eAAO;AACX,aAAO;AACP,UAAI,KAAK,QAAQ;AACb,2BAAmB,iBAAiB,MAAM,MAAM;AAAA,IACxD;AACA,SAAK,UAAU,KAAK,IAAI,MAAM,KAAK,GAAG,GAAG;AACzC,QAAI,KAAK;AACL;AACJ,QAAI,UAAU,OAAO,QAAQ,OAAO,KAAK,KAAK,SAAS,OAAO;AAC9D,QAAI,WAAW,QAAQ,SAAS;AAC5B,UAAI,QAAQ,OAAO,KAAK,MAAM,QAAQ,QAAQ,CAAC,EAAE,OAAO,OAAO,CAAC;AAChE,UAAI,oBAAoB,KAAK,aAAa,OAAO,OAAK,CAAC,EAAE,SAAS,EAAE,MAAM,QAAQ,KAAK,IAAI,CAAC;AAC5F,UAAIC,YAAW,OAAO,WAAW;AACjC,eAAS,IAAI,GAAG,MAAM,SAAQ,KAAK;AAC/B,YAAI,OAAO,IAAI,QAAQ,QAAQ,SAAS,QAAQ,QAAQ,CAAC,IAAI;AAC7D,YAAI,UAAU,OAAO,KAAK,OAAO,QAAQ;AACzC,YAAI,YAAY,KAAK,IAAI,MAAM,GAAG,GAAG,UAAU,KAAK,IAAI,IAAI,OAAO;AACnE,YAAI,YAAY,WAAWA,WAAU;AACjC,iBAAO,OAAO,OAAO,SAAS;AAC1B,iBAAK,eAAe,QAAQ,WAAW,SAAS,gBAAgB,YAAY;AAC5E,iBAAK,UAAU,KAAK,IAAI,SAAS,OAAO,EAAE,GAAG,GAAG;AAChD,gBAAI,OAAO,MAAM,WAAW,CAAC,OAAO,YAAY;AAC5C;AAAA,UACR;AAAA,QACJ;AACA,YAAI,CAAC,QAAQ,UAAU;AACnB;AACJ,cAAM,KAAK,KAAK;AAChB,YAAI,MAAM,MAAM;AACZ,eAAK,eAAe,MAAM,OAAO,GAAG,KAAK,IAAI,MAAM,KAAK,OAAO,KAAK,GAAG,KAAK,IAAI,IAAI,GAAG,GAAG,IAAI,iBAAiB;AAC/G,eAAK,UAAU,KAAK,IAAI,IAAI,GAAG,GAAG,GAAG;AAAA,QACzC;AAAA,MACJ;AACA,UAAIA;AACA,eAAO,OAAO;AAAA,IACtB,WACS,OAAO,WAAW,GAAG;AAC1B,UAAI;AACA,yBAAiB;AACrB,SAAG;AACC,YAAI,OAAO,MAAM;AACb;AACJ,YAAI,OAAO,QAAQ;AACf;AACJ,aAAK,eAAe,QAAQ,MAAM,IAAI,gBAAgB,YAAY;AAClE,aAAK,UAAU,KAAK,IAAI,IAAI,OAAO,EAAE,GAAG,GAAG;AAAA,MAC/C,SAAS,OAAO,YAAY;AAC5B,aAAO,OAAO;AAAA,IAClB;AAAA,EACJ;AACJ;AAMA,SAAS,aAAa,MAAM;AACxB,MAAI,OAAO,KAAK,KAAK,KAAK,YAAY;AACtC,SAAO,QAAQ,KAAK,WAAW,CAAC,KAAK,aAAa,KAAK,OAAO;AAC1D,WAAO,KAAK;AAChB,SAAO,QAAQ;AACnB;AACA,IAAM,IAAI,IAAI;AACd,IAAM,UAAU,EAAE;AAAlB,IAAqB,OAAO,EAAE;AAA9B,IAAiC,WAAW,EAAE,IAAI;AAAlD,IAAqD,eAAe,EAAE,IAAI;AAA1E,IAA6E,UAAU,EAAE;AAAzF,IAA4F,SAAS,EAAE,OAAO;AAA9G,IAAiH,SAAS,EAAE,OAAO;AAAnI,IAAsI,UAAU,EAAE;AAAlJ,IAAqJ,UAAU,EAAE,OAAO;AAAxK,IAA2K,UAAU,EAAE;AAAvL,IAA0L,WAAW,EAAE;AAAvM,IAA0M,cAAc,EAAE;AAA1N,IAA6N,UAAU,EAAE,WAAW;AAApP,IAAuP,OAAO,EAAE;AAqBhQ,IAAM,OAAO;AAAA;AAAA;AAAA;AAAA,EAIT;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA,EAItB,cAAc,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA,EAIvB,YAAY,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA,EAIrB;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,EAAE,IAAI;AAAA;AAAA;AAAA;AAAA,EAIpB;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,EAAE,QAAQ;AAAA;AAAA;AAAA;AAAA,EAInB;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe,EAAE,YAAY;AAAA;AAAA;AAAA;AAAA,EAI7B,WAAW,EAAE,IAAI;AAAA;AAAA;AAAA;AAAA,EAIjB,WAAW,EAAE,IAAI;AAAA;AAAA;AAAA;AAAA,EAIjB,WAAW,EAAE,IAAI;AAAA;AAAA;AAAA;AAAA,EAIjB,WAAW,EAAE,IAAI;AAAA;AAAA;AAAA;AAAA,EAIjB;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,EAAE,MAAM;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,EAAE,MAAM;AAAA;AAAA;AAAA;AAAA,EAInB,gBAAgB,EAAE,MAAM;AAAA;AAAA;AAAA;AAAA,EAIxB;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,EAAE,MAAM;AAAA;AAAA;AAAA;AAAA,EAIjB,OAAO,EAAE,MAAM;AAAA;AAAA;AAAA;AAAA,EAIf,MAAM,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA,EAIf,QAAQ,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,QAAQ,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA,EAIjB,OAAO,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA,EAIhB,KAAK,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA,EAId;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA,EAIf,MAAM,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA,EAIf,MAAM,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA,EAIf,MAAM,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA,EAIf,UAAU,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA,EAInB,iBAAiB,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA,EAI1B,gBAAgB,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA,EAIzB,mBAAmB,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5B,eAAe,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA,EAIxB;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe,EAAE,QAAQ;AAAA;AAAA;AAAA;AAAA,EAIzB,oBAAoB,EAAE,QAAQ;AAAA;AAAA;AAAA;AAAA,EAI9B,eAAe,EAAE,QAAQ;AAAA;AAAA;AAAA;AAAA,EAIzB,iBAAiB,EAAE,QAAQ;AAAA;AAAA;AAAA;AAAA,EAI3B,iBAAiB,EAAE,QAAQ;AAAA;AAAA;AAAA;AAAA,EAI3B,gBAAgB,EAAE,QAAQ;AAAA;AAAA;AAAA;AAAA,EAI1B,oBAAoB,EAAE,QAAQ;AAAA;AAAA;AAAA;AAAA,EAI9B,cAAc,EAAE,QAAQ;AAAA;AAAA;AAAA;AAAA,EAIxB,iBAAiB,EAAE,QAAQ;AAAA;AAAA;AAAA;AAAA,EAI3B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,EAAE,WAAW;AAAA;AAAA;AAAA;AAAA,EAIxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,eAAe,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxB,OAAO,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhB,OAAO,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA,EAIhB;AAAA;AAAA;AAAA;AAAA,EAIA;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA,EAInB,UAAU,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA,EAInB,UAAU,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA,EAInB,UAAU,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA,EAInB,UAAU,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA,EAInB,UAAU,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA,EAInB,kBAAkB,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA,EAI3B,MAAM,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA,EAIf,OAAO,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA,EAIhB,UAAU,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA,EAInB,QAAQ,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA,EAIjB,MAAM,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKf,WAAW,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,eAAe,EAAE,OAAO;AAAA;AAAA;AAAA;AAAA,EAIxB,UAAU,EAAE;AAAA;AAAA;AAAA;AAAA,EAIZ,SAAS,EAAE;AAAA;AAAA;AAAA;AAAA,EAIX,SAAS,EAAE;AAAA;AAAA;AAAA;AAAA,EAIX,SAAS,EAAE;AAAA;AAAA;AAAA;AAAA,EAIX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,EAAE,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,YAAY,EAAE,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlB,uBAAuB,EAAE,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM7B,YAAY,IAAI,eAAe,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM3C,UAAU,IAAI,eAAe,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOvC,UAAU,IAAI,eAAe,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMvC,UAAU,IAAI,eAAe,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvC,OAAO,IAAI,eAAe,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASjC,SAAS,IAAI,eAAe,SAAS;AACzC;AACA,SAASC,SAAQ,MAAM;AACnB,MAAI,MAAM,KAAKA,KAAI;AACnB,MAAI,eAAe;AACf,QAAI,OAAOA;AACnB;AAiDA,IAAM,mBAAmB,eAAe;AAAA,EACpC,EAAE,KAAK,KAAK,MAAM,OAAO,WAAW;AAAA,EACpC,EAAE,KAAK,KAAK,SAAS,OAAO,cAAc;AAAA,EAC1C,EAAE,KAAK,KAAK,UAAU,OAAO,eAAe;AAAA,EAC5C,EAAE,KAAK,KAAK,QAAQ,OAAO,aAAa;AAAA,EACxC,EAAE,KAAK,KAAK,SAAS,OAAO,cAAc;AAAA,EAC1C,EAAE,KAAK,KAAK,MAAM,OAAO,WAAW;AAAA,EACpC,EAAE,KAAK,KAAK,MAAM,OAAO,WAAW;AAAA,EACpC,EAAE,KAAK,KAAK,KAAK,OAAO,UAAU;AAAA,EAClC,EAAE,KAAK,KAAK,WAAW,OAAO,gBAAgB;AAAA,EAC9C,EAAE,KAAK,KAAK,UAAU,OAAO,eAAe;AAAA,EAC5C,EAAE,KAAK,KAAK,SAAS,OAAO,cAAc;AAAA,EAC1C,EAAE,KAAK,KAAK,SAAS,OAAO,cAAc;AAAA,EAC1C,EAAE,KAAK,KAAK,QAAQ,OAAO,aAAa;AAAA,EACxC,EAAE,KAAK,KAAK,QAAQ,OAAO,aAAa;AAAA,EACxC,EAAE,KAAK,CAAC,KAAK,QAAQ,KAAK,QAAQ,KAAK,QAAQ,KAAK,MAAM,CAAC,GAAG,OAAO,cAAc;AAAA,EACnF,EAAE,KAAK,KAAK,cAAc,OAAO,mBAAmB;AAAA,EACpD,EAAE,KAAK,KAAK,MAAM,KAAK,YAAY,GAAG,OAAO,6BAA6B;AAAA,EAC1E,EAAE,KAAK,KAAK,WAAW,KAAK,YAAY,GAAG,OAAO,kCAAkC;AAAA,EACpF,EAAE,KAAK,KAAK,QAAQ,KAAK,YAAY,GAAG,OAAO,oBAAoB;AAAA,EACnE,EAAE,KAAK,KAAK,WAAW,KAAK,YAAY,GAAG,OAAO,kCAAkC;AAAA,EACpF,EAAE,KAAK,KAAK,UAAU,OAAO,eAAe;AAAA,EAC5C,EAAE,KAAK,KAAK,WAAW,OAAO,gBAAgB;AAAA,EAC9C,EAAE,KAAK,KAAK,WAAW,OAAO,gBAAgB;AAAA,EAC9C,EAAE,KAAK,KAAK,WAAW,OAAO,gBAAgB;AAAA,EAC9C,EAAE,KAAK,KAAK,cAAc,OAAO,mBAAmB;AAAA,EACpD,EAAE,KAAK,KAAK,UAAU,OAAO,eAAe;AAAA,EAC5C,EAAE,KAAK,KAAK,SAAS,OAAO,cAAc;AAAA,EAC1C,EAAE,KAAK,KAAK,MAAM,OAAO,WAAW;AAAA,EACpC,EAAE,KAAK,KAAK,SAAS,OAAO,cAAc;AAAA,EAC1C,EAAE,KAAK,KAAK,aAAa,OAAO,kBAAkB;AACtD,CAAC;;;AC/3BD,IAAI;AAKJ,IAAM,mBAAgC,IAAI,SAAS;AASnD,SAAS,oBAAoB,UAAU;AACnC,SAAO,MAAM,OAAO;AAAA,IAChB,SAAS,WAAW,YAAU,OAAO,OAAO,QAAQ,IAAI;AAAA,EAC5D,CAAC;AACL;AAKA,IAAM,kBAA+B,IAAI,SAAS;AAUlD,IAAM,WAAN,MAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQX,YAKA,MAAM,QAAQ,kBAAkB,CAAC,GAIjCC,QAAO,IAAI;AACP,SAAK,OAAO;AACZ,SAAK,OAAOA;AAIZ,QAAI,CAAC,YAAY,UAAU,eAAe,MAAM;AAC5C,aAAO,eAAe,YAAY,WAAW,QAAQ,EAAE,MAAM;AAAE,eAAO,WAAW,IAAI;AAAA,MAAG,EAAE,CAAC;AAC/F,SAAK,SAAS;AACd,SAAK,YAAY;AAAA,MACb,SAAS,GAAG,IAAI;AAAA,MAChB,YAAY,aAAa,GAAG,CAAC,OAAO,KAAK,SAAS;AAC9C,YAAI,MAAM,UAAU,OAAO,KAAK,IAAI,GAAGC,QAAO,IAAI,KAAK,KAAK,gBAAgB;AAC5E,YAAI,CAACA;AACD,iBAAO,CAAC;AACZ,YAAI,OAAO,MAAM,MAAMA,KAAI,GAAG,MAAM,IAAI,KAAK,KAAK,eAAe;AACjE,YAAI,KAAK;AACL,cAAI,YAAY,IAAI,QAAQ,MAAM,IAAI,MAAM,IAAI;AAChD,mBAAS,WAAW;AAChB,gBAAI,QAAQ,KAAK,WAAW,KAAK,GAAG;AAChC,kBAAIA,QAAO,MAAM,MAAM,QAAQ,KAAK;AACpC,qBAAO,QAAQ,QAAQ,YAAYA,QAAOA,MAAK,OAAO,IAAI;AAAA,YAC9D;AAAA,QACR;AACA,eAAO;AAAA,MACX,CAAC;AAAA,IACL,EAAE,OAAO,eAAe;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,OAAO,KAAK,OAAO,IAAI;AAC9B,WAAO,UAAU,OAAO,KAAK,IAAI,EAAE,KAAK,KAAK,gBAAgB,KAAK,KAAK;AAAA,EAC3E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,OAAO;AACf,QAAI,OAAO,MAAM,MAAM,QAAQ;AAC/B,SAAK,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,SAAS,KAAK;AAChE,aAAO,CAAC,EAAE,MAAM,GAAG,IAAI,MAAM,IAAI,OAAO,CAAC;AAC7C,QAAI,CAAC,QAAQ,CAAC,KAAK;AACf,aAAO,CAAC;AACZ,QAAI,SAAS,CAAC;AACd,QAAI,UAAU,CAAC,MAAM,SAAS;AAC1B,UAAI,KAAK,KAAK,gBAAgB,KAAK,KAAK,MAAM;AAC1C,eAAO,KAAK,EAAE,MAAM,IAAI,OAAO,KAAK,OAAO,CAAC;AAC5C;AAAA,MACJ;AACA,UAAI,QAAQ,KAAK,KAAK,SAAS,OAAO;AACtC,UAAI,OAAO;AACP,YAAI,MAAM,KAAK,KAAK,gBAAgB,KAAK,KAAK,MAAM;AAChD,cAAI,MAAM;AACN,qBAAS,KAAK,MAAM;AAChB,qBAAO,KAAK,EAAE,MAAM,EAAE,OAAO,MAAM,IAAI,EAAE,KAAK,KAAK,CAAC;AAAA;AAExD,mBAAO,KAAK,EAAE,MAAY,IAAI,OAAO,KAAK,OAAO,CAAC;AACtD;AAAA,QACJ,WACS,MAAM,SAAS;AACpB,cAAI,OAAO,OAAO;AAClB,kBAAQ,MAAM,MAAM,MAAM,QAAQ,CAAC,EAAE,OAAO,IAAI;AAChD,cAAI,OAAO,SAAS;AAChB;AAAA,QACR;AAAA,MACJ;AACA,eAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC3C,YAAI,KAAK,KAAK,SAAS,CAAC;AACxB,YAAI,cAAc;AACd,kBAAQ,IAAI,KAAK,UAAU,CAAC,IAAI,IAAI;AAAA,MAC5C;AAAA,IACJ;AACA,YAAQ,WAAW,KAAK,GAAG,CAAC;AAC5B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,gBAAgB;AAAE,WAAO;AAAA,EAAM;AACvC;AAIA,SAAS,WAAwB,YAAY,OAAO;AACpD,SAAS,UAAU,OAAO,KAAK,MAAM;AACjC,MAAI,UAAU,MAAM,MAAM,QAAQ,GAAG,OAAO,WAAW,KAAK,EAAE;AAC9D,MAAI,CAAC,WAAW,QAAQ,eAAe;AACnC,aAAS,OAAO,MAAM,MAAM,OAAO,KAAK,MAAM,KAAK,MAAM,SAAS,cAAc;AAC5E,UAAI,KAAK,KAAK;AACV,eAAO;AAAA,EACnB;AACA,SAAO;AACX;AAMA,IAAM,aAAN,MAAM,oBAAmB,SAAS;AAAA,EAC9B,YAAY,MAAM,QAAQD,OAAM;AAC5B,UAAM,MAAM,QAAQ,CAAC,GAAGA,KAAI;AAC5B,SAAK,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,OAAO,MAAM;AAChB,QAAI,OAAO,oBAAoB,KAAK,YAAY;AAChD,WAAO,IAAI,YAAW,MAAM,KAAK,OAAO,UAAU;AAAA,MAC9C,OAAO,CAAC,iBAAiB,IAAI,UAAQ,KAAK,QAAQ,OAAO,MAAS,CAAC;AAAA,IACvE,CAAC,GAAG,KAAK,IAAI;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,SAASA,OAAM;AACrB,WAAO,IAAI,YAAW,KAAK,MAAM,KAAK,OAAO,UAAU,OAAO,GAAGA,SAAQ,KAAK,IAAI;AAAA,EACtF;AAAA,EACA,IAAI,gBAAgB;AAAE,WAAO,KAAK,OAAO,YAAY;AAAA,EAAG;AAC5D;AAOA,SAAS,WAAW,OAAO;AACvB,MAAI,QAAQ,MAAM,MAAM,SAAS,OAAO,KAAK;AAC7C,SAAO,QAAQ,MAAM,OAAO,KAAK;AACrC;AA0DA,IAAM,WAAN,MAAe;AAAA;AAAA;AAAA;AAAA,EAIX,YAAY,KAAK;AACb,SAAK,MAAM;AACX,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,SAAS,IAAI,KAAK;AAAA,EAC3B;AAAA,EACA,IAAI,SAAS;AAAE,WAAO,KAAK,IAAI;AAAA,EAAQ;AAAA,EACvC,OAAO,KAAK;AACR,SAAK,SAAS,KAAK,OAAO,KAAK,MAAM,KAAK,SAAS,EAAE;AACrD,SAAK,YAAY,MAAM,KAAK,OAAO;AACnC,WAAO,KAAK,YAAY,KAAK,OAAO;AAAA,EACxC;AAAA,EACA,MAAM,KAAK;AACP,SAAK,OAAO,GAAG;AACf,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,IAAI,aAAa;AAAE,WAAO;AAAA,EAAM;AAAA,EAChC,KAAK,MAAM,IAAI;AACX,QAAI,cAAc,KAAK,YAAY,KAAK,OAAO;AAC/C,QAAI,OAAO,eAAe,MAAM,KAAK;AACjC,aAAO,KAAK,IAAI,YAAY,MAAM,EAAE;AAAA;AAEpC,aAAO,KAAK,OAAO,MAAM,OAAO,aAAa,KAAK,WAAW;AAAA,EACrE;AACJ;AACA,IAAI,iBAAiB;AAIrB,IAAM,eAAN,MAAM,cAAa;AAAA,EACf,YAAY,QAIZ,OAIA,YAAY,CAAC,GAIb,MAIA,SASA,UAIA,SAMA,YAAY;AACR,SAAK,SAAS;AACd,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,aAAa;AAClB,SAAK,QAAQ;AAIb,SAAK,cAAc,CAAC;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,OAAO,QAAQ,OAAO,UAAU;AACnC,WAAO,IAAI,cAAa,QAAQ,OAAO,CAAC,GAAG,KAAK,OAAO,GAAG,UAAU,CAAC,GAAG,IAAI;AAAA,EAChF;AAAA,EACA,aAAa;AACT,WAAO,KAAK,OAAO,WAAW,IAAI,SAAS,KAAK,MAAM,GAAG,GAAG,KAAK,SAAS;AAAA,EAC9E;AAAA;AAAA;AAAA;AAAA,EAIA,KAAK,OAAO,MAAM;AACd,QAAI,QAAQ,QAAQ,QAAQ,KAAK,MAAM,IAAI;AACvC,aAAO;AACX,QAAI,KAAK,QAAQ,KAAK,SAAS,KAAK,OAAO,SAAS,QAAQ,SAAS,SAAS,OAAO,KAAK,MAAM,IAAI,MAAM,GAAG;AACzG,WAAK,SAAS;AACd,aAAO;AAAA,IACX;AACA,WAAO,KAAK,YAAY,MAAM;AAC1B,UAAIE;AACJ,UAAI,OAAO,SAAS,UAAU;AAC1B,YAAI,UAAU,KAAK,IAAI,IAAI;AAC3B,gBAAQ,MAAM,KAAK,IAAI,IAAI;AAAA,MAC/B;AACA,UAAI,CAAC,KAAK;AACN,aAAK,QAAQ,KAAK,WAAW;AACjC,UAAI,QAAQ,SAAS,KAAK,MAAM,aAAa,QAAQ,KAAK,MAAM,YAAY,SACxE,OAAO,KAAK,MAAM,IAAI;AACtB,aAAK,MAAM,OAAO,IAAI;AAC1B,iBAAS;AACL,YAAI,OAAO,KAAK,MAAM,QAAQ;AAC9B,YAAI,MAAM;AACN,eAAK,YAAY,KAAK,mBAAmB,aAAa,QAAQ,MAAM,KAAK,WAAW,KAAK,MAAM,aAAa,IAAI,CAAC;AACjH,eAAK,WAAWA,MAAK,KAAK,MAAM,eAAe,QAAQA,QAAO,SAASA,MAAK,KAAK,MAAM,IAAI;AAC3F,eAAK,OAAO;AACZ,eAAK,QAAQ;AACb,cAAI,KAAK,WAAW,SAAS,QAAQ,SAAS,SAAS,OAAO,KAAK,MAAM,IAAI;AACzE,iBAAK,QAAQ,KAAK,WAAW;AAAA;AAE7B,mBAAO;AAAA,QACf;AACA,YAAI,MAAM;AACN,iBAAO;AAAA,MACf;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACP,QAAI,KAAK;AACT,QAAI,KAAK,UAAU,MAAM,KAAK,MAAM,cAAc,KAAK,SAAS;AAC5D,UAAI,KAAK,MAAM,aAAa,QAAQ,KAAK,MAAM,YAAY;AACvD,aAAK,MAAM,OAAO,GAAG;AACzB,WAAK,YAAY,MAAM;AAAE,eAAO,EAAE,OAAO,KAAK,MAAM,QAAQ,IAAI;AAAA,QAAE;AAAA,MAAE,CAAC;AACrE,WAAK,UAAU;AACf,WAAK,OAAO;AACZ,WAAK,YAAY,KAAK,mBAAmB,aAAa,QAAQ,KAAK,MAAM,KAAK,WAAW,IAAI,CAAC;AAC9F,WAAK,QAAQ;AAAA,IACjB;AAAA,EACJ;AAAA,EACA,YAAY,GAAG;AACX,QAAI,OAAO;AACX,qBAAiB;AACjB,QAAI;AACA,aAAO,EAAE;AAAA,IACb,UACA;AACI,uBAAiB;AAAA,IACrB;AAAA,EACJ;AAAA,EACA,mBAAmB,WAAW;AAC1B,aAAS,GAAG,IAAI,KAAK,YAAY,IAAI;AACjC,kBAAY,aAAa,WAAW,EAAE,MAAM,EAAE,EAAE;AACpD,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,SAAS,UAAU;AACvB,QAAI,EAAE,WAAW,MAAM,SAAS,UAAU,QAAQ,IAAI;AACtD,SAAK,SAAS;AACd,QAAI,CAAC,QAAQ,OAAO;AAChB,UAAI,SAAS,CAAC;AACd,cAAQ,kBAAkB,CAAC,OAAO,KAAK,OAAO,QAAQ,OAAO,KAAK,EAAE,OAAO,KAAK,OAAO,IAAI,CAAC,CAAC;AAC7F,kBAAY,aAAa,aAAa,WAAW,MAAM;AACvD,aAAO,KAAK;AACZ,gBAAU;AACV,iBAAW,EAAE,MAAM,QAAQ,OAAO,SAAS,MAAM,EAAE,GAAG,IAAI,QAAQ,OAAO,SAAS,IAAI,CAAC,EAAE;AACzF,UAAI,KAAK,QAAQ,QAAQ;AACrB,kBAAU,CAAC;AACX,iBAAS,KAAK,KAAK,SAAS;AACxB,cAAI,OAAO,QAAQ,OAAO,EAAE,MAAM,CAAC,GAAG,KAAK,QAAQ,OAAO,EAAE,IAAI,EAAE;AAClE,cAAI,OAAO;AACP,oBAAQ,KAAK,EAAE,MAAM,GAAG,CAAC;AAAA,QACjC;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,IAAI,cAAa,KAAK,QAAQ,UAAU,WAAW,MAAM,SAAS,UAAU,SAAS,KAAK,UAAU;AAAA,EAC/G;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe,UAAU;AACrB,QAAI,KAAK,SAAS,QAAQ,SAAS,QAAQ,KAAK,SAAS,MAAM,SAAS;AACpE,aAAO;AACX,SAAK,WAAW;AAChB,QAAI,WAAW,KAAK,QAAQ;AAC5B,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,KAAK;AAC1C,UAAI,EAAE,MAAM,GAAG,IAAI,KAAK,QAAQ,CAAC;AACjC,UAAI,OAAO,SAAS,MAAM,KAAK,SAAS,MAAM;AAC1C,aAAK,YAAY,aAAa,KAAK,WAAW,MAAM,EAAE;AACtD,aAAK,QAAQ,OAAO,KAAK,CAAC;AAAA,MAC9B;AAAA,IACJ;AACA,QAAI,KAAK,QAAQ,UAAU;AACvB,aAAO;AACX,SAAK,MAAM;AACX,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ;AACJ,QAAI,KAAK,OAAO;AACZ,WAAK,SAAS;AACd,WAAK,QAAQ;AAAA,IACjB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,MAAM,IAAI;AACtB,SAAK,QAAQ,KAAK,EAAE,MAAM,GAAG,CAAC;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAO,kBAAkB,OAAO;AAC5B,WAAO,IAAI,cAAc,OAAO;AAAA,MAC5B,YAAY,OAAO,WAAW,QAAQ;AAClC,YAAI,OAAO,OAAO,CAAC,EAAE,MAAM,KAAK,OAAO,OAAO,SAAS,CAAC,EAAE;AAC1D,YAAI,SAAS;AAAA,UACT,WAAW;AAAA,UACX,UAAU;AACN,gBAAI,KAAK;AACT,gBAAI,IAAI;AACJ,uBAAS,KAAK;AACV,mBAAG,YAAY,KAAK,CAAC;AACzB,kBAAI;AACA,mBAAG,aAAa,GAAG,aAAa,QAAQ,IAAI,CAAC,GAAG,YAAY,KAAK,CAAC,IAAI;AAAA,YAC9E;AACA,iBAAK,YAAY;AACjB,mBAAO,IAAI,KAAK,SAAS,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,IAAI;AAAA,UACpD;AAAA,UACA,WAAW;AAAA,UACX,SAAS;AAAA,UAAE;AAAA,QACf;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,MAAM;AACT,WAAO,KAAK,IAAI,MAAM,KAAK,MAAM,IAAI,MAAM;AAC3C,QAAI,QAAQ,KAAK;AACjB,WAAO,KAAK,WAAW,QAAQ,MAAM,UAAU,MAAM,CAAC,EAAE,QAAQ,KAAK,MAAM,CAAC,EAAE,MAAM;AAAA,EACxF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,MAAM;AAAE,WAAO;AAAA,EAAgB;AAC1C;AACA,SAAS,aAAa,WAAW,MAAM,IAAI;AACvC,SAAO,aAAa,aAAa,WAAW,CAAC,EAAE,OAAO,MAAM,KAAK,IAAI,OAAO,MAAM,KAAK,GAAG,CAAC,CAAC;AAChG;AACA,IAAM,gBAAN,MAAM,eAAc;AAAA,EAChB,YAGA,SAAS;AACL,SAAK,UAAU;AACf,SAAK,OAAO,QAAQ;AAAA,EACxB;AAAA,EACA,MAAM,IAAI;AACN,QAAI,CAAC,GAAG,cAAc,KAAK,QAAQ,KAAK,QAAQ;AAC5C,aAAO;AACX,QAAI,QAAQ,KAAK,QAAQ,QAAQ,GAAG,SAAS,GAAG,KAAK;AAIrD,QAAI,OAAO,KAAK,QAAQ,WAAW,GAAG,WAAW,IAAI,SAAS,SACxD,KAAK,IAAI,GAAG,QAAQ,OAAO,KAAK,QAAQ,OAAO,GAAG,MAAM,SAAS,EAAE;AACzE,QAAI,CAAC,MAAM,KAAK,IAAqB,IAAI;AACrC,YAAM,SAAS;AACnB,WAAO,IAAI,eAAc,KAAK;AAAA,EAClC;AAAA,EACA,OAAO,KAAK,OAAO;AACf,QAAI,OAAO,KAAK,IAAI,KAA8B,MAAM,IAAI,MAAM;AAClE,QAAI,aAAa,aAAa,OAAO,MAAM,MAAM,QAAQ,EAAE,QAAQ,OAAO,EAAE,MAAM,GAAG,IAAI,KAAK,CAAC;AAC/F,QAAI,CAAC,WAAW,KAAK,IAAqB,IAAI;AAC1C,iBAAW,SAAS;AACxB,WAAO,IAAI,eAAc,UAAU;AAAA,EACvC;AACJ;AACA,SAAS,QAAqB,WAAW,OAAO;AAAA,EAC5C,QAAQ,cAAc;AAAA,EACtB,OAAO,OAAO,IAAI;AACd,aAAS,KAAK,GAAG;AACb,UAAI,EAAE,GAAG,SAAS,QAAQ;AACtB,eAAO,EAAE;AACjB,QAAI,GAAG,WAAW,MAAM,QAAQ,KAAK,GAAG,MAAM,MAAM,QAAQ;AACxD,aAAO,cAAc,KAAK,GAAG,KAAK;AACtC,WAAO,MAAM,MAAM,EAAE;AAAA,EACzB;AACJ,CAAC;AACD,IAAI,cAAc,CAAC,aAAa;AAC5B,MAAI,UAAU;AAAA,IAAW,MAAM,SAAS;AAAA,IAAG;AAAA;AAAA,EAAuB;AAClE,SAAO,MAAM,aAAa,OAAO;AACrC;AACA,IAAI,OAAO,uBAAuB;AAC9B,gBAAc,CAAC,aAAa;AACxB,QAAI,OAAO,IAAI,UAAU;AAAA,MAAW,MAAM;AACtC,eAAO,oBAAoB,UAAU;AAAA,UAAE,SAAS,MAA0B;AAAA;AAAA,QAAwB,CAAC;AAAA,MACvG;AAAA,MAAG;AAAA;AAAA,IAAuB;AAC1B,WAAO,MAAM,OAAO,IAAI,aAAa,OAAO,IAAI,mBAAmB,IAAI;AAAA,EAC3E;AACJ,IAAM,iBAAiB,OAAO,aAAa,iBAAiB,KAAK,UAAU,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,kBACzH,MAAM,UAAU,WAAW,eAAe,IAAI;AACpD,IAAM,cAA2B,WAAW,UAAU,MAAM,YAAY;AAAA,EACpE,YAAY,MAAM;AACd,SAAK,OAAO;AACZ,SAAK,UAAU;AACf,SAAK,gBAAgB;AAErB,SAAK,WAAW;AAEhB,SAAK,cAAc;AACnB,SAAK,OAAO,KAAK,KAAK,KAAK,IAAI;AAC/B,SAAK,aAAa;AAAA,EACtB;AAAA,EACA,OAAO,QAAQ;AACX,QAAI,KAAK,KAAK,KAAK,MAAM,MAAM,SAAS,KAAK,EAAE;AAC/C,QAAI,GAAG,eAAe,OAAO,KAAK,QAAQ,KAAK,KAAK,KAAK,SAAS,KAAK,GAAG;AACtE,WAAK,aAAa;AACtB,QAAI,OAAO,cAAc,OAAO,cAAc;AAC1C,UAAI,KAAK,KAAK;AACV,aAAK,eAAe;AACxB,WAAK,aAAa;AAAA,IACtB;AACA,SAAK,mBAAmB,EAAE;AAAA,EAC9B;AAAA,EACA,eAAe;AACX,QAAI,KAAK;AACL;AACJ,QAAI,EAAE,MAAM,IAAI,KAAK,MAAM,QAAQ,MAAM,MAAM,SAAS,KAAK;AAC7D,QAAI,MAAM,QAAQ,MAAM,QAAQ,QAAQ,CAAC,MAAM,QAAQ,OAAO,MAAM,IAAI,MAAM;AAC1E,WAAK,UAAU,YAAY,KAAK,IAAI;AAAA,EAC5C;AAAA,EACA,KAAK,UAAU;AACX,SAAK,UAAU;AACf,QAAI,MAAM,KAAK,IAAI;AACnB,QAAI,KAAK,WAAW,QAAQ,KAAK,WAAW,KAAK,KAAK,KAAK,WAAW;AAClE,WAAK,WAAW,MAAM;AACtB,WAAK,cAAc;AAAA,IACvB;AACA,QAAI,KAAK,eAAe;AACpB;AACJ,QAAI,EAAE,OAAO,UAAU,EAAE,IAAI,KAAK,EAAE,IAAI,KAAK,MAAM,QAAQ,MAAM,MAAM,SAAS,KAAK;AACrF,QAAI,MAAM,QAAQ,MAAM,QAAQ,QAAQ,MAAM,QAAQ;AAAA,MAAO,OAAO;AAAA;AAAA,IAA+B;AAC/F;AACJ,QAAI,UAAU,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,aAAa,KAAsB,YAAY,CAAC,iBAAiB,KAAK,IAAI,IAAwB,SAAS,cAAc,IAAI,CAAC,IAAI,GAAG;AAC9K,QAAI,gBAAgB,MAAM,QAAQ,UAAU,QAAQ,MAAM,IAAI,SAAS,OAAO;AAC9E,QAAI,OAAO,MAAM,QAAQ,KAAK,MAAM;AAChC,aAAO,kBAAkB,eAAe,KAAK,KAAK,IAAI,IAAI;AAAA,IAC9D,GAAG,QAAQ,gBAAgB,IAAI,IAAgC;AAC/D,SAAK,eAAe,KAAK,IAAI,IAAI;AACjC,QAAI,QAAQ,KAAK,eAAe,GAAG;AAC/B,YAAM,QAAQ,SAAS;AACvB,WAAK,KAAK,SAAS,EAAE,SAAS,SAAS,SAAS,GAAG,IAAI,cAAc,MAAM,OAAO,CAAC,EAAE,CAAC;AAAA,IAC1F;AACA,QAAI,KAAK,cAAc,KAAK,EAAE,QAAQ,CAAC;AACnC,WAAK,aAAa;AACtB,SAAK,mBAAmB,MAAM,OAAO;AAAA,EACzC;AAAA,EACA,mBAAmB,IAAI;AACnB,QAAI,GAAG,YAAY;AACf,WAAK;AACL,SAAG,WACE,KAAK,MAAM,KAAK,aAAa,CAAC,EAC9B,MAAM,SAAO,aAAa,KAAK,KAAK,OAAO,GAAG,CAAC,EAC/C,KAAK,MAAM,KAAK,eAAe;AACpC,SAAG,aAAa;AAAA,IACpB;AAAA,EACJ;AAAA,EACA,UAAU;AACN,QAAI,KAAK;AACL,WAAK,QAAQ;AAAA,EACrB;AAAA,EACA,YAAY;AACR,WAAO,CAAC,EAAE,KAAK,WAAW,KAAK,gBAAgB;AAAA,EACnD;AACJ,GAAG;AAAA,EACC,eAAe,EAAE,QAAQ;AAAE,SAAK,aAAa;AAAA,EAAG,EAAE;AACtD,CAAC;AAOD,IAAM,WAAwB,MAAM,OAAO;AAAA,EACvC,QAAQ,WAAW;AAAE,WAAO,UAAU,SAAS,UAAU,CAAC,IAAI;AAAA,EAAM;AAAA,EACpE,SAAS,CAAAC,cAAY;AAAA,IACjB,SAAS;AAAA,IACT;AAAA,IACA,WAAW,kBAAkB,QAAQ,CAACA,SAAQ,GAAG,WAAS;AACtD,UAAI,OAAO,MAAM,MAAMA,SAAQ;AAC/B,aAAO,QAAQ,KAAK,OAAO,EAAE,iBAAiB,KAAK,KAAK,IAAI,CAAC;AAAA,IACjE,CAAC;AAAA,EACL;AACJ,CAAC;AAQD,IAAM,kBAAN,MAAsB;AAAA;AAAA;AAAA;AAAA,EAIlB,YAIAA,WAOA,UAAU,CAAC,GAAG;AACV,SAAK,WAAWA;AAChB,SAAK,UAAU;AACf,SAAK,YAAY,CAACA,WAAU,OAAO;AAAA,EACvC;AACJ;AAOA,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACtB,YAIAC,OAIA,OAIA,YAKA,UAAU,UAIV,UAAU,QAAW;AACjB,SAAK,OAAOA;AACZ,SAAK,QAAQ;AACb,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,UAAU;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO;AACH,WAAO,KAAK,YAAY,KAAK,UAAU,KAAK,SAAS,EAAE,KAAK,aAAW,KAAK,UAAU,SAAS,SAAO;AAAE,WAAK,UAAU;AAAM,YAAM;AAAA,IAAK,CAAC;AAAA,EAC7I;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,GAAG,MAAM;AACZ,QAAI,EAAE,MAAM,QAAQ,IAAI;AACxB,QAAI,CAAC,MAAM;AACP,UAAI,CAAC;AACD,cAAM,IAAI,WAAW,gEAAgE;AACzF,aAAO,MAAM,QAAQ,QAAQ,OAAO;AAAA,IACxC;AACA,WAAO,IAAI,qBAAoB,KAAK,OAAO,KAAK,SAAS,CAAC,GAAG,OAAO,KAAK,IAAI,EAAE,IAAI,OAAK,EAAE,YAAY,CAAC,GAAG,KAAK,cAAc,CAAC,GAAG,KAAK,UAAU,MAAM,OAAO;AAAA,EACjK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,cAAc,OAAO,UAAU;AAClC,aAAS,KAAK;AACV,UAAI,EAAE,YAAY,EAAE,SAAS,KAAK,QAAQ;AACtC,eAAO;AACf,QAAI,MAAM,aAAa,KAAK,QAAQ;AACpC,QAAI;AACA,eAAS,KAAK;AACV,YAAI,EAAE,WAAW,QAAQ,IAAI,CAAC,CAAC,IAAI;AAC/B,iBAAO;AAAA;AACnB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,kBAAkB,OAAOA,OAAM,QAAQ,MAAM;AAChD,IAAAA,QAAOA,MAAK,YAAY;AACxB,aAAS,KAAK;AACV,UAAI,EAAE,MAAM,KAAK,OAAK,KAAKA,KAAI;AAC3B,eAAO;AACf,QAAI;AACA,eAAS,KAAK;AACV,iBAAS,KAAK,EAAE,OAAO;AACnB,cAAI,QAAQA,MAAK,QAAQ,CAAC;AAC1B,cAAI,QAAQ,OAAO,EAAE,SAAS,KAAK,CAAC,KAAK,KAAKA,MAAK,QAAQ,CAAC,CAAC,KAAK,CAAC,KAAK,KAAKA,MAAK,QAAQ,EAAE,MAAM,CAAC;AAC/F,mBAAO;AAAA,QACf;AACR,WAAO;AAAA,EACX;AACJ;AAWA,IAAM,gBAA6B,MAAM,OAAO;AAMhD,IAAM,aAA0B,MAAM,OAAO;AAAA,EACzC,SAAS,YAAU;AACf,QAAI,CAAC,OAAO;AACR,aAAO;AACX,QAAI,OAAO,OAAO,CAAC;AACnB,QAAI,CAAC,QAAQ,KAAK,KAAK,IAAI,KAAK,MAAM,KAAK,IAAI,EAAE,KAAK,OAAK,KAAK,KAAK,CAAC,CAAC;AACnE,YAAM,IAAI,MAAM,0BAA0B,KAAK,UAAU,OAAO,CAAC,CAAC,CAAC;AACvE,WAAO;AAAA,EACX;AACJ,CAAC;AAOD,SAAS,cAAc,OAAO;AAC1B,MAAI,OAAO,MAAM,MAAM,UAAU;AACjC,SAAO,KAAK,WAAW,CAAC,KAAK,IAAI,MAAM,UAAU,KAAK,SAAS,KAAK;AACxE;AAOA,SAAS,aAAa,OAAO,MAAM;AAC/B,MAAI,SAAS,IAAI,KAAK,MAAM,SAAS,KAAK,MAAM,MAAM,UAAU,EAAE,CAAC;AACnE,MAAI,MAAM,KAAM;AACZ,WAAO,QAAQ,IAAI;AACf,gBAAU;AACV,cAAQ;AAAA,IACZ;AACA,SAAK;AAAA,EACT;AACA,WAAS,IAAI,GAAG,IAAI,MAAM;AACtB,cAAU;AACd,SAAO;AACX;AAUA,SAAS,eAAe,SAAS,KAAK;AAClC,MAAI,mBAAmB;AACnB,cAAU,IAAI,cAAc,OAAO;AACvC,WAAS,WAAW,QAAQ,MAAM,MAAM,aAAa,GAAG;AACpD,QAAI,SAAS,QAAQ,SAAS,GAAG;AACjC,QAAI,WAAW;AACX,aAAO;AAAA,EACf;AACA,MAAI,OAAO,WAAW,QAAQ,KAAK;AACnC,SAAO,KAAK,UAAU,MAAM,kBAAkB,SAAS,MAAM,GAAG,IAAI;AACxE;AAgCA,IAAM,gBAAN,MAAoB;AAAA;AAAA;AAAA;AAAA,EAIhB,YAIA,OAIA,UAAU,CAAC,GAAG;AACV,SAAK,QAAQ;AACb,SAAK,UAAU;AACf,SAAK,OAAO,cAAc,KAAK;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,KAAK,OAAO,GAAG;AAClB,QAAI,OAAO,KAAK,MAAM,IAAI,OAAO,GAAG;AACpC,QAAI,EAAE,eAAe,oBAAoB,IAAI,KAAK;AAClD,QAAI,iBAAiB,QAAQ,iBAAiB,KAAK,QAAQ,iBAAiB,KAAK,IAAI;AACjF,UAAI,uBAAuB,iBAAiB;AACxC,eAAO,EAAE,MAAM,IAAI,MAAM,IAAI;AAAA,eACxB,OAAO,IAAI,gBAAgB,MAAM,iBAAiB;AACvD,eAAO,EAAE,MAAM,KAAK,KAAK,MAAM,gBAAgB,KAAK,IAAI,GAAG,MAAM,cAAc;AAAA;AAE/E,eAAO,EAAE,MAAM,KAAK,KAAK,MAAM,GAAG,gBAAgB,KAAK,IAAI,GAAG,MAAM,KAAK,KAAK;AAAA,IACtF;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,KAAK,OAAO,GAAG;AACxB,QAAI,KAAK,QAAQ,uBAAuB,OAAO,KAAK,QAAQ;AACxD,aAAO;AACX,QAAI,EAAE,MAAM,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI;AAC1C,WAAO,KAAK,MAAM,MAAM,MAAM,KAAK,IAAI,KAAK,QAAQ,MAAM,MAAM,IAAI,CAAC;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,KAAK,OAAO,GAAG;AAClB,QAAI,EAAE,MAAM,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI;AAC1C,QAAI,SAAS,KAAK,YAAY,MAAM,MAAM,IAAI;AAC9C,QAAI,WAAW,KAAK,QAAQ,sBAAsB,KAAK,QAAQ,oBAAoB,IAAI,IAAI;AAC3F,QAAI,WAAW;AACX,gBAAU,WAAW,KAAK,YAAY,MAAM,KAAK,OAAO,MAAM,CAAC;AACnE,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,MAAM,MAAM,KAAK,QAAQ;AACjC,WAAO,YAAY,MAAM,KAAK,MAAM,SAAS,GAAG;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,KAAK,OAAO,GAAG;AACtB,QAAI,EAAE,MAAM,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI;AAC1C,QAAI,WAAW,KAAK,QAAQ;AAC5B,QAAI,UAAU;AACV,UAAI,YAAY,SAAS,IAAI;AAC7B,UAAI,YAAY;AACZ,eAAO;AAAA,IACf;AACA,WAAO,KAAK,YAAY,MAAM,KAAK,OAAO,MAAM,CAAC;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,iBAAiB;AACjB,WAAO,KAAK,QAAQ,iBAAiB;AAAA,EACzC;AACJ;AAQA,IAAM,iBAA8B,IAAI,SAAS;AAEjD,SAAS,kBAAkB,IAAI,KAAK,KAAK;AACrC,MAAI,QAAQ,IAAI,aAAa,GAAG;AAChC,MAAI,QAAQ,IAAI,aAAa,KAAK,EAAE,EAAE,QAAQ,KAAK,CAAC,EAAE,2BAA2B,GAAG;AACpF,MAAI,SAAS,MAAM,MAAM;AACrB,QAAI,MAAM,CAAC;AACX,aAAS,MAAM,OAAO,OAAO,EAAE,IAAI,QAAQ,MAAM,KAAK,QAAQ,IAAI,QAAQ,MAAM,KAAK,OAAO,MAAM,IAAI;AAClG,UAAI,KAAK,GAAG;AAChB,aAAS,IAAI,IAAI,SAAS,GAAG,KAAK,GAAG;AACjC,cAAQ,EAAE,MAAM,IAAI,CAAC,GAAG,MAAM,MAAM;AAAA,EAC5C;AACA,SAAO,UAAU,OAAO,IAAI,GAAG;AACnC;AACA,SAAS,UAAU,OAAO,IAAI,KAAK;AAC/B,WAAS,MAAM,OAAO,KAAK,MAAM,IAAI,MAAM;AACvC,QAAI,WAAW,eAAe,IAAI,IAAI;AACtC,QAAI;AACA,aAAO,SAAS,kBAAkB,OAAO,IAAI,KAAK,GAAG,CAAC;AAAA,EAC9D;AACA,SAAO;AACX;AACA,SAAS,aAAa,IAAI;AACtB,SAAO,GAAG,OAAO,GAAG,QAAQ,iBAAiB,GAAG,QAAQ;AAC5D;AACA,SAAS,eAAe,MAAM;AAC1B,MAAI,WAAW,KAAK,KAAK,KAAK,cAAc;AAC5C,MAAI;AACA,WAAO;AACX,MAAI,QAAQ,KAAK,YAAY;AAC7B,MAAI,UAAU,QAAQ,MAAM,KAAK,KAAK,SAAS,QAAQ,IAAI;AACvD,QAAI,OAAO,KAAK,WAAW,SAAS,QAAQ,MAAM,QAAQ,KAAK,IAAI,IAAI;AACvE,WAAO,QAAM,kBAAkB,IAAI,MAAM,GAAG,QAAW,UAAU,CAAC,aAAa,EAAE,IAAI,KAAK,OAAO,MAAS;AAAA,EAC9G;AACA,SAAO,KAAK,UAAU,OAAO,YAAY;AAC7C;AACA,SAAS,YAAY;AAAE,SAAO;AAAG;AAKjC,IAAM,oBAAN,MAAM,2BAA0B,cAAc;AAAA,EAC1C,YAAY,MAIZ,KAIA,SAAS;AACL,UAAM,KAAK,OAAO,KAAK,OAAO;AAC9B,SAAK,OAAO;AACZ,SAAK,MAAM;AACX,SAAK,UAAU;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,OAAO;AAAE,WAAO,KAAK,QAAQ;AAAA,EAAM;AAAA;AAAA;AAAA;AAAA,EAIvC,OAAO,OAAO,MAAM,KAAK,SAAS;AAC9B,WAAO,IAAI,mBAAkB,MAAM,KAAK,OAAO;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,YAAY;AACZ,WAAO,KAAK,aAAa,KAAK,GAAG;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,aAAa;AACb,WAAO,KAAK,cAAc,KAAK,IAAI;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,MAAM;AAChB,QAAI,OAAO,KAAK,MAAM,IAAI,OAAO,KAAK,IAAI;AAE1C,eAAS;AACL,UAAI,UAAU,KAAK,QAAQ,KAAK,IAAI;AACpC,aAAO,QAAQ,UAAU,QAAQ,OAAO,QAAQ,QAAQ;AACpD,kBAAU,QAAQ;AACtB,UAAI,SAAS,SAAS,IAAI;AACtB;AACJ,aAAO,KAAK,MAAM,IAAI,OAAO,QAAQ,IAAI;AAAA,IAC7C;AACA,WAAO,KAAK,WAAW,KAAK,IAAI;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACP,WAAO,UAAU,KAAK,QAAQ,MAAM,KAAK,MAAM,KAAK,GAAG;AAAA,EAC3D;AACJ;AACA,SAAS,SAAS,QAAQ,IAAI;AAC1B,WAAS,MAAM,IAAI,KAAK,MAAM,IAAI;AAC9B,QAAI,UAAU;AACV,aAAO;AACf,SAAO;AACX;AAIA,SAAS,iBAAiB,SAAS;AAC/B,MAAI,OAAO,QAAQ;AACnB,MAAI,YAAY,KAAK,WAAW,KAAK,IAAI,GAAG,OAAO,KAAK;AACxD,MAAI,CAAC;AACD,WAAO;AACX,MAAI,MAAM,QAAQ,QAAQ;AAC1B,MAAI,WAAW,QAAQ,MAAM,IAAI,OAAO,UAAU,IAAI;AACtD,MAAI,UAAU,OAAO,QAAQ,OAAO,SAAS,OAAO,SAAS,KAAK,KAAK,IAAI,SAAS,IAAI,GAAG;AAC3F,WAAS,MAAM,UAAU,QAAM;AAC3B,QAAI,OAAO,KAAK,WAAW,GAAG;AAC9B,QAAI,CAAC,QAAQ,QAAQ;AACjB,aAAO;AACX,QAAI,CAAC,KAAK,KAAK,WAAW;AACtB,UAAI,KAAK,QAAQ;AACb,eAAO;AACX,UAAI,QAAQ,MAAM,KAAK,SAAS,KAAK,MAAM,UAAU,KAAK,SAAS,IAAI,CAAC,EAAE,CAAC,EAAE;AAC7E,aAAO,EAAE,MAAM,UAAU,MAAM,IAAI,UAAU,KAAK,MAAM;AAAA,IAC5D;AACA,UAAM,KAAK;AAAA,EACf;AACJ;AAYA,SAAS,gBAAgB,EAAE,SAAS,QAAQ,MAAM,QAAQ,EAAE,GAAG;AAC3D,SAAO,CAAC,YAAY,kBAAkB,SAAS,OAAO,OAAO,OAAO;AACxE;AACA,SAAS,kBAAkB,SAAS,OAAO,OAAO,SAAS,UAAU;AACjE,MAAI,QAAQ,QAAQ,WAAW,QAAQ,MAAM,MAAM,MAAM,EAAE,CAAC,EAAE;AAC9D,MAAI,SAAS,WAAW,MAAM,MAAM,OAAO,QAAQ,QAAQ,MAAM,KAAK,WAAW,YAAY,QAAQ,MAAM;AAC3G,MAAI,UAAU,QAAQ,iBAAiB,OAAO,IAAI;AAClD,MAAI;AACA,WAAO,SAAS,QAAQ,OAAO,QAAQ,IAAI,IAAI,QAAQ,OAAO,QAAQ,EAAE;AAC5E,SAAO,QAAQ,cAAc,SAAS,IAAI,QAAQ,OAAO;AAC7D;AAKA,IAAM,aAAa,CAAC,YAAY,QAAQ;AASxC,SAAS,gBAAgB,EAAE,QAAQ,QAAQ,EAAE,IAAI,CAAC,GAAG;AACjD,SAAO,CAAC,YAAY;AAChB,QAAI,cAAc,UAAU,OAAO,KAAK,QAAQ,SAAS;AACzD,WAAO,QAAQ,cAAc,cAAc,IAAI,QAAQ,QAAQ;AAAA,EACnE;AACJ;AACA,IAAM,mBAAmB;AAczB,SAAS,gBAAgB;AACrB,SAAO,YAAY,kBAAkB,GAAG,QAAM;AAC1C,QAAI,CAAC,GAAG,cAAc,CAAC,GAAG,YAAY,YAAY,KAAK,CAAC,GAAG,YAAY,gBAAgB;AACnF,aAAO;AACX,QAAI,QAAQ,GAAG,WAAW,eAAe,iBAAiB,GAAG,WAAW,UAAU,KAAK,IAAI;AAC3F,QAAI,CAAC,MAAM;AACP,aAAO;AACX,QAAI,MAAM,GAAG,QAAQ,EAAE,KAAK,IAAI,GAAG,aAAa,MAAM,OAAO,IAAI,OAAO,IAAI;AAC5E,QAAI,OAAO,KAAK,OAAO;AACnB,aAAO;AACX,QAAI,YAAY,IAAI,YAAY,KAAK,MAAM,IAAI;AAC/C,QAAI,CAAC,MAAM,KAAK,OAAK,EAAE,KAAK,SAAS,CAAC;AAClC,aAAO;AACX,QAAI,EAAE,MAAM,IAAI,IAAI,OAAO,IAAI,UAAU,CAAC;AAC1C,aAAS,EAAE,MAAAC,MAAK,KAAK,MAAM,UAAU,QAAQ;AACzC,UAAIC,QAAO,MAAM,IAAI,OAAOD,KAAI;AAChC,UAAIC,MAAK,QAAQ;AACb;AACJ,aAAOA,MAAK;AACZ,UAAI,SAAS,eAAe,OAAOA,MAAK,IAAI;AAC5C,UAAI,UAAU;AACV;AACJ,UAAI,MAAM,OAAO,KAAKA,MAAK,IAAI,EAAE,CAAC;AAClC,UAAI,OAAO,aAAa,OAAO,MAAM;AACrC,UAAI,OAAO;AACP,gBAAQ,KAAK,EAAE,MAAMA,MAAK,MAAM,IAAIA,MAAK,OAAO,IAAI,QAAQ,QAAQ,KAAK,CAAC;AAAA,IAClF;AACA,WAAO,QAAQ,SAAS,CAAC,IAAI,EAAE,SAAS,YAAY,KAAK,CAAC,IAAI;AAAA,EAClE,CAAC;AACL;AAQA,IAAM,cAA2B,MAAM,OAAO;AAO9C,IAAM,eAA4B,IAAI,SAAS;AAM/C,SAAS,WAAW,MAAM;AACtB,MAAI,QAAQ,KAAK,YAAY,OAAO,KAAK;AACzC,SAAO,SAAS,MAAM,KAAK,KAAK,OAAO,EAAE,MAAM,MAAM,IAAI,IAAI,KAAK,KAAK,UAAU,KAAK,KAAK,KAAK,KAAK,IAAI;AAC7G;AACA,SAAS,cAAc,OAAO,OAAO,KAAK;AACtC,MAAI,OAAO,WAAW,KAAK;AAC3B,MAAI,KAAK,SAAS;AACd,WAAO;AACX,MAAI,QAAQ,KAAK,aAAa,KAAK,CAAC;AACpC,MAAI,QAAQ;AACZ,WAAS,OAAO,OAAO,MAAM,OAAO,KAAK,MAAM;AAC3C,QAAI,MAAM,KAAK;AACf,QAAI,IAAI,MAAM,OAAO,IAAI,OAAO;AAC5B;AACJ,QAAI,SAAS,IAAI,OAAO;AACpB;AACJ,QAAI,OAAO,IAAI,KAAK,KAAK,YAAY;AACrC,QAAI,SAAS,IAAI,KAAK,KAAK,SAAS,MAAM,KAAK,UAAU,MAAM,IAAI,UAAU,CAAC,aAAa,GAAG,IAAI;AAC9F,UAAI,QAAQ,KAAK,KAAK,KAAK;AAC3B,UAAI,SAAS,MAAM,QAAQ,OAAO,MAAM,QAAQ,SAAS,MAAM,KAAK;AAChE,gBAAQ;AAAA,IAChB;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,aAAa,MAAM;AACxB,MAAI,KAAK,KAAK;AACd,SAAO,MAAM,GAAG,MAAM,KAAK,MAAM,GAAG,KAAK;AAC7C;AASA,SAAS,SAAS,OAAO,WAAW,SAAS;AACzC,WAAS,WAAW,MAAM,MAAM,WAAW,GAAG;AAC1C,QAAI,SAAS,QAAQ,OAAO,WAAW,OAAO;AAC9C,QAAI;AACA,aAAO;AAAA,EACf;AACA,SAAO,cAAc,OAAO,WAAW,OAAO;AAClD;AACA,SAAS,SAAS,OAAO,SAAS;AAC9B,MAAI,OAAO,QAAQ,OAAO,MAAM,MAAM,CAAC,GAAG,KAAK,QAAQ,OAAO,MAAM,IAAI,EAAE;AAC1E,SAAO,QAAQ,KAAK,SAAY,EAAE,MAAM,GAAG;AAC/C;AAQA,IAAM,aAA0B,YAAY,OAAO,EAAE,KAAK,SAAS,CAAC;AAIpE,IAAM,eAA4B,YAAY,OAAO,EAAE,KAAK,SAAS,CAAC;AACtE,SAAS,cAAc,MAAM;AACzB,MAAI,QAAQ,CAAC;AACb,WAAS,EAAE,KAAK,KAAK,KAAK,MAAM,UAAU,QAAQ;AAC9C,QAAI,MAAM,KAAK,OAAK,EAAE,QAAQ,QAAQ,EAAE,MAAM,IAAI;AAC9C;AACJ,UAAM,KAAK,KAAK,YAAY,IAAI,CAAC;AAAA,EACrC;AACA,SAAO;AACX;AAQA,IAAM,YAAyB,WAAW,OAAO;AAAA,EAC7C,SAAS;AACL,WAAO,WAAW;AAAA,EACtB;AAAA,EACA,OAAO,QAAQ,IAAI;AACf,aAAS,OAAO,IAAI,GAAG,OAAO;AAC9B,aAAS,KAAK,GAAG,SAAS;AACtB,UAAI,EAAE,GAAG,UAAU,KAAK,CAAC,WAAW,QAAQ,EAAE,MAAM,MAAM,EAAE,MAAM,EAAE,GAAG;AACnE,YAAI,EAAE,mBAAmB,IAAI,GAAG,MAAM,MAAM,UAAU;AACtD,YAAI,SAAS,CAAC,qBAAqB,aAC/B,WAAW,QAAQ,EAAE,QAAQ,IAAI,mBAAmB,mBAAmB,GAAG,OAAO,EAAE,KAAK,CAAC,EAAE,CAAC;AAChG,iBAAS,OAAO,OAAO,EAAE,KAAK,CAAC,OAAO,MAAM,EAAE,MAAM,MAAM,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;AAAA,MAC5E,WACS,EAAE,GAAG,YAAY,GAAG;AACzB,iBAAS,OAAO,OAAO;AAAA,UAAE,QAAQ,CAAC,MAAM,OAAO,EAAE,MAAM,QAAQ,QAAQ,EAAE,MAAM,MAAM;AAAA,UACjF,YAAY,EAAE,MAAM;AAAA,UAAM,UAAU,EAAE,MAAM;AAAA,QAAG,CAAC;AAAA,MACxD;AAAA,IACJ;AAEA,QAAI,GAAG,WAAW;AACd,UAAI,cAAc,OAAO,EAAE,KAAK,IAAI,GAAG,UAAU;AACjD,aAAO,QAAQ,MAAM,MAAM,CAAC,GAAG,MAAM;AAAE,YAAI,IAAI,QAAQ,IAAI;AACvD,wBAAc;AAAA,MAAM,CAAC;AACzB,UAAI;AACA,iBAAS,OAAO,OAAO;AAAA,UACnB,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,QAAQ,CAAC,GAAG,MAAM,KAAK,QAAQ,KAAK;AAAA,QACxC,CAAC;AAAA,IACT;AACA,WAAO;AAAA,EACX;AAAA,EACA,SAAS,OAAK,WAAW,YAAY,KAAK,CAAC;AAAA,EAC3C,OAAO,QAAQ,OAAO;AAClB,QAAI,SAAS,CAAC;AACd,WAAO,QAAQ,GAAG,MAAM,IAAI,QAAQ,CAAC,MAAM,OAAO;AAAE,aAAO,KAAK,MAAM,EAAE;AAAA,IAAG,CAAC;AAC5E,WAAO;AAAA,EACX;AAAA,EACA,SAAS,OAAO;AACZ,QAAI,CAAC,MAAM,QAAQ,KAAK,KAAK,MAAM,SAAS;AACxC,YAAM,IAAI,WAAW,6BAA6B;AACtD,QAAI,SAAS,CAAC;AACd,aAAS,IAAI,GAAG,IAAI,MAAM,UAAS;AAC/B,UAAI,OAAO,MAAM,GAAG,GAAG,KAAK,MAAM,GAAG;AACrC,UAAI,OAAO,QAAQ,YAAY,OAAO,MAAM;AACxC,cAAM,IAAI,WAAW,6BAA6B;AACtD,aAAO,KAAK,WAAW,MAAM,MAAM,EAAE,CAAC;AAAA,IAC1C;AACA,WAAO,WAAW,IAAI,QAAQ,IAAI;AAAA,EACtC;AACJ,CAAC;AAQD,SAAS,SAAS,OAAO,MAAM,IAAI;AAC/B,MAAIC;AACJ,MAAI,QAAQ;AACZ,GAACA,MAAK,MAAM,MAAM,WAAW,KAAK,OAAO,QAAQA,QAAO,SAAS,SAASA,IAAG,QAAQ,MAAM,IAAI,CAACC,OAAMC,QAAO;AACzG,QAAI,CAAC,SAAS,MAAM,OAAOD;AACvB,cAAQ,EAAE,MAAAA,OAAM,IAAAC,IAAG;AAAA,EAC3B,CAAC;AACD,SAAO;AACX;AACA,SAAS,WAAW,QAAQ,MAAM,IAAI;AAClC,MAAI,QAAQ;AACZ,SAAO,QAAQ,MAAM,MAAM,CAAC,GAAG,MAAM;AAAE,QAAI,KAAK,QAAQ,KAAK;AACzD,cAAQ;AAAA,EAAM,CAAC;AACnB,SAAO;AACX;AACA,SAAS,YAAY,OAAO,OAAO;AAC/B,SAAO,MAAM,MAAM,WAAW,KAAK,IAAI,QAAQ,MAAM,OAAO,YAAY,aAAa,GAAG,YAAY,CAAC,CAAC;AAC1G;AAIA,IAAM,WAAW,UAAQ;AACrB,WAAS,QAAQ,cAAc,IAAI,GAAG;AAClC,QAAI,QAAQ,SAAS,KAAK,OAAO,KAAK,MAAM,KAAK,EAAE;AACnD,QAAI,OAAO;AACP,WAAK,SAAS,EAAE,SAAS,YAAY,KAAK,OAAO,CAAC,WAAW,GAAG,KAAK,GAAG,aAAa,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC;AACrG,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AAIA,IAAM,aAAa,UAAQ;AACvB,MAAI,CAAC,KAAK,MAAM,MAAM,WAAW,KAAK;AAClC,WAAO;AACX,MAAI,UAAU,CAAC;AACf,WAAS,QAAQ,cAAc,IAAI,GAAG;AAClC,QAAI,SAAS,SAAS,KAAK,OAAO,KAAK,MAAM,KAAK,EAAE;AACpD,QAAI;AACA,cAAQ,KAAK,aAAa,GAAG,MAAM,GAAG,aAAa,MAAM,QAAQ,KAAK,CAAC;AAAA,EAC/E;AACA,MAAI,QAAQ;AACR,SAAK,SAAS,EAAE,QAAQ,CAAC;AAC7B,SAAO,QAAQ,SAAS;AAC5B;AACA,SAAS,aAAa,MAAM,OAAO,OAAO,MAAM;AAC5C,MAAI,WAAW,KAAK,MAAM,IAAI,OAAO,MAAM,IAAI,EAAE,QAAQ,SAAS,KAAK,MAAM,IAAI,OAAO,MAAM,EAAE,EAAE;AAClG,SAAO,WAAW,SAAS,GAAG,GAAG,KAAK,MAAM,OAAO,OAAO,iBAAiB,gBAAgB,CAAC,IAAI,QAAQ,IAAI,KAAK,MAAM,OAAO,IAAI,CAAC,IAAI,MAAM,GAAG;AACpJ;AAUA,IAAM,UAAU,UAAQ;AACpB,MAAI,EAAE,MAAM,IAAI,MAAM,UAAU,CAAC;AACjC,WAAS,MAAM,GAAG,MAAM,MAAM,IAAI,UAAS;AACvC,QAAI,OAAO,KAAK,YAAY,GAAG,GAAG,QAAQ,SAAS,OAAO,KAAK,MAAM,KAAK,EAAE;AAC5E,QAAI;AACA,cAAQ,KAAK,WAAW,GAAG,KAAK,CAAC;AACrC,WAAO,QAAQ,KAAK,YAAY,MAAM,EAAE,IAAI,MAAM,KAAK;AAAA,EAC3D;AACA,MAAI,QAAQ;AACR,SAAK,SAAS,EAAE,SAAS,YAAY,KAAK,OAAO,OAAO,EAAE,CAAC;AAC/D,SAAO,CAAC,CAAC,QAAQ;AACrB;AAIA,IAAM,YAAY,UAAQ;AACtB,MAAI,QAAQ,KAAK,MAAM,MAAM,WAAW,KAAK;AAC7C,MAAI,CAAC,SAAS,CAAC,MAAM;AACjB,WAAO;AACX,MAAI,UAAU,CAAC;AACf,QAAM,QAAQ,GAAG,KAAK,MAAM,IAAI,QAAQ,CAAC,MAAM,OAAO;AAAE,YAAQ,KAAK,aAAa,GAAG,EAAE,MAAM,GAAG,CAAC,CAAC;AAAA,EAAG,CAAC;AACtG,OAAK,SAAS,EAAE,QAAQ,CAAC;AACzB,SAAO;AACX;AA4CA,IAAM,aAAa;AAAA,EACf,EAAE,KAAK,gBAAgB,KAAK,aAAa,KAAK,SAAS;AAAA,EACvD,EAAE,KAAK,gBAAgB,KAAK,aAAa,KAAK,WAAW;AAAA,EACzD,EAAE,KAAK,cAAc,KAAK,QAAQ;AAAA,EAClC,EAAE,KAAK,cAAc,KAAK,UAAU;AACxC;AACA,IAAM,gBAAgB;AAAA,EAClB,gBAAgB;AAAA,EAChB,oBAAoB;AAAA,EACpB,iBAAiB;AACrB;AACA,IAAM,aAA0B,MAAM,OAAO;AAAA,EACzC,QAAQ,QAAQ;AAAE,WAAO,cAAc,QAAQ,aAAa;AAAA,EAAG;AACnE,CAAC;AAID,SAAS,YAAY,QAAQ;AACzB,MAAI,SAAS,CAAC,WAAW,WAAW;AACpC,MAAI;AACA,WAAO,KAAK,WAAW,GAAG,MAAM,CAAC;AACrC,SAAO;AACX;AACA,SAAS,YAAY,MAAM,UAAU;AACjC,MAAI,EAAE,MAAM,IAAI,MAAM,OAAO,MAAM,MAAM,UAAU;AACnD,MAAI,UAAU,CAAC,UAAU;AACrB,QAAI,OAAO,KAAK,YAAY,KAAK,SAAS,MAAM,MAAM,CAAC;AACvD,QAAI,SAAS,SAAS,KAAK,OAAO,KAAK,MAAM,KAAK,EAAE;AACpD,QAAI;AACA,WAAK,SAAS,EAAE,SAAS,aAAa,GAAG,MAAM,EAAE,CAAC;AACtD,UAAM,eAAe;AAAA,EACzB;AACA,MAAI,KAAK;AACL,WAAO,KAAK,eAAe,MAAM,SAAS,QAAQ;AACtD,MAAI,UAAU,SAAS,cAAc,MAAM;AAC3C,UAAQ,cAAc,KAAK;AAC3B,UAAQ,aAAa,cAAc,MAAM,OAAO,aAAa,CAAC;AAC9D,UAAQ,QAAQ,MAAM,OAAO,QAAQ;AACrC,UAAQ,YAAY;AACpB,UAAQ,UAAU;AAClB,SAAO;AACX;AACA,IAAM,aAA0B,WAAW,QAAQ,EAAE,QAAqB,IAAI,cAAc,WAAW;AAAA,EAC/F,MAAM,MAAM;AAAE,WAAO,YAAY,MAAM,IAAI;AAAA,EAAG;AAClD,IAAE,CAAC;AACP,IAAM,qBAAN,cAAiC,WAAW;AAAA,EACxC,YAAY,OAAO;AACf,UAAM;AACN,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,GAAG,OAAO;AAAE,WAAO,KAAK,SAAS,MAAM;AAAA,EAAO;AAAA,EAC9C,MAAM,MAAM;AAAE,WAAO,YAAY,MAAM,KAAK,KAAK;AAAA,EAAG;AACxD;AACA,IAAM,qBAAqB;AAAA,EACvB,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,kBAAkB,CAAC;AAAA,EACnB,gBAAgB,MAAM;AAC1B;AACA,IAAM,aAAN,cAAyB,aAAa;AAAA,EAClC,YAAY,QAAQ,MAAM;AACtB,UAAM;AACN,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,GAAG,OAAO;AAAE,WAAO,KAAK,UAAU,MAAM,UAAU,KAAK,QAAQ,MAAM;AAAA,EAAM;AAAA,EAC3E,MAAM,MAAM;AACR,QAAI,KAAK,OAAO;AACZ,aAAO,KAAK,OAAO,UAAU,KAAK,IAAI;AAC1C,QAAI,OAAO,SAAS,cAAc,MAAM;AACxC,SAAK,cAAc,KAAK,OAAO,KAAK,OAAO,WAAW,KAAK,OAAO;AAClE,SAAK,QAAQ,KAAK,MAAM,OAAO,KAAK,OAAO,cAAc,aAAa;AACtE,WAAO;AAAA,EACX;AACJ;AAMA,SAAS,WAAW,SAAS,CAAC,GAAG;AAC7B,MAAI,aAAa,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,kBAAkB,GAAG,MAAM;AAC5E,MAAI,UAAU,IAAI,WAAW,YAAY,IAAI,GAAG,YAAY,IAAI,WAAW,YAAY,KAAK;AAC5F,MAAI,UAAU,WAAW,UAAU,MAAM;AAAA,IACrC,YAAY,MAAM;AACd,WAAK,OAAO,KAAK,SAAS;AAC1B,WAAK,UAAU,KAAK,aAAa,IAAI;AAAA,IACzC;AAAA,IACA,OAAO,QAAQ;AACX,UAAI,OAAO,cAAc,OAAO,mBAC5B,OAAO,WAAW,MAAM,QAAQ,KAAK,OAAO,MAAM,MAAM,QAAQ,KAChE,OAAO,WAAW,MAAM,WAAW,KAAK,KAAK,OAAO,MAAM,MAAM,WAAW,KAAK,KAChF,WAAW,OAAO,UAAU,KAAK,WAAW,OAAO,KAAK,KACxD,WAAW,eAAe,MAAM;AAChC,aAAK,UAAU,KAAK,aAAa,OAAO,IAAI;AAAA,IACpD;AAAA,IACA,aAAa,MAAM;AACf,UAAI,UAAU,IAAI,gBAAgB;AAClC,eAAS,QAAQ,KAAK,oBAAoB;AACtC,YAAI,OAAO,SAAS,KAAK,OAAO,KAAK,MAAM,KAAK,EAAE,IAAI,YAChD,SAAS,KAAK,OAAO,KAAK,MAAM,KAAK,EAAE,IAAI,UAAU;AAC3D,YAAI;AACA,kBAAQ,IAAI,KAAK,MAAM,KAAK,MAAM,IAAI;AAAA,MAC9C;AACA,aAAO,QAAQ,OAAO;AAAA,IAC1B;AAAA,EACJ,CAAC;AACD,MAAI,EAAE,iBAAiB,IAAI;AAC3B,SAAO;AAAA,IACH;AAAA,IACA,OAAO;AAAA,MACH,OAAO;AAAA,MACP,QAAQ,MAAM;AAAE,YAAIC;AAAI,iBAASA,MAAK,KAAK,OAAO,OAAO,OAAO,QAAQA,QAAO,SAAS,SAASA,IAAG,YAAY,SAAS;AAAA,MAAO;AAAA,MAChI,gBAAgB;AACZ,eAAO,IAAI,WAAW,YAAY,KAAK;AAAA,MAC3C;AAAA,MACA,kBAAkB,OAAO,OAAO,OAAO,OAAO,CAAC,GAAG,gBAAgB,GAAG,EAAE,OAAO,CAAC,MAAM,MAAM,UAAU;AAC7F,YAAI,iBAAiB,SAAS,iBAAiB,MAAM,MAAM,MAAM,KAAK;AAClE,iBAAO;AACX,YAAI,SAAS,SAAS,KAAK,OAAO,KAAK,MAAM,KAAK,EAAE;AACpD,YAAI,QAAQ;AACR,eAAK,SAAS,EAAE,SAAS,aAAa,GAAG,MAAM,EAAE,CAAC;AAClD,iBAAO;AAAA,QACX;AACA,YAAI,QAAQ,SAAS,KAAK,OAAO,KAAK,MAAM,KAAK,EAAE;AACnD,YAAI,OAAO;AACP,eAAK,SAAS,EAAE,SAAS,WAAW,GAAG,KAAK,EAAE,CAAC;AAC/C,iBAAO;AAAA,QACX;AACA,eAAO;AAAA,MACX,EAAE,CAAC;AAAA,IACX,CAAC;AAAA,IACD,YAAY;AAAA,EAChB;AACJ;AACA,IAAM,cAA2B,WAAW,UAAU;AAAA,EAClD,uBAAuB;AAAA,IACnB,iBAAiB;AAAA,IACjB,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,cAAc;AAAA,IACd,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,QAAQ;AAAA,EACZ;AAAA,EACA,uBAAuB;AAAA,IACnB,SAAS;AAAA,IACT,QAAQ;AAAA,EACZ;AACJ,CAAC;AAMD,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACjB,YAIA,OAAO,SAAS;AACZ,SAAK,QAAQ;AACb,QAAI;AACJ,aAAS,IAAI,MAAM;AACf,UAAI,MAAM,YAAY,QAAQ;AAC9B,OAAC,YAAY,UAAU,uBAAO,OAAO,IAAI,IAAI,MAAM,GAAG,IAAI;AAC1D,aAAO;AAAA,IACX;AACA,UAAM,MAAM,OAAO,QAAQ,OAAO,WAAW,QAAQ,MAAM,QAAQ,MAAM,IAAI,QAAQ,GAAG,IAAI;AAC5F,UAAM,WAAW,QAAQ;AACzB,SAAK,QAAQ,oBAAoB,WAAW,CAAC,SAAS,KAAK,KAAK,gBAAgB,KAAK,SAAS,OACxF,WAAW,CAAC,SAAS,QAAQ,WAAW;AAC9C,SAAK,QAAQ,eAAe,MAAM,IAAI,YAAU;AAAA,MAC5C,KAAK,MAAM;AAAA,MACX,OAAO,MAAM,SAAS,IAAI,OAAO,OAAO,CAAC,GAAG,OAAO,EAAE,KAAK,KAAK,CAAC,CAAC;AAAA,IACrE,EAAE,GAAG;AAAA,MACD;AAAA,IACJ,CAAC,EAAE;AACH,SAAK,SAAS,UAAU,IAAI,YAAY,OAAO,IAAI;AACnD,SAAK,YAAY,QAAQ;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,OAAO,OAAO,OAAO,SAAS;AAC1B,WAAO,IAAI,gBAAe,OAAO,WAAW,CAAC,CAAC;AAAA,EAClD;AACJ;AACA,IAAM,mBAAgC,MAAM,OAAO;AACnD,IAAM,sBAAmC,MAAM,OAAO;AAAA,EAClD,QAAQ,QAAQ;AAAE,WAAO,OAAO,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI;AAAA,EAAM;AACjE,CAAC;AACD,SAAS,gBAAgB,OAAO;AAC5B,MAAI,OAAO,MAAM,MAAM,gBAAgB;AACvC,SAAO,KAAK,SAAS,OAAO,MAAM,MAAM,mBAAmB;AAC/D;AAQA,SAAS,mBAAmB,aAAa,SAAS;AAC9C,MAAI,MAAM,CAAC,eAAe,GAAG;AAC7B,MAAI,uBAAuB,gBAAgB;AACvC,QAAI,YAAY;AACZ,UAAI,KAAK,WAAW,YAAY,GAAG,YAAY,MAAM,CAAC;AAC1D,gBAAY,YAAY;AAAA,EAC5B;AACA,MAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ;AAC1D,QAAI,KAAK,oBAAoB,GAAG,WAAW,CAAC;AAAA,WACvC;AACL,QAAI,KAAK,iBAAiB,SAAS,CAAC,WAAW,SAAS,GAAG,WAAS;AAChE,aAAO,MAAM,MAAM,WAAW,SAAS,MAAM,aAAa,UAAU,CAAC,WAAW,IAAI,CAAC;AAAA,IACzF,CAAC,CAAC;AAAA;AAEF,QAAI,KAAK,iBAAiB,GAAG,WAAW,CAAC;AAC7C,SAAO;AACX;AAqBA,IAAM,kBAAN,MAAsB;AAAA,EAClB,YAAY,MAAM;AACd,SAAK,YAAY,uBAAO,OAAO,IAAI;AACnC,SAAK,OAAO,WAAW,KAAK,KAAK;AACjC,SAAK,cAAc,KAAK,UAAU,MAAM,gBAAgB,KAAK,KAAK,CAAC;AACnE,SAAK,cAAc,KAAK,SAAS;AAAA,EACrC;AAAA,EACA,OAAO,QAAQ;AACX,QAAI,OAAO,WAAW,OAAO,KAAK,GAAG,eAAe,gBAAgB,OAAO,KAAK;AAChF,QAAI,cAAc,gBAAgB,gBAAgB,OAAO,UAAU;AACnE,QAAI,EAAE,SAAS,IAAI,OAAO,MAAM,oBAAoB,OAAO,QAAQ,OAAO,KAAK,aAAa,CAAC;AAC7F,QAAI,KAAK,SAAS,SAAS,MAAM,CAAC,eAAe,KAAK,QAAQ,KAAK,KAAK,QAAQ,qBAAqB,SAAS,IAAI;AAC9G,WAAK,cAAc,KAAK,YAAY,IAAI,OAAO,OAAO;AACtD,WAAK,cAAc;AAAA,IACvB,WACS,QAAQ,KAAK,QAAQ,OAAO,mBAAmB,aAAa;AACjE,WAAK,OAAO;AACZ,WAAK,cAAc,KAAK,UAAU,OAAO,MAAM,YAAY;AAC3D,WAAK,cAAc,SAAS;AAAA,IAChC;AAAA,EACJ;AAAA,EACA,UAAU,MAAM,cAAc;AAC1B,QAAI,CAAC,gBAAgB,CAAC,KAAK,KAAK;AAC5B,aAAO,WAAW;AACtB,QAAI,UAAU,IAAI,gBAAgB;AAClC,aAAS,EAAE,MAAM,GAAG,KAAK,KAAK,eAAe;AACzC,oBAAc,KAAK,MAAM,cAAc,CAACC,OAAMC,KAAI,UAAU;AACxD,gBAAQ,IAAID,OAAMC,KAAI,KAAK,UAAU,KAAK,MAAM,KAAK,UAAU,KAAK,IAAI,WAAW,KAAK,EAAE,OAAO,MAAM,CAAC,EAAE;AAAA,MAC9G,GAAG,MAAM,EAAE;AAAA,IACf;AACA,WAAO,QAAQ,OAAO;AAAA,EAC1B;AACJ;AACA,IAAM,kBAA+B,KAAK,KAAkB,WAAW,UAAU,iBAAiB;AAAA,EAC9F,aAAa,OAAK,EAAE;AACxB,CAAC,CAAC;AAIF,IAAM,wBAAqC,eAAe,OAAO;AAAA,EAC7D;AAAA,IAAE,KAAK,KAAK;AAAA,IACR,OAAO;AAAA,EAAU;AAAA,EACrB;AAAA,IAAE,KAAK,KAAK;AAAA,IACR,gBAAgB;AAAA,EAAY;AAAA,EAChC;AAAA,IAAE,KAAK,KAAK;AAAA,IACR,gBAAgB;AAAA,IAChB,YAAY;AAAA,EAAO;AAAA,EACvB;AAAA,IAAE,KAAK,KAAK;AAAA,IACR,WAAW;AAAA,EAAS;AAAA,EACxB;AAAA,IAAE,KAAK,KAAK;AAAA,IACR,YAAY;AAAA,EAAO;AAAA,EACvB;AAAA,IAAE,KAAK,KAAK;AAAA,IACR,gBAAgB;AAAA,EAAe;AAAA,EACnC;AAAA,IAAE,KAAK,KAAK;AAAA,IACR,OAAO;AAAA,EAAO;AAAA,EAClB;AAAA,IAAE,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,KAAK,KAAK,KAAK,kBAAkB,KAAK,SAAS;AAAA,IACzE,OAAO;AAAA,EAAO;AAAA,EAClB;AAAA,IAAE,KAAK,CAAC,KAAK,SAAS,KAAK,QAAQ;AAAA,IAC/B,OAAO;AAAA,EAAO;AAAA,EAClB;AAAA,IAAE,KAAK,CAAC,KAAK,QAAQ,KAAK,OAAO;AAAA,IAC7B,OAAO;AAAA,EAAO;AAAA,EAClB;AAAA,IAAE,KAAK,CAAC,KAAK,QAAQ,KAAK,QAAqB,KAAK,QAAQ,KAAK,MAAM,CAAC;AAAA,IACpE,OAAO;AAAA,EAAO;AAAA,EAClB;AAAA,IAAE,KAAkB,KAAK,WAAW,KAAK,YAAY;AAAA,IACjD,OAAO;AAAA,EAAO;AAAA,EAClB;AAAA,IAAE,KAAkB,KAAK,MAAM,KAAK,YAAY;AAAA,IAC5C,OAAO;AAAA,EAAO;AAAA,EAClB;AAAA,IAAE,KAAK,CAAC,KAAK,UAAU,KAAK,SAAS;AAAA,IACjC,OAAO;AAAA,EAAO;AAAA,EAClB;AAAA,IAAE,KAAK,KAAK;AAAA,IACR,OAAO;AAAA,EAAO;AAAA,EAClB;AAAA,IAAE,KAAK,CAAc,KAAK,QAAQ,KAAK,YAAY,GAAG,KAAK,SAAS;AAAA,IAChE,OAAO;AAAA,EAAO;AAAA,EAClB;AAAA,IAAE,KAAkB,KAAK,WAAW,KAAK,YAAY;AAAA,IACjD,OAAO;AAAA,EAAO;AAAA,EAClB;AAAA,IAAE,KAAK,KAAK;AAAA,IACR,OAAO;AAAA,EAAO;AAAA,EAClB;AAAA,IAAE,KAAK,KAAK;AAAA,IACR,OAAO;AAAA,EAAO;AACtB,CAAC;AAED,IAAM,YAAyB,WAAW,UAAU;AAAA,EAChD,oCAAoC,EAAE,iBAAiB,YAAY;AAAA,EACnE,uCAAuC,EAAE,iBAAiB,YAAY;AAC1E,CAAC;AACD,IAAM,kBAAkB;AAAxB,IAA+B,kBAAkB;AACjD,IAAM,wBAAqC,MAAM,OAAO;AAAA,EACpD,QAAQ,SAAS;AACb,WAAO,cAAc,SAAS;AAAA,MAC1B,aAAa;AAAA,MACb,UAAU;AAAA,MACV,iBAAiB;AAAA,MACjB,aAAa;AAAA,IACjB,CAAC;AAAA,EACL;AACJ,CAAC;AACD,IAAM,eAA4B,WAAW,KAAK,EAAE,OAAO,qBAAqB,CAAC;AAAjF,IAAoF,kBAA+B,WAAW,KAAK,EAAE,OAAO,wBAAwB,CAAC;AACrK,SAAS,mBAAmB,OAAO;AAC/B,MAAI,cAAc,CAAC;AACnB,MAAI,OAAO,MAAM,UAAU,eAAe;AAC1C,cAAY,KAAK,KAAK,MAAM,MAAM,MAAM,MAAM,MAAM,MAAM,EAAE,CAAC;AAC7D,MAAI,MAAM;AACN,gBAAY,KAAK,KAAK,MAAM,MAAM,IAAI,MAAM,MAAM,IAAI,EAAE,CAAC;AAC7D,SAAO;AACX;AACA,IAAM,uBAAoC,WAAW,OAAO;AAAA,EACxD,SAAS;AAAE,WAAO,WAAW;AAAA,EAAM;AAAA,EACnC,OAAO,MAAM,IAAI;AACb,QAAI,CAAC,GAAG,cAAc,CAAC,GAAG;AACtB,aAAO;AACX,QAAI,cAAc,CAAC;AACnB,QAAI,SAAS,GAAG,MAAM,MAAM,qBAAqB;AACjD,aAAS,SAAS,GAAG,MAAM,UAAU,QAAQ;AACzC,UAAI,CAAC,MAAM;AACP;AACJ,UAAI,QAAQ,cAAc,GAAG,OAAO,MAAM,MAAM,IAAI,MAAM,KAClD,MAAM,OAAO,KAAK,cAAc,GAAG,OAAO,MAAM,OAAO,GAAG,GAAG,MAAM,KACnE,OAAO,gBACN,cAAc,GAAG,OAAO,MAAM,MAAM,GAAG,MAAM,KACzC,MAAM,OAAO,GAAG,MAAM,IAAI,UAAU,cAAc,GAAG,OAAO,MAAM,OAAO,GAAG,IAAI,MAAM;AACnG,UAAI;AACA,sBAAc,YAAY,OAAO,OAAO,YAAY,OAAO,GAAG,KAAK,CAAC;AAAA,IAC5E;AACA,WAAO,WAAW,IAAI,aAAa,IAAI;AAAA,EAC3C;AAAA,EACA,SAAS,OAAK,WAAW,YAAY,KAAK,CAAC;AAC/C,CAAC;AACD,IAAM,wBAAwB;AAAA,EAC1B;AAAA,EACA;AACJ;AAOA,SAAS,gBAAgB,SAAS,CAAC,GAAG;AAClC,SAAO,CAAC,sBAAsB,GAAG,MAAM,GAAG,qBAAqB;AACnE;AASA,IAAM,wBAAqC,IAAI,SAAS;AACxD,SAAS,cAAc,MAAM,KAAK,UAAU;AACxC,MAAI,SAAS,KAAK,KAAK,MAAM,IAAI,SAAS,WAAW,SAAS,QAAQ;AACtE,MAAI;AACA,WAAO;AACX,MAAI,KAAK,KAAK,UAAU,GAAG;AACvB,QAAI,QAAQ,SAAS,QAAQ,KAAK,IAAI;AACtC,QAAI,QAAQ,MAAM,QAAQ,MAAM,MAAM,IAAI,IAAI;AAC1C,aAAO,CAAC,SAAS,QAAQ,GAAG,CAAC;AAAA,EACrC;AACA,SAAO;AACX;AACA,SAAS,WAAW,MAAM;AACtB,MAAI,YAAY,KAAK,KAAK,KAAK,qBAAqB;AACpD,SAAO,YAAY,UAAU,KAAK,IAAI,IAAI;AAC9C;AAOA,SAAS,cAAc,OAAO,KAAK,KAAK,SAAS,CAAC,GAAG;AACjD,MAAI,kBAAkB,OAAO,mBAAmB,iBAAiB,WAAW,OAAO,YAAY;AAC/F,MAAI,OAAO,WAAW,KAAK,GAAG,OAAO,KAAK,aAAa,KAAK,GAAG;AAC/D,WAAS,MAAM,MAAM,KAAK,MAAM,IAAI,QAAQ;AACxC,QAAI,UAAU,cAAc,IAAI,MAAM,KAAK,QAAQ;AACnD,QAAI,WAAW,IAAI,OAAO,IAAI,IAAI;AAC9B,UAAI,SAAS,WAAW,GAAG;AAC3B,UAAI,WAAW,MAAM,IAAI,OAAO,OAAO,QAAQ,MAAM,OAAO,KAAK,MAAM,OAAO,QAAQ,OAAO,OAAO;AAChG,eAAO,oBAAoB,OAAO,KAAK,KAAK,KAAK,QAAQ,SAAS,QAAQ;AAAA,IAClF;AAAA,EACJ;AACA,SAAO,mBAAmB,OAAO,KAAK,KAAK,MAAM,KAAK,MAAM,iBAAiB,QAAQ;AACzF;AACA,SAAS,oBAAoB,QAAQ,MAAM,KAAK,OAAO,QAAQ,UAAU,UAAU;AAC/E,MAAI,SAAS,MAAM,QAAQ,aAAa,EAAE,MAAM,OAAO,MAAM,IAAI,OAAO,GAAG;AAC3E,MAAI,QAAQ,GAAG,SAAS,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,OAAO;AACtF,MAAI,WAAW,MAAM,IAAI,OAAO,YAAY,MAAM,IAAI,IAAI,OAAO,WAAW,MAAM,EAAE;AAChF,OAAG;AACC,UAAI,MAAM,IAAI,OAAO,MAAM,MAAM,OAAO,OAAO,QAAQ,MAAM,IAAI;AAC7D,YAAI,SAAS,KAAK,SAAS,QAAQ,OAAO,KAAK,IAAI,IAAI,MAAM,OAAO,OAAO,OAAO,IAAI;AAClF,cAAI,YAAY,WAAW,MAAM;AACjC,iBAAO,EAAE,OAAO,YAAY,KAAK,YAAY,EAAE,MAAM,UAAU,MAAM,IAAI,UAAU,GAAG,IAAI,QAAW,SAAS,KAAK;AAAA,QACvH,WACS,cAAc,OAAO,MAAM,KAAK,QAAQ,GAAG;AAChD;AAAA,QACJ,WACS,cAAc,OAAO,MAAM,CAAC,KAAK,QAAQ,GAAG;AACjD,cAAI,SAAS,GAAG;AACZ,gBAAI,YAAY,WAAW,MAAM;AACjC,mBAAO;AAAA,cACH,OAAO;AAAA,cACP,KAAK,aAAa,UAAU,OAAO,UAAU,KAAK,EAAE,MAAM,UAAU,MAAM,IAAI,UAAU,GAAG,IAAI;AAAA,cAC/F,SAAS;AAAA,YACb;AAAA,UACJ;AACA;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,SAAS,MAAM,IAAI,OAAO,YAAY,IAAI,OAAO,YAAY;AACjE,SAAO,EAAE,OAAO,YAAY,SAAS,MAAM;AAC/C;AACA,SAAS,mBAAmB,OAAO,KAAK,KAAK,MAAM,WAAW,iBAAiB,UAAU;AACrF,MAAI,UAAU,MAAM,IAAI,MAAM,SAAS,MAAM,GAAG,GAAG,IAAI,MAAM,SAAS,KAAK,MAAM,CAAC;AAClF,MAAIC,WAAU,SAAS,QAAQ,OAAO;AACtC,MAAIA,WAAU,KAAMA,WAAU,KAAK,KAAO,MAAM;AAC5C,WAAO;AACX,MAAI,aAAa,EAAE,MAAM,MAAM,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,IAAI,MAAM,IAAI,IAAI;AAC9E,MAAI,OAAO,MAAM,IAAI,UAAU,KAAK,MAAM,IAAI,MAAM,IAAI,SAAS,CAAC,GAAG,QAAQ;AAC7E,WAAS,WAAW,GAAG,CAAE,KAAK,KAAK,EAAG,QAAQ,YAAY,mBAAkB;AACxE,QAAI,OAAO,KAAK;AAChB,QAAI,MAAM;AACN,kBAAY,KAAK;AACrB,QAAI,UAAU,MAAM,WAAW;AAC/B,aAASC,OAAM,MAAM,IAAI,IAAI,KAAK,SAAS,GAAG,MAAM,MAAM,IAAI,KAAK,SAAS,IAAIA,QAAO,KAAKA,QAAO,KAAK;AACpG,UAAI,QAAQ,SAAS,QAAQ,KAAKA,IAAG,CAAC;AACtC,UAAI,QAAQ,KAAK,KAAK,aAAa,UAAUA,MAAK,CAAC,EAAE,QAAQ;AACzD;AACJ,UAAK,QAAQ,KAAK,KAAO,MAAM,GAAI;AAC/B;AAAA,MACJ,WACS,SAAS,GAAG;AACjB,eAAO,EAAE,OAAO,YAAY,KAAK,EAAE,MAAM,UAAUA,MAAK,IAAI,UAAUA,OAAM,EAAE,GAAG,SAAU,SAAS,KAAOD,YAAW,EAAG;AAAA,MAC7H,OACK;AACD;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,MAAM;AACN,kBAAY,KAAK;AAAA,EACzB;AACA,SAAO,KAAK,OAAO,EAAE,OAAO,YAAY,SAAS,MAAM,IAAI;AAC/D;AAIA,SAAS,SAASE,SAAQ,KAAK,SAAS,aAAa,GAAG,aAAa,GAAG;AACpE,MAAI,OAAO,MAAM;AACb,UAAMA,QAAO,OAAO,aAAa;AACjC,QAAI,OAAO;AACP,YAAMA,QAAO;AAAA,EACrB;AACA,MAAI,IAAI;AACR,WAAS,IAAI,YAAY,IAAI,KAAK,KAAK;AACnC,QAAIA,QAAO,WAAW,CAAC,KAAK;AACxB,WAAK,UAAW,IAAI;AAAA;AAEpB;AAAA,EACR;AACA,SAAO;AACX;AAKA,IAAM,eAAN,MAAmB;AAAA;AAAA;AAAA;AAAA,EAIf,YAIAA,SAAQ,SAIRC,aAAY,gBAAgB;AACxB,SAAK,SAASD;AACd,SAAK,UAAU;AACf,SAAK,aAAaC;AAClB,SAAK,iBAAiB;AAItB,SAAK,MAAM;AAIX,SAAK,QAAQ;AACb,SAAK,gBAAgB;AACrB,SAAK,kBAAkB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM;AAAE,WAAO,KAAK,OAAO,KAAK,OAAO;AAAA,EAAQ;AAAA;AAAA;AAAA;AAAA,EAI/C,MAAM;AAAE,WAAO,KAAK,OAAO;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9B,OAAO;AAAE,WAAO,KAAK,OAAO,OAAO,KAAK,GAAG,KAAK;AAAA,EAAW;AAAA;AAAA;AAAA;AAAA,EAI3D,OAAO;AACH,QAAI,KAAK,MAAM,KAAK,OAAO;AACvB,aAAO,KAAK,OAAO,OAAO,KAAK,KAAK;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,OAAO;AACP,QAAI,KAAK,KAAK,OAAO,OAAO,KAAK,GAAG;AACpC,QAAI;AACJ,QAAI,OAAO,SAAS;AAChB,WAAK,MAAM;AAAA;AAEX,WAAK,OAAO,iBAAiB,SAAS,MAAM,KAAK,EAAE,IAAI,MAAM,EAAE;AACnE,QAAI,IAAI;AACJ,QAAE,KAAK;AACP,aAAO;AAAA,IACX;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,OAAO;AACZ,QAAI,QAAQ,KAAK;AACjB,WAAO,KAAK,IAAI,KAAK,GAAG;AAAA,IAAE;AAC1B,WAAO,KAAK,MAAM;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACP,QAAI,QAAQ,KAAK;AACjB,WAAO,aAAa,KAAK,KAAK,OAAO,OAAO,KAAK,GAAG,CAAC;AACjD,QAAE,KAAK;AACX,WAAO,KAAK,MAAM;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY;AAAE,SAAK,MAAM,KAAK,OAAO;AAAA,EAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAK7C,OAAO,IAAI;AACP,QAAI,QAAQ,KAAK,OAAO,QAAQ,IAAI,KAAK,GAAG;AAC5C,QAAI,QAAQ,IAAI;AACZ,WAAK,MAAM;AACX,aAAO;AAAA,IACX;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,GAAG;AAAE,SAAK,OAAO;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAI3B,SAAS;AACL,QAAI,KAAK,gBAAgB,KAAK,OAAO;AACjC,WAAK,kBAAkB,SAAS,KAAK,QAAQ,KAAK,OAAO,KAAK,SAAS,KAAK,eAAe,KAAK,eAAe;AAC/G,WAAK,gBAAgB,KAAK;AAAA,IAC9B;AACA,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AACV,QAAIC;AACJ,YAAQA,MAAK,KAAK,oBAAoB,QAAQA,QAAO,SAASA,MAAK,SAAS,KAAK,QAAQ,MAAM,KAAK,OAAO;AAAA,EAC/G;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,SAAS,SAAS,iBAAiB;AACrC,QAAI,OAAO,WAAW,UAAU;AAC5B,UAAI,QAAQ,CAAC,QAAQ,kBAAkB,IAAI,YAAY,IAAI;AAC3D,UAAI,SAAS,KAAK,OAAO,OAAO,KAAK,KAAK,QAAQ,MAAM;AACxD,UAAI,MAAM,MAAM,KAAK,MAAM,OAAO,GAAG;AACjC,YAAI,YAAY;AACZ,eAAK,OAAO,QAAQ;AACxB,eAAO;AAAA,MACX;AAEI,eAAO;AAAA,IACf,OACK;AACD,UAAI,QAAQ,KAAK,OAAO,MAAM,KAAK,GAAG,EAAE,MAAM,OAAO;AACrD,UAAI,SAAS,MAAM,QAAQ;AACvB,eAAO;AACX,UAAI,SAAS,YAAY;AACrB,aAAK,OAAO,MAAM,CAAC,EAAE;AACzB,aAAO;AAAA,IACX;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AAAE,WAAO,KAAK,OAAO,MAAM,KAAK,OAAO,KAAK,GAAG;AAAA,EAAG;AAChE;AAEA,SAAS,WAAW,MAAM;AACtB,SAAO;AAAA,IACH,MAAM,KAAK,QAAQ;AAAA,IACnB,OAAO,KAAK;AAAA,IACZ,WAAW,KAAK,cAAc,MAAM;AAAA,IAAE;AAAA,IACtC,YAAY,KAAK,eAAe,MAAM;AAAA,IACtC,WAAW,KAAK,aAAa;AAAA,IAC7B,QAAQ,KAAK,WAAW,MAAM;AAAA,IAC9B,cAAc,KAAK,gBAAgB,CAAC;AAAA,IACpC,YAAY,KAAK,cAAc;AAAA,IAC/B,aAAa,KAAK,gBAAgB;AAAA,EACtC;AACJ;AACA,SAAS,iBAAiB,OAAO;AAC7B,MAAI,OAAO,SAAS;AAChB,WAAO;AACX,MAAI,WAAW,CAAC;AAChB,WAAS,QAAQ,OAAO;AACpB,QAAI,MAAM,MAAM,IAAI;AACpB,aAAS,IAAI,IAAK,eAAe,QAAQ,IAAI,MAAM,IAAI;AAAA,EAC3D;AACA,SAAO;AACX;AACA,IAAM,eAA4B,oBAAI,QAAQ;AAK9C,IAAM,iBAAN,MAAM,wBAAuB,SAAS;AAAA,EAClC,YAAY,QAAQ;AAChB,QAAI,OAAO,oBAAoB,OAAO,YAAY;AAClD,QAAI,IAAI,WAAW,MAAM,GAAG;AAC5B,QAAI,OAAO,IAAI,cAAc,OAAO;AAAA,MAChC,YAAY,OAAO,WAAW,QAAQ;AAClC,eAAO,IAAI,MAAM,MAAM,OAAO,WAAW,MAAM;AAAA,MACnD;AAAA,IACJ;AACA,UAAM,MAAM,MAAM,CAAC,GAAG,OAAO,IAAI;AACjC,SAAK,UAAU,MAAM,MAAM,IAAI;AAC/B,WAAO;AACP,SAAK,eAAe;AACpB,SAAK,aAAa,IAAI,SAAS,EAAE,SAAS,KAAK,CAAC;AAChD,SAAK,aAAa,OAAO,aAAa,IAAI,WAAW,EAAE,UAAU,IAAI;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,OAAO,MAAM;AAAE,WAAO,IAAI,gBAAe,IAAI;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAIvD,UAAU,IAAI;AACV,QAAI,OAAO;AACX,QAAI,EAAE,oBAAoB,IAAI,GAAG;AACjC,QAAI,qBAAqB;AACrB,aAAO,aAAa,IAAI,GAAG,KAAK;AAChC,UAAI,QAAQ,QAAQ,OAAO,GAAG,MAAM;AAChC,eAAO;AAAA,IACf;AACA,QAAI,QAAQ,UAAU,MAAM,GAAG,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG,KAAK,MAAM,SAAS,QAAQ,SAAS,SAAS,OAAO,GAAG,GAAG,GAAG,UAAU;AACnI,QAAI,OAAO;AACP,cAAQ,MAAM;AACd,iBAAW,MAAM,MAAM;AAAA,IAC3B,OACK;AACD,cAAQ,KAAK,aAAa,WAAW,GAAG,IAAI;AAC5C,iBAAW,GAAG,KAAK;AAAA,IACvB;AACA,QAAI,GAAG,MAAM,WAAW;AACpB,aAAO;AACX,WAAO,WAAW,GAAG,KAAK;AACtB,UAAIC,QAAO,GAAG,MAAM,IAAI,OAAO,QAAQ,GAAG,MAAM,KAAK,IAAI,GAAG,KAAKA,MAAK,EAAE;AACxE,UAAIA,MAAK,QAAQ;AACb,YAAI,cAAc,sBAAsB,oBAAoBA,MAAK,IAAI,IAAI;AACzE,YAAI,SAAS,IAAI,aAAaA,MAAK,MAAM,GAAG,MAAM,SAAS,GAAG,MAAM,cAAc,IAAI,SAAY,WAAW;AAC7G,eAAO,OAAO,MAAM,MAAMA,MAAK;AAC3B,oBAAU,KAAK,aAAa,OAAO,QAAQ,KAAK;AAAA,MACxD,OACK;AACD,aAAK,aAAa,UAAU,OAAO,GAAG,IAAI;AAAA,MAC9C;AACA,UAAI,OAAO,GAAG;AACV;AACJ,iBAAWA,MAAK,KAAK;AAAA,IACzB;AACA,QAAI,OAAO,GAAG,OAAO,GAAG,GAAG;AAC3B,QAAI,uBAAuB,QAAQ;AAC/B,mBAAa,IAAI,GAAG,OAAO,KAAK,IAAI;AACxC,WAAO,KAAK,aAAa,OAAO,OAAO,WAAW,KAAK,KAAK,IAAI,EAAE,CAAC,GAAG,EAAE;AAAA,EAC5E;AAAA,EACA,IAAI,gBAAgB;AAAE,WAAO;AAAA,EAAO;AACxC;AACA,SAAS,UAAU,MAAM,MAAM,KAAK,UAAU,QAAQ;AAClD,MAAI,QAAQ,OAAO,YAAY,MAAM,KAAK,UAAU,UAAU,KAAK,KAAK,KAAK,UAAU;AACvF,MAAI;AACA,WAAO,EAAE,OAAO,KAAK,aAAa,UAAU,KAAK,GAAG,KAAK,MAAM,KAAK,OAAO;AAC/E,WAAS,IAAI,KAAK,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AAChD,QAAI,QAAQ,KAAK,SAAS,CAAC,GAAG,MAAM,MAAM,KAAK,UAAU,CAAC;AAC1D,QAAI,QAAQ,iBAAiB,QAAQ,MAAM,UAAU,UAAU,MAAM,OAAO,KAAK,UAAU,MAAM;AACjG,QAAI;AACA,aAAO;AAAA,EACf;AACA,SAAO;AACX;AACA,SAAS,QAAQ,MAAM,MAAM,MAAM,IAAI,QAAQ;AAC3C,MAAI,UAAU,QAAQ,KAAK,MAAM,KAAK;AAClC,WAAO;AACX,MAAI,CAAC,UAAU,QAAQ,KAAK,KAAK,QAAQ,KAAK;AAC1C,aAAS;AACb,WAAS,IAAI,KAAK,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AAChD,QAAI,MAAM,KAAK,UAAU,CAAC,GAAG,QAAQ,KAAK,SAAS,CAAC,GAAG;AACvD,QAAI,MAAM,MAAM,iBAAiB,MAAM;AACnC,UAAI,EAAE,QAAQ,QAAQ,MAAM,OAAO,OAAO,KAAK,KAAK,KAAK,MAAM;AAC3D;AACJ,aAAO,CAAC,SAAS,QACX,IAAI,KAAK,KAAK,MAAM,KAAK,SAAS,MAAM,GAAG,CAAC,EAAE,OAAO,KAAK,GAAG,KAAK,UAAU,MAAM,GAAG,IAAI,CAAC,GAAG,MAAM,MAAM,MAAM;AAAA,IACzH;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,qBAAqB,MAAM,WAAW,UAAU,QAAQ,aAAa;AAC1E,WAAS,KAAK,WAAW;AACrB,QAAI,OAAO,EAAE,QAAQ,EAAE,YAAY,KAAK,IAAI,KAAK,EAAE,MAAM,EAAE,UAAU,KAAK;AAC1E,QAAI,QAAQ,QAAQ,YAAY,KAAK,YAAY,UAAU,MAAM,EAAE,MAAM,IAAI,EAAE,QAAQ,UAAU,EAAE,GAAG;AACtG,QAAI,SAAS,MAAM,OAAO,WAAW,OAAO,QAAQ,MAAM,EAAE,MAAM,WAAW,EAAE,QAAQ,MAAM,MAAM,EAAE,QAAQ,KAAK;AAC9G,aAAO,EAAE,OAAO,MAAM,OAAO,KAAK;AAAA,EAC1C;AACA,SAAO,EAAE,OAAO,KAAK,aAAa,WAAW,cAAc,cAAc,WAAW,IAAI,CAAC,GAAG,MAAM,KAAK,MAAM;AACjH;AACA,IAAM,QAAN,MAAY;AAAA,EACR,YAAY,MAAM,OAAO,WAAW,QAAQ;AACxC,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,SAAS,CAAC;AACf,SAAK,WAAW,CAAC;AACjB,SAAK,QAAQ,CAAC;AACd,SAAK,cAAc;AACnB,SAAK,aAAa;AAClB,SAAK,KAAK,OAAO,OAAO,SAAS,CAAC,EAAE;AACpC,QAAI,UAAU,aAAa,IAAI,GAAG,OAAO,OAAO,CAAC,EAAE;AACnD,QAAI,EAAE,OAAO,KAAK,IAAI,qBAAqB,MAAM,WAAW,MAAM,KAAK,IAAI,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,KAAK;AAC1I,SAAK,QAAQ;AACb,SAAK,YAAY,KAAK,aAAa,OAAO,KAAK;AAC/C,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC3C,WAAK,OAAO,KAAK,KAAK,SAAS,CAAC,CAAC;AACjC,WAAK,SAAS,KAAK,KAAK,UAAU,CAAC,CAAC;AAAA,IACxC;AACA,QAAI,WAAW,KAAK,YAAY,QAAQ,SAAS,OAAO,OACpD,OAAO,KAAK,OAAK,EAAE,QAAQ,QAAQ,SAAS,QAAQ,EAAE,MAAM,QAAQ,SAAS,IAAI,GAAG;AACpF,WAAK,QAAQ,KAAK,KAAK,aAAa,WAAW,cAAc,QAAQ,KAAK,CAAC;AAC3E,cAAQ,gBAAgB,KAAK,WAAW,QAAQ,SAAS,IAAI;AAC7D,WAAK,YAAY,QAAQ,SAAS;AAAA,IACtC;AACA,SAAK,eAAe;AAAA,EACxB;AAAA,EACA,UAAU;AACN,QAAI,UAAU,aAAa,IAAI;AAC/B,QAAI,WAAW,KAAK,aAAa,OAAO,KAAK,KAAK,KAAK,IAAI,KAAK,IAAI,KAAK,SAAS;AAClF,QAAI,MAAM,KAAK;AAAA,MAAI;AAAA,MAAU,KAAK,aAAa;AAAA;AAAA,IAAsB;AACrE,QAAI;AACA,YAAM,KAAK,IAAI,KAAK,QAAQ,SAAS,EAAE;AAC3C,WAAO,KAAK,YAAY;AACpB,WAAK,UAAU,OAAO;AAC1B,QAAI,KAAK,aAAa,KAAK;AACvB,WAAK,YAAY;AACrB,QAAI,KAAK,aAAa;AAClB,aAAO,KAAK,OAAO;AACvB,QAAI,WAAW,KAAK,aAAa,QAAQ,SAAS,IAAI;AAClD,cAAQ,gBAAgB,KAAK,WAAW,QAAQ;AAChD,aAAO,KAAK,OAAO;AAAA,IACvB;AACA,WAAO;AAAA,EACX;AAAA,EACA,OAAO,KAAK;AACR,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,UAAU,KAAK;AACX,QAAI,QAAQ,KAAK,MAAM,MAAM,GAAG;AAChC,QAAI,CAAC,KAAK,MAAM,YAAY;AACxB,UAAI,MAAM,MAAM,QAAQ,IAAI;AAC5B,UAAI,MAAM;AACN,gBAAQ,MAAM,MAAM,GAAG,GAAG;AAAA,IAClC,WACS,SAAS,MAAM;AACpB,cAAQ;AAAA,IACZ;AACA,WAAO,MAAM,MAAM,UAAU,KAAK,KAAK,QAAQ,MAAM,MAAM,GAAG,KAAK,KAAK,GAAG;AAAA,EAC/E;AAAA,EACA,WAAW;AACP,QAAI,OAAO,KAAK,WAAW,OAAO,KAAK,UAAU,IAAI,GAAG,MAAM,OAAO,KAAK;AAC1E,aAAS,QAAQ,KAAK,gBAAc;AAChC,UAAI,WAAW,KAAK,OAAO,KAAK,EAAE;AAClC,UAAI,YAAY;AACZ;AACJ,aAAO,KAAK,MAAM,GAAG,YAAY,MAAM,KAAK,OAAO;AACnD;AACA,UAAI,SAAS,KAAK,OAAO;AACrB;AACJ,UAAI,aAAa,KAAK,OAAO,KAAK,EAAE;AACpC,UAAI,QAAQ,KAAK,UAAU,UAAU;AACrC,cAAQ;AACR,YAAM,aAAa,MAAM;AAAA,IAC7B;AACA,WAAO,EAAE,MAAM,IAAI;AAAA,EACvB;AAAA,EACA,WAAW,KAAK,QAAQ,MAAM;AAC1B,eAAS;AACL,UAAI,MAAM,KAAK,OAAO,KAAK,UAAU,EAAE,IAAI,SAAS,MAAM;AAC1D,UAAI,OAAO,IAAI,MAAM,SAAS,OAAO;AACjC;AACJ,UAAI,QAAQ,KAAK,OAAO,EAAE,KAAK,UAAU,EAAE;AAC3C,gBAAU,QAAQ;AAAA,IACtB;AACA,WAAO;AAAA,EACX;AAAA,EACA,iBAAiB;AACb,WAAO,KAAK,OAAO,KAAK,UAAU,EAAE,KAAK,KAAK;AAC1C,WAAK;AAAA,EACb;AAAA,EACA,UAAU,IAAI,MAAM,IAAI,QAAQ;AAC5B,QAAI,OAAO;AACX,QAAI,KAAK,OAAO,SAAS,GAAG;AACxB,eAAS,KAAK,WAAW,MAAM,QAAQ,CAAC;AACxC,cAAQ;AACR,UAAI,OAAO,KAAK,MAAM;AACtB,eAAS,KAAK,WAAW,IAAI,QAAQ,EAAE;AACvC,YAAM;AACN,cAAQ,KAAK,MAAM,SAAS;AAAA,IAChC;AACA,QAAI,OAAO,KAAK,MAAM,SAAS;AAC/B,QAAI,KAAK,KAAK,aAAa,eAAe,QAAQ,KAAK,QAAQ,KAC3D,KAAK,MAAM,IAAI,KAAK,MAAM,KAAK,MAAM,OAAO,CAAC,KAAK;AAClD,WAAK,MAAM,OAAO,CAAC,IAAI;AAAA;AAEvB,WAAK,MAAM,KAAK,IAAI,MAAM,IAAI,IAAI;AACtC,WAAO;AAAA,EACX;AAAA,EACA,UAAU,SAAS;AACf,QAAI,EAAE,MAAM,IAAI,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,EAAE,aAAa,IAAI,KAAK;AACzE,QAAI,SAAS,IAAI,aAAa,MAAM,UAAU,QAAQ,MAAM,UAAU,GAAG,UAAU,cAAc,QAAQ,KAAK,IAAI,CAAC;AACnH,QAAI,OAAO,IAAI,GAAG;AACd,mBAAa,UAAU,KAAK,OAAO,OAAO,UAAU;AAAA,IACxD,OACK;AACD,aAAO,CAAC,OAAO,IAAI,GAAG;AAClB,YAAI,QAAQ,UAAU,aAAa,OAAO,QAAQ,KAAK,KAAK;AAC5D,YAAI;AACA,mBAAS,KAAK,UAAU,KAAK,KAAK,WAAW,QAAQ,KAAK,GAAG,KAAK,YAAY,OAAO,OAAO,KAAK,YAAY,OAAO,KAAK,MAAM;AACnI,YAAI,OAAO,QAAQ;AACf;AAAA,MACR;AAAA,IACJ;AACA,SAAK,YAAY;AACjB,SAAK,eAAe;AACpB,QAAI,KAAK,YAAY,KAAK;AACtB,WAAK;AAAA,EACb;AAAA,EACA,cAAc;AACV,QAAI,OAAO,KAAK,MAAM;AAAA,MAClB,QAAQ,KAAK;AAAA,MACb,OAAO,KAAK;AAAA,MACZ,QAAQ,KAAK,YAAY,KAAK;AAAA,MAC9B;AAAA,MACA,OAAO;AAAA,MACP,iBAAiB;AAAA,MACjB,QAAQ,KAAK;AAAA,IACjB,CAAC;AACD,WAAO,IAAI,KAAK,KAAK,MAAM,KAAK,UAAU,KAAK,WAAW,KAAK,QAAQ,CAAC,CAAC,KAAK,KAAK,YAAY,KAAK,KAAK,aAAa,UAAU,KAAK,KAAK,CAAC,CAAC,CAAC;AAC7I,SAAK,OAAO,KAAK,IAAI;AACrB,SAAK,SAAS,KAAK,KAAK,aAAa,KAAK,OAAO,CAAC,EAAE,IAAI;AACxD,SAAK,QAAQ,CAAC;AACd,SAAK,cAAc;AACnB,SAAK,aAAa,KAAK;AAAA,EAC3B;AAAA,EACA,SAAS;AACL,WAAO,IAAI,KAAK,KAAK,KAAK,SAAS,KAAK,QAAQ,KAAK,UAAU,KAAK,YAAY,KAAK,OAAO,CAAC,EAAE,IAAI,EAAE,QAAQ;AAAA,EACjH;AACJ;AACA,SAAS,UAAU,OAAO,QAAQ,OAAO;AACrC,SAAO,QAAQ,OAAO;AACtB,WAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AACzB,QAAI,SAAS,MAAM,QAAQ,KAAK;AAChC,QAAI,OAAO,MAAM,OAAO;AACpB,aAAO;AAAA,EACf;AACA,QAAM,IAAI,MAAM,yCAAyC;AAC7D;AACA,IAAM,WAAwB,uBAAO,OAAO,IAAI;AAChD,IAAM,YAAY,CAAC,SAAS,IAAI;AAChC,IAAM,UAAuB,IAAI,QAAQ,SAAS;AAClD,IAAM,SAAS,CAAC;AAEhB,IAAM,QAAqB,uBAAO,OAAO,IAAI;AAC7C,IAAM,eAA4B,uBAAO,OAAO,IAAI;AACpD,SAAS,CAAC,YAAYC,KAAI,KAAK;AAAA,EAC3B,CAAC,YAAY,cAAc;AAAA,EAC3B,CAAC,cAAc,sBAAsB;AAAA,EACrC,CAAC,YAAY,gBAAgB;AAAA,EAC7B,CAAC,OAAO,yBAAyB;AAAA,EACjC,CAAC,OAAO,SAAS;AAAA,EACjB,CAAC,aAAa,eAAe;AAAA,EAC7B,CAAC,QAAQ,UAAU;AAAA,EACnB,CAAC,WAAW,uBAAuB;AAAA,EACnC,CAAC,aAAa,UAAU;AAAA,EACxB,CAAC,SAAS,SAAS;AAAA,EACnB,CAAC,UAAU,SAAS;AAAA,EACpB,CAAC,YAAY,cAAc;AAC/B;AACI,eAAa,UAAU,IAAiB,gBAAgB,UAAUA,KAAI;AAC1E,IAAM,aAAN,MAAiB;AAAA,EACb,YAAY,OAAO;AACf,SAAK,QAAQ;AACb,SAAK,QAAQ,OAAO,OAAO,uBAAO,OAAO,IAAI,GAAG,YAAY;AAAA,EAChE;AAAA,EACA,QAAQ,KAAK;AACT,WAAO,CAAC,MAAM,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,MAAM,GAAG,IAAI,gBAAgB,KAAK,OAAO,GAAG;AAAA,EAC3F;AACJ;AACA,IAAM,oBAAiC,IAAI,WAAW,QAAQ;AAC9D,SAAS,YAAY,MAAM,KAAK;AAC5B,MAAI,OAAO,QAAQ,IAAI,IAAI;AACvB;AACJ,SAAO,KAAK,IAAI;AAChB,UAAQ,KAAK,GAAG;AACpB;AACA,SAAS,gBAAgB,OAAO,QAAQ;AACpC,MAAI,SAAS,CAAC;AACd,WAASA,SAAQ,OAAO,MAAM,GAAG,GAAG;AAChC,QAAI,QAAQ,CAAC;AACb,aAAS,QAAQA,MAAK,MAAM,GAAG,GAAG;AAC9B,UAAI,QAAS,MAAM,IAAI,KAAK,KAAK,IAAI;AACrC,UAAI,CAAC,OAAO;AACR,oBAAY,MAAM,4BAA4B,IAAI,EAAE;AAAA,MACxD,WACS,OAAO,SAAS,YAAY;AACjC,YAAI,CAAC,MAAM;AACP,sBAAY,MAAM,YAAY,IAAI,uBAAuB;AAAA;AAEzD,kBAAQ,MAAM,IAAI,KAAK;AAAA,MAC/B,OACK;AACD,YAAI,MAAM;AACN,sBAAY,MAAM,OAAO,IAAI,mBAAmB;AAAA;AAEhD,kBAAQ,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK;AAAA,MACrD;AAAA,IACJ;AACA,aAAS,OAAO;AACZ,aAAO,KAAK,GAAG;AAAA,EACvB;AACA,MAAI,CAAC,OAAO;AACR,WAAO;AACX,MAAIA,QAAO,OAAO,QAAQ,MAAM,GAAG,GAAG,MAAMA,QAAO,MAAM,OAAO,IAAI,CAAAC,OAAKA,GAAE,EAAE;AAC7E,MAAI,QAAQ,MAAM,GAAG;AACrB,MAAI;AACA,WAAO,MAAM;AACjB,MAAI,OAAO,MAAM,GAAG,IAAI,SAAS,OAAO;AAAA,IACpC,IAAI,UAAU;AAAA,IACd,MAAAD;AAAA,IACA,OAAO,CAAC,UAAU,EAAE,CAACA,KAAI,GAAG,OAAO,CAAC,CAAC;AAAA,EACzC,CAAC;AACD,YAAU,KAAK,IAAI;AACnB,SAAO,KAAK;AAChB;AACA,SAAS,MAAM,MAAM,MAAM;AACvB,MAAI,OAAO,SAAS,OAAO,EAAE,IAAI,UAAU,QAAQ,MAAM,YAAY,OAAO;AAAA,IACpE,iBAAiB,IAAI,MAAM,IAAI;AAAA,IAC/B,eAAe,IAAI,MAAM,QAAM,KAAK,UAAU,EAAE,CAAC;AAAA,EACrD,GAAG,KAAK,KAAK,CAAC;AAClB,YAAU,KAAK,IAAI;AACnB,SAAO;AACX;AAEA,SAAS,aAAa,MAAM;AACxB,SAAO,KAAK,UAAU,QAAQ,yDAAyD,KAAK,IAAI;AACpG;AACA,SAAS,WAAW,MAAM;AACtB,WAAS,IAAI,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,EAAE;AAChC,QAAI,aAAa,EAAE,KAAK;AACpB,aAAO;AACf,SAAO;AACX;AACA,SAAS,cAAc,QAAQ;AAC3B,MAAI,QAAQ;AACZ,SAAO,YAAY,CAAC,IAAI,IAAI,IAAI,IAAI,QAAQ;AACxC,QAAI,CAAC,SAAS,WAAW,GAAG;AACxB,cAAQ;AAAA,EAChB,CAAC;AACD,SAAO;AACX;AACA,IAAM,gBAA6B,MAAM,OAAO,EAAE,SAAS,YAAU,OAAO,KAAK,OAAK,CAAC,EAAE,CAAC;AAa1F,IAAM,eAA4B,WAAW,UAAU,MAAM;AAAA,EACzD,YAAY,MAAM;AACd,SAAK,SAAS,KAAK,MAAM,MAAM,aAAa,KACxC,KAAK,iBAAiB,UAAU,OAChC,KAAK,MAAM,MAAM,WAAW,oBAAoB;AACpD,SAAK,SAAS,CAAC,KAAK,UAAU,WAAW,KAAK,MAAM,GAAG;AACvD,SAAK,OAAO,WAAW,KAAK,KAAK;AACjC,SAAK,cAAc,KAAK,UAAU,KAAK,SAAS,UAAU,MAAM,KAAK,MAAM,KAAK,MAAM,IAAI,WAAW;AAAA,EACzG;AAAA,EACA,OAAO,QAAQ;AACX,QAAI,SAAS,OAAO,MAAM,MAAM,aAAa,KACzC,OAAO,KAAK,iBAAiB,UAAU,OACvC,OAAO,MAAM,MAAM,WAAW,oBAAoB;AACtD,QAAI,CAAC,UAAU,CAAC,KAAK,UAAU,cAAc,OAAO,OAAO;AACvD,WAAK,SAAS;AAClB,QAAI,CAAC,UAAU,CAAC,KAAK;AACjB;AACJ,QAAI,OAAO,WAAW,OAAO,KAAK;AAClC,QAAI,UAAU,KAAK,UAAU,QAAQ,KAAK,QAAQ,OAAO,cAAc,OAAO,iBAAiB;AAC3F,WAAK,OAAO;AACZ,WAAK,SAAS;AACd,WAAK,cAAc,UAAU,OAAO,MAAM,MAAM,MAAM;AAAA,IAC1D;AAAA,EACJ;AACJ,GAAG;AAAA,EACC,SAAS,YAAU;AACf,aAAS,OAAO,MAAM;AAClB,UAAIE,KAAI;AACR,cAAQ,MAAMA,MAAK,KAAK,OAAO,MAAM,OAAO,QAAQA,QAAO,SAAS,SAASA,IAAG,iBAAiB,QAAQ,OAAO,SAAS,KAAK,WAAW;AAAA,IAC7I;AACA,WAAO;AAAA,MAAC,WAAW,iBAAiB,GAAG,MAAM;AAAA,MACzC,KAAK,OAAO,WAAW,mBAAmB,GAAG,MAAM,CAAC;AAAA,IAAC;AAAA,EAC7D;AACJ,CAAC;AACD,SAAS,UAAU,MAAM,MAAM,QAAQ;AACnC,MAAI,OAAO,IAAI,gBAAgB;AAC/B,MAAI,SAAS,KAAK;AAClB,MAAI,CAAC;AACD,aAAS,aAAa,QAAQ,KAAK,MAAM,GAAG;AAChD,WAAS,EAAE,MAAM,GAAG,KAAK,QAAQ;AAC7B,SAAK,QAAQ;AAAA,MACT,OAAO,UAAQ;AACX,YAAI,MAAM,KAAK,KAAK,KAAK,SAAS,OAAO;AACzC,YAAI;AACA,eAAK,IAAI,KAAK,MAAM,KAAK,IAAI,MAAM,GAAG,CAAC;AAAA,MAC/C;AAAA,MACA;AAAA,MAAM;AAAA,IACV,CAAC;AAAA,EACL;AACA,SAAO,KAAK,OAAO;AACvB;AACA,SAAS,aAAa,QAAQ,KAAK;AAC/B,MAAI,MAAM,IAAI,KAAK,GAAG,MAAM,GAAG,SAAS,CAAC,GAAG,OAAO;AACnD,WAAS,EAAE,MAAM,GAAG,KAAK,QAAQ;AAC7B,QAAI,QAAQ,KAAK,KAAK,MAAM;AACxB,aAAO,KAAK;AACZ,UAAI,QAAQ;AACR;AAAA,IACR;AACA,QAAI,MAAM,IAAI,MAAM,SAAS,MAAM;AAC/B,UAAI,KAAK,QAAQ,MAAM,IAAI,MAAM,OAAO;AACxC,YAAM;AAAA,IACV;AACA,eAAS;AACL,UAAI,QAAQ,KAAK,MAAM,MAAM,IAAI,MAAM;AACvC,UAAI,CAAC,IAAI,aAAa,aAAa,IAAI,KAAK,GAAG;AAC3C,YAAI,QAAQ,KAAK,KAAK,QAAQ;AAC1B,eAAK,KAAK,KAAK,IAAI,IAAI,GAAG;AAAA;AAE1B,iBAAO,KAAK,OAAO,EAAE,MAAM,OAAO,IAAI,KAAK,IAAI,IAAI,GAAG,EAAE,CAAC;AAAA,MACjE;AACA,UAAI,OAAO;AACP;AACJ,YAAM;AACN,UAAI,KAAK;AAAA,IACb;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAM,QAAQ;AAAA,EACV,KAAkB,WAAW,KAAK,EAAE,OAAO,UAAU,WAAW,MAAM,YAAY,EAAE,KAAK,MAAM,GAAG,aAAa,UAAU,IAAI,CAAC;AAAA,EAC9H,KAAkB,WAAW,KAAK,EAAE,OAAO,UAAU,WAAW,MAAM,YAAY,EAAE,KAAK,MAAM,GAAG,aAAa,UAAU,IAAI,CAAC;AAAA,EAC9H,MAAmB,WAAW,KAAK,EAAE,OAAO,UAAU,WAAW,MAAM,YAAY,EAAE,KAAK,OAAO,GAAG,aAAa,KAAK,CAAC;AAC3H;", "names": ["name", "IterMode", "_a", "nodeSet", "children", "positions", "node", "buffer", "data", "contextHash", "length", "lookAhead", "nodeSize", "from", "to", "string", "done", "inner", "r", "pos", "name", "t", "tags", "<PERSON><PERSON><PERSON><PERSON>", "name", "name", "data", "_a", "language", "name", "head", "line", "_a", "from", "to", "_a", "from", "to", "bracket", "pos", "string", "indentUnit", "_a", "line", "name", "t", "_a"]}