<template>
  <a-drawer
    :width="540"
    :visible="visible"
    unmountOnClose
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <template #title>
      <div class="flex grow items-center">
        <div class="flex items-center">
          <img
            v-if="typeof toolCollection.icon === 'string'"
            :src="toolCollection.icon"
            style="width: 18px; height: 18px; display: inline-block"
            alt=""
          />
          <div v-else :style="{ backgroundColor: toolCollection.icon ? toolCollection.icon.background : '#ffffff' }">
            {{ toolCollection.icon?.content }}
          </div>

          <div class="flex h-4 items-center space-x-0.5 ml-4">
            <div class="system-xs-regular shrink-0 text-text-tertiary">{{ toolCollection.author }}</div>
            <div class="system-xs-regular shrink-0 text-text-quaternary">/</div>
            <div class="system-xs-regular truncate text-text-tertiary">
              {{ toolCollection.name.split('/').pop() || '' }}
            </div>
          </div>
        </div>
      </div>
    </template>
    <div>
      <div class="system-md-semibold mt-1 text-text-primary">{{ renderName(toolCollection.label) }}</div>
      <div class="h-10 line-clamp-2 system-sm-regular text-text-tertiary mt-3">
        {{ renderName(toolCollection.description) }}
      </div>

      <div class="mt-6">
        <a-tabs :active-key="activeKey" :type="'capsule'" :size="'small'" @change="handleChangeTab">
          <a-tab-pane key="info" title="参数">
            <InfoTab :infoSchemas="infoSchemas" />
          </a-tab-pane>
          <a-tab-pane v-if="hasSetting" key="setting" title="设置">
            <SettingTab ref="settingTabRef" :settingSchemas="settingSchemas" :formValues="formValues" />
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </a-drawer>
</template>
<script setup lang="ts">
import { addDefaultValue, renderName, toolParametersToFormSchemas } from '@/views/app/workflow/utils/configuration'
import { CollectionType } from '@/views/app/workflow/types/variable'
import InfoTab from '@/views/app/configuration/agent-tools/InfoTab.vue'
import SettingTab from '@/views/app/configuration/agent-tools/SettingTab.vue'
import {
  fetchBuiltInToolList,
  fetchCustomToolList,
  fetchModelToolList,
  fetchWorkflowToolList
} from '@/apis/workflow/configuration'

// toolCollection：collection属性。setting：tool_parameters属性。toolName：tool_name属性。
const props = defineProps(['toolCollection', 'toolName', 'setting'])
const emits = defineEmits(['closeDrawer'])
const visible = ref(true)
// 工具下子工具的list
const toolList = ref([])
const formSchemas = ref([])
const infoSchemas = ref([]) // info的formSchema
const settingSchemas = ref([]) // setting的formSchema
// 是否有设置tab
const hasSetting = computed(() => {
  return settingSchemas.value.length > 0
})
// 表单的值（form）
const formValues = ref({})
const settingTabRef = ref()
const activeKey = ref('info')
const getToolList = async () => {
  let serviceName = fetchModelToolList
  let params: any = ''
  if (props.toolCollection.type === CollectionType.model) {
    serviceName = fetchModelToolList
    params = { provider: props.toolCollection.name }
  } else if (props.toolCollection.type === CollectionType.builtIn) {
    params = props.toolCollection.name
    serviceName = fetchBuiltInToolList
  } else if (props.toolCollection.type === CollectionType.workflow) {
    params = props.toolCollection.id
    serviceName = fetchWorkflowToolList
  } else {
    params = { provider: props.toolCollection.name }
    serviceName = fetchCustomToolList
  }
  const res: any[] = await serviceName(params)
  toolList.value = res || []

  const currentTool = toolList.value.find((tool) => tool.name === props.toolName)
  if (currentTool) {
    // formSchema 处理（增加和重命名字段）
    const formSchemasTemp = toolParametersToFormSchemas(currentTool.parameters)
    formSchemas.value = formSchemasTemp
    infoSchemas.value = formSchemas.value.filter((item) => item.form === 'llm')
    settingSchemas.value = formSchemas.value.filter((item) => item.form !== 'llm')
    if (settingSchemas.value.length > 0) {
      activeKey.value = 'setting'
    }
    // formValues的处理
    const forms = addDefaultValue(props.setting, formSchemasTemp)
    formValues.value = forms
  }
}
// drawer确定按钮
const handleOk = () => {
  const formValues = settingTabRef.value && settingTabRef.value.formValues
  emits('closeDrawer', 'ok', formValues)
}
// drawer 取消 按钮
const handleCancel = () => {
  emits('closeDrawer', 'cancel')
}
// 切换tab
const handleChangeTab = (key) => {
  activeKey.value = key
}
onMounted(() => {
  getToolList()
})
</script>

<style scoped lang="scss">

</style>
