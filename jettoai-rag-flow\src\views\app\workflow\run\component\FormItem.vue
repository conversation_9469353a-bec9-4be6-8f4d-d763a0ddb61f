<template>
  <a-form ref="formRef" :model="form" layout="vertical">
    <template v-if="variablesData.length">
      <a-form-item
        v-for="item in variablesData"
        :key="item.variable"
        :label="item.required ? item.label + `( 必填 )` : item.label + `( 选填 )`"
        :field="item.variable"
      >
        <a-input
          v-if="item.type === 'text-input'"
          v-model="form[item.variable]"
          :default-value="inputs[item.variable]"
          :placeholder="`${item.label}${!item.required ? `可选` : ''}`"
          :max-length="item.max_length || 48"
          @change="
            (value) => {
              handleInputsChange({ ...inputs, [item.variable]: value })
            }
          "
        />

        <a-select
          v-if="item.type === 'select'"
          v-model="form[item.variable]"
          :default-value="inputs[item.variable]"
          @change="
            (value) => {
              handleInputsChange({ ...inputs, [item.variable]: value })
            }
          "
        >
          <a-option v-for="ele in item.options || []" :key="ele" :value="ele">{{ ele }}</a-option>
        </a-select>

        <a-textarea
          v-if="item.type === 'paragraph'"
          v-model="form[item.variable]"
          :default-value="inputs[item.variable]"
          :placeholder="`${item.label}${!item.required ? `可选` : ''}`"
          @change="
            (value) => {
              handleInputsChange({ ...inputs, [item.variable]: value })
            }
          "
        />
        <a-input-number
          v-if="item.type === 'number'"
          v-model="form[item.variable]"
          :default-value="inputs[item.variable]"
          :placeholder="`${item.label}${!item.required ? `可选` : ''}`"
          @change="
            (value) => {
              handleInputsChange({ ...inputs, [item.variable]: value })
            }
          "
        />
        <AiFileUpload
          v-if="item.type === 'file'"
          v-model="form[item.variable]"
          :multiple="false"
          :inputsKey="item.variable"
          :type="item.type"
          :method="item?.allowed_file_upload_methods?.[0] || ''"
          @uploadFile="onUploadFile"
        />

        <AiFileUpload
          v-if="item.type === 'file-list'"
          v-model="form[item.variable]"
          :inputsKey="item.variable"
          :type="item.type"
          :method="item?.allowed_file_upload_methods?.[0] || ''"
          @uploadFile="onUploadFile"
        />
      </a-form-item>
    </template>
  </a-form>
</template>

<script setup lang="ts">
import { uploadFile } from '@/apis/workflow/share'
import type { FormInstance } from '@arco-design/web-vue'
import type { RunInputVariables } from '../type'
import { useVModel } from '@vueuse/core'

export type IRunOnceProps = {
  variablesData: RunInputVariables[]
  inputs: Record<string, any>
}

defineOptions({
  name: 'FormItem'
})

const emits = defineEmits(['onSend', 'onInputsChange', 'onVisionFilesChange', 'update:inputs'])

const form = ref({})
const formRef = ref<FormInstance>()
const reset = () => {
  try {
    formRef.value?.resetFields()
    form.value = {}
  } catch (e) {}
}
// const { variablesData, inputs } = defineProps<IRunOnceProps>()

const props = withDefaults(defineProps<IRunOnceProps>(), {
  variablesData: () => [],
  inputs: () => ({})
})

const inputs = useVModel(props, 'inputs', emits)

onMounted(() => {
  console.log(props.variablesData, '=========')
})

const onUploadFile = async (
  file: File,
  onProgress?: (percent: number) => void,
  key?: string,
  transfer_method?: string,
  callback?: (res) => void,
  type?: string
) => {
  const response = await uploadFile(file, (percent) => {
    onProgress && onProgress(percent)
  })
  console.log(inputs.value)
  const newInputs = {
    ...props.inputs
  }
  if (key) {
    if (type == 'file') {
      newInputs[key] = {
        type: 'document',
        transfer_method: transfer_method,
        url: '',
        upload_file_id: response.id || ''
      }
    } else {
      newInputs[key] = [
        {
          type: 'document',
          transfer_method: transfer_method,
          url: '',
          upload_file_id: response.id || ''
        }
      ]
    }
  }
  if (callback) {
    callback(response)
  }
  form.value = { ...form.value, ...newInputs }
  emits('update:inputs', form.value)
  emits('onInputsChange', newInputs)
}

const handleInputsChange = (newInputs: Record<string, any>) => {
  const newForm = { ...form.value, ...newInputs }
  emits('update:inputs', newForm)
  emits('onInputsChange', newInputs)
}
defineExpose({ reset })
</script>

<style scoped lang="scss"></style>
